<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 基础设置 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>基础设置</span>
            </div>
          </template>
          
          <el-form :model="basicSettings" label-width="120px">
            <el-form-item label="默认货币">
              <el-select v-model="basicSettings.default_currency" style="width: 100%">
                <el-option label="人民币 (CNY)" value="CNY" />
                <el-option label="美元 (USD)" value="USD" />
                <el-option label="欧元 (EUR)" value="EUR" />
                <el-option label="日元 (JPY)" value="JPY" />
                <el-option label="英镑 (GBP)" value="GBP" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="默认库存状态">
              <el-select v-model="basicSettings.default_stock_status" style="width: 100%">
                <el-option label="有库存" value="in_stock" />
                <el-option label="无库存" value="out_of_stock" />
                <el-option label="已停产" value="discontinued" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="新商品默认状态">
              <el-switch
                v-model="basicSettings.default_active_status"
                active-text="上架"
                inactive-text="下架"
              />
            </el-form-item>
            
            <el-form-item label="库存预警数量">
              <el-input-number 
                v-model="basicSettings.stock_warning_threshold" 
                :min="0" 
                style="width: 100%" 
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 显示设置 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>显示设置</span>
            </div>
          </template>
          
          <el-form :model="displaySettings" label-width="120px">
            <el-form-item label="每页显示数量">
              <el-select v-model="displaySettings.items_per_page" style="width: 100%">
                <el-option label="10条" :value="10" />
                <el-option label="15条" :value="15" />
                <el-option label="20条" :value="20" />
                <el-option label="50条" :value="50" />
                <el-option label="100条" :value="100" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="默认排序字段">
              <el-select v-model="displaySettings.default_sort_field" style="width: 100%">
                <el-option label="创建时间" value="created_at" />
                <el-option label="更新时间" value="last_updated_at" />
                <el-option label="商品名称" value="name" />
                <el-option label="价格" value="price" />
                <el-option label="库存数量" value="stock_quantity" />
                <el-option label="关注数量" value="followers_count" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="默认排序方向">
              <el-radio-group v-model="displaySettings.default_sort_direction">
                <el-radio label="desc">降序</el-radio>
                <el-radio label="asc">升序</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="显示图片预览">
              <el-switch
                v-model="displaySettings.show_image_preview"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 通知设置 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>通知设置</span>
            </div>
          </template>
          
          <el-form :model="notificationSettings" label-width="120px">
            <el-form-item label="库存预警通知">
              <el-switch
                v-model="notificationSettings.enable_stock_warning"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
            
            <el-form-item label="新商品通知">
              <el-switch
                v-model="notificationSettings.enable_new_product_notification"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
            
            <el-form-item label="商品更新通知">
              <el-switch
                v-model="notificationSettings.enable_product_update_notification"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
            
            <el-form-item label="价格变动通知">
              <el-switch
                v-model="notificationSettings.enable_price_change_notification"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 数据管理 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>数据管理</span>
            </div>
          </template>
          
          <el-form label-width="120px">
            <el-form-item label="数据导出">
              <el-button type="primary" @click="handleExportData" :loading="exportLoading">
                导出商品数据
              </el-button>
            </el-form-item>
            
            <el-form-item label="数据导入">
              <el-upload
                class="upload-demo"
                :auto-upload="false"
                :on-change="handleFileUpload"
                :file-list="[]"
                accept=".xlsx,.xls,.csv"
              >
                <el-button type="success" :icon="Upload">选择文件</el-button>
              </el-upload>
            </el-form-item>
            
            <el-form-item label="数据清理">
              <el-button type="warning" @click="handleDataCleanup" :loading="cleanupLoading">
                清理无效数据
              </el-button>
            </el-form-item>
            
            <el-form-item label="缓存管理">
              <el-button type="info" @click="handleClearCache" :loading="cacheLoading">
                清理缓存
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <!-- 保存按钮 -->
    <div class="save-actions">
      <el-button type="primary" size="large" @click="handleSaveSettings" :loading="saveLoading">
        保存设置
      </el-button>
      <el-button size="large" @click="handleResetSettings">
        重置设置
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'

// 加载状态
const saveLoading = ref(false)
const exportLoading = ref(false)
const cleanupLoading = ref(false)
const cacheLoading = ref(false)

// 基础设置
const basicSettings = reactive({
  default_currency: 'CNY',
  default_stock_status: 'in_stock',
  default_active_status: true,
  stock_warning_threshold: 10
})

// 显示设置
const displaySettings = reactive({
  items_per_page: 15,
  default_sort_field: 'created_at',
  default_sort_direction: 'desc',
  show_image_preview: true
})

// 通知设置
const notificationSettings = reactive({
  enable_stock_warning: true,
  enable_new_product_notification: true,
  enable_product_update_notification: false,
  enable_price_change_notification: true
})

onMounted(() => {
  loadSettings()
})

const loadSettings = async () => {
  try {
    // 这里应该调用实际的API
    console.log('加载商品设置')
    // const response = await productApi.getSettings()
    // Object.assign(basicSettings, response.data.basic)
    // Object.assign(displaySettings, response.data.display)
    // Object.assign(notificationSettings, response.data.notification)
  } catch (error) {
    ElMessage.error('加载设置失败')
  }
}

const handleSaveSettings = async () => {
  saveLoading.value = true
  try {
    const settings = {
      basic: basicSettings,
      display: displaySettings,
      notification: notificationSettings
    }
    
    // 这里应该调用实际的API
    console.log('保存商品设置', settings)
    // await productApi.updateSettings(settings)
    
    ElMessage.success('设置保存成功')
  } catch (error) {
    ElMessage.error('设置保存失败')
  } finally {
    saveLoading.value = false
  }
}

const handleResetSettings = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置所有设置为默认值吗？',
      '重置确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 重置为默认值
    Object.assign(basicSettings, {
      default_currency: 'CNY',
      default_stock_status: 'in_stock',
      default_active_status: true,
      stock_warning_threshold: 10
    })
    
    Object.assign(displaySettings, {
      items_per_page: 15,
      default_sort_field: 'created_at',
      default_sort_direction: 'desc',
      show_image_preview: true
    })
    
    Object.assign(notificationSettings, {
      enable_stock_warning: true,
      enable_new_product_notification: true,
      enable_product_update_notification: false,
      enable_price_change_notification: true
    })
    
    ElMessage.success('设置已重置为默认值')
  } catch (error) {
    // 用户取消操作
  }
}

const handleExportData = async () => {
  exportLoading.value = true
  try {
    // 这里应该调用实际的API
    console.log('导出商品数据')
    // const response = await productApi.exportData()
    // 创建下载链接
    // const url = window.URL.createObjectURL(new Blob([response.data]))
    // const link = document.createElement('a')
    // link.href = url
    // link.download = 'products.xlsx'
    // link.click()
    
    ElMessage.success('数据导出成功')
  } catch (error) {
    ElMessage.error('数据导出失败')
  } finally {
    exportLoading.value = false
  }
}

const handleFileUpload = (file: any) => {
  console.log('选择文件', file.name)
  // 这里应该实现文件上传逻辑
  ElMessage.info('文件上传功能待实现')
}

const handleDataCleanup = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清理无效数据吗？此操作不可撤销。',
      '数据清理确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    cleanupLoading.value = true
    
    // 这里应该调用实际的API
    console.log('清理无效数据')
    // await productApi.cleanupData()
    
    ElMessage.success('数据清理完成')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('数据清理失败')
    }
  } finally {
    cleanupLoading.value = false
  }
}

const handleClearCache = async () => {
  cacheLoading.value = true
  try {
    // 这里应该调用实际的API
    console.log('清理缓存')
    // await productApi.clearCache()
    
    ElMessage.success('缓存清理完成')
  } catch (error) {
    ElMessage.error('缓存清理失败')
  } finally {
    cacheLoading.value = false
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.card-header {
  font-weight: 600;
}

.save-actions {
  margin-top: 30px;
  text-align: center;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

.upload-demo {
  width: 100%;
}
</style> 