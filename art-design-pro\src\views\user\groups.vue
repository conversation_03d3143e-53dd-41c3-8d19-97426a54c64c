<template>
  <div class="app-container">
    <!-- 头部操作栏 -->
    <div class="header-actions">
      <el-row :gutter="16" align="middle">
        <el-col :span="12">
          <h2 class="page-title">用户组管理</h2>
        </el-col>
        <el-col :span="12" class="text-right">
          <el-button type="primary" @click="handleAdd" :icon="Plus">
            添加用户组
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="组名称">
          <el-input v-model="searchForm.name" placeholder="请输入组名称" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
          <el-button @click="handleReset" :icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 用户组列表 -->
    <el-card class="list-card">
      <el-table :data="groupList" v-loading="loading" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="组名称" min-width="150" />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="member_count" label="成员数量" width="100" />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleToggleStatus(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)" :icon="Edit">
              编辑
            </el-button>
            <el-button type="info" size="small" @click="handleViewMembers(row)" :icon="User">
              成员
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)" :icon="Delete">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :small="false"
        :disabled="loading"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="pagination"
      />
    </el-card>

    <!-- 添加/编辑用户组弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogMode === 'add' ? '添加用户组' : '编辑用户组'"
      width="600px"
    >
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="80px">
        <el-form-item label="组名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入组名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入组描述"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          {{ dialogMode === 'add' ? '添加' : '更新' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete, Search, Refresh, User } from '@element-plus/icons-vue'

// 定义组件名称
defineOptions({
  name: 'UserGroups'
})

// 响应式数据
const loading = ref(false)
const groupList = ref([])
const searchForm = reactive({
  name: '',
  status: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 弹窗相关
const dialogVisible = ref(false)
const dialogMode = ref<'add' | 'edit'>('add')
const submitLoading = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive({
  id: null,
  name: '',
  description: '',
  status: 1
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '组名称长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

// 方法
const loadGroupList = async () => {
  loading.value = true
  try {
    // 模拟数据
    const mockData = [
      {
        id: 1,
        name: '管理员组',
        description: '系统管理员用户组',
        member_count: 5,
        status: 1,
        created_at: '2024-01-01 10:00:00'
      },
      {
        id: 2,
        name: '编辑组',
        description: '内容编辑用户组',
        member_count: 12,
        status: 1,
        created_at: '2024-01-02 10:00:00'
      },
      {
        id: 3,
        name: '访客组',
        description: '普通访客用户组',
        member_count: 100,
        status: 1,
        created_at: '2024-01-03 10:00:00'
      }
    ]
    
    groupList.value = mockData
    pagination.total = mockData.length
  } catch (error) {
    ElMessage.error('获取用户组列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadGroupList()
}

const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    status: ''
  })
  handleSearch()
}

const handleAdd = () => {
  dialogMode.value = 'add'
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row: any) => {
  dialogMode.value = 'edit'
  Object.assign(formData, row)
  dialogVisible.value = true
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户组 "${row.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('删除成功')
    loadGroupList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleToggleStatus = async (row: any) => {
  try {
    ElMessage.success(`${row.status ? '启用' : '禁用'}成功`)
  } catch (error) {
    ElMessage.error('状态切换失败')
  }
}

const handleViewMembers = (row: any) => {
  ElMessage.info(`查看用户组 "${row.name}" 的成员`)
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(`${dialogMode.value === 'add' ? '添加' : '更新'}成功`)
    dialogVisible.value = false
    loadGroupList()
  } catch (error) {
    ElMessage.error(`${dialogMode.value === 'add' ? '添加' : '更新'}失败`)
  } finally {
    submitLoading.value = false
  }
}

const resetForm = () => {
  Object.assign(formData, {
    id: null,
    name: '',
    description: '',
    status: 1
  })
  formRef.value?.resetFields()
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadGroupList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadGroupList()
}

// 生命周期
onMounted(() => {
  loadGroupList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.header-actions {
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.list-card {
  margin-bottom: 20px;
}

.text-right {
  text-align: right;
}

.pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style> 