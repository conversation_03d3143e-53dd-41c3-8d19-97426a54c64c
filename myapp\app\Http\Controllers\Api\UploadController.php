<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class UploadController extends Controller
{
    /**
     * 上传图片
     */
    public function uploadImage(Request $request): JsonResponse
    {
        // 验证请求
        $validator = Validator::make($request->all(), [
            'image' => 'required|image|mimes:jpeg,jpg,png,gif,webp|max:5120', // 最大5MB
        ], [
            'image.required' => '请选择要上传的图片',
            'image.image' => '上传的文件必须是图片格式',
            'image.mimes' => '图片格式只支持 jpeg, jpg, png, gif, webp',
            'image.max' => '图片大小不能超过5MB',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $file = $request->file('image');
            
            // 生成文件名
            $extension = $file->getClientOriginalExtension();
            $filename = 'banner_' . time() . '_' . Str::random(10) . '.' . $extension;
            
            // 存储文件到 public/uploads/banners 目录
            $path = $file->storeAs('uploads/banners', $filename, 'public');
            
            // 生成完整的URL
            $url = Storage::url($path);
            $fullUrl = url($url);
            
            return response()->json([
                'success' => true,
                'message' => '图片上传成功',
                'data' => [
                    'path' => $path,
                    'url' => $url,
                    'full_url' => $fullUrl,
                    'filename' => $filename,
                    'size' => $file->getSize(),
                    'mime_type' => $file->getMimeType(),
                ],
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '图片上传失败：' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 删除图片
     */
    public function deleteImage(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        try {
            $path = $request->input('path');
            
            // 检查文件是否存在
            if (Storage::disk('public')->exists($path)) {
                Storage::disk('public')->delete($path);
                
                return response()->json([
                    'success' => true,
                    'message' => '图片删除成功',
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => '图片文件不存在',
                ], 404);
            }
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '图片删除失败：' . $e->getMessage(),
            ], 500);
        }
    }
} 