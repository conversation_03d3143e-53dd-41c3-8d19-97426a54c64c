<?php

namespace App\Filament\Resources\BannerResource\Pages;

use App\Filament\Resources\BannerResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewBanner extends ViewRecord
{
    protected static string $resource = BannerResource::class;

    protected function getActions(): array
    {
        return [
            Actions\EditAction::make()->label('编辑'),
            Actions\DeleteAction::make()->label('删除'),
        ];
    }

    protected function getTitle(): string
    {
        return '查看轮播图';
    }
} 