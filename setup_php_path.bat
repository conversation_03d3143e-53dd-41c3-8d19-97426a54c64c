@echo off
echo ===========================================
echo PHP路径设置和Laravel测试脚本
echo ===========================================
echo.

REM 设置PHP路径（根据您的phpstudy路径）
set PHP_PATH=D:\phpstudy_pro\Extensions\php\php8.1.9nts
set PATH=%PHP_PATH%;%PATH%

echo 正在设置PHP路径: %PHP_PATH%
echo.

REM 测试PHP是否可用
echo 测试PHP版本:
php -v
echo.

REM 进入Laravel项目目录
cd /d myapp
echo 已进入Laravel项目目录: %CD%
echo.

REM 测试Laravel Artisan命令
echo 测试Laravel Artisan命令:
php artisan --version
echo.

REM 清除缓存
echo 清除Laravel缓存:
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
echo.

REM 生成应用密钥（如果需要）
echo 检查应用密钥:
php artisan key:generate --show
echo.

REM 运行数据库迁移
echo 运行数据库迁移:
php artisan migrate --force
echo.

REM 启动开发服务器
echo 启动Laravel开发服务器:
echo 请在浏览器中访问: http://localhost:8000
echo 按Ctrl+C停止服务器
echo.
php artisan serve --host=0.0.0.0 --port=8000

pause 