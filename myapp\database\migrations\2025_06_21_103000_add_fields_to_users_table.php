<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('phone')->nullable()->after('email'); // 手机号
            $table->unsignedBigInteger('region_id')->nullable()->after('phone'); // 所在地区
            $table->string('timezone')->default('America/New_York')->after('region_id'); // 时区
            $table->boolean('email_notifications')->default(true)->after('timezone'); // 邮件通知开关
            $table->boolean('wechat_notifications')->default(false)->after('email_notifications'); // 微信通知开关
            $table->string('wechat_id')->nullable()->after('wechat_notifications'); // 微信ID
            $table->json('notification_preferences')->nullable()->after('wechat_id'); // 通知偏好设置
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active')->after('notification_preferences'); // 用户状态
            $table->timestamp('last_login_at')->nullable()->after('status'); // 最后登录时间
            $table->string('avatar')->nullable()->after('last_login_at'); // 头像

            $table->foreign('region_id')->references('id')->on('regions')->onDelete('set null');
            $table->index(['region_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['region_id']);
            $table->dropIndex(['region_id', 'status']);
            $table->dropColumn([
                'phone',
                'region_id',
                'timezone',
                'email_notifications',
                'wechat_notifications',
                'wechat_id',
                'notification_preferences',
                'status',
                'last_login_at',
                'avatar'
            ]);
        });
    }
};
