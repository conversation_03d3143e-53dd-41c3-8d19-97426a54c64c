@echo off
chcp 65001 >nul
echo.
echo =============================================
echo        启动Laravel管理后台服务器
echo =============================================
echo.

:: 检查是否在Laravel项目目录
if not exist "artisan" (
    echo ❌ 错误: 请在Laravel项目根目录运行此脚本
    echo 当前目录: %cd%
    pause
    exit /b 1
)

:: 检查PHP命令
where php >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到PHP命令
    echo 💡 解决方案:
    echo    1. 运行 install_php.bat 安装PHP
    echo    2. 或手动安装PHP并添加到PATH
    pause
    exit /b 1
)

echo 📁 项目目录: %cd%
echo.

:: 显示PHP版本
echo 🔍 PHP版本信息:
php -v
echo.

:: 检查是否安装了FilamentPHP
if not exist "vendor\filament" (
    echo ⚠️  警告: 未检测到FilamentPHP
    echo.
    echo 💡 解决方案:
    echo    运行 install_filament.bat 安装FilamentPHP管理后台
    echo.
    set /p choice="是否现在安装FilamentPHP? (y/n): "
    if /i "!choice!"=="y" (
        call install_filament.bat
        if errorlevel 1 (
            echo ❌ FilamentPHP安装失败
            pause
            exit /b 1
        )
    ) else (
        echo 跳过安装，继续启动服务器...
    )
    echo.
)

:: 显示启动信息
echo ==========================================
echo           服务器启动信息
echo ==========================================
echo.
echo 🌐 管理后台地址: http://localhost:8000/admin
echo 🏠 前端首页地址: http://localhost:8000
echo 📋 测试页面地址: http://localhost:8000/test
echo.
echo 💡 提示:
echo    - 按 Ctrl+C 停止服务器
echo    - 管理后台需要登录才能访问
echo    - 如果没有管理员账号，请先运行: php artisan make:filament-user
echo.
echo ==========================================
echo.

:: 启动Laravel开发服务器
echo 🚀 启动Laravel开发服务器...
echo.
php artisan serve --host=127.0.0.1 --port=8000

echo.
echo 📴 服务器已停止
pause 