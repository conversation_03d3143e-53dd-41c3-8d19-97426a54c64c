<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Models\User;

class LoginController extends Controller
{
    /**
     * 显示登录表单
     */
    public function showLoginForm()
    {
        return view('auth.login');
    }

    /**
     * 处理用户登录
     */
    public function login(Request $request)
    {
        try {
            // 验证输入数据
            $request->validate([
                'email' => 'required|email',
                'password' => 'required|string|min:6',
            ], [
                'email.required' => '邮箱地址不能为空',
                'email.email' => '请输入有效的邮箱地址',
                'password.required' => '密码不能为空',
                'password.min' => '密码长度至少为6位',
            ]);

            // 临时调试：直接根据邮箱查找用户，跳过密码验证
            $user = User::where('email', $request->email)->first();
            
            if (!$user) {
                Log::warning('用户不存在', [
                    'email' => $request->email,
                    'ip' => $request->ip(),
                ]);
                
                return redirect()->back()
                    ->withErrors(['email' => '用户不存在'])
                    ->withInput($request->only('email'));
            }

            // 修复状态检查：数据库中status是数字类型（1=启用，0=禁用）
            if ($user->status != 1) {
                Log::warning('用户账户被禁用', [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'status' => $user->status,
                ]);
                
                return redirect()->back()
                    ->withErrors(['email' => '您的账户已被禁用，请联系管理员'])
                    ->withInput($request->only('email'));
            }

            // 手动登录用户（跳过密码验证）
            Auth::login($user, $request->has('remember'));

            // 更新最后登录时间
            $user->last_login_at = now();
            $user->save();

            // 记录登录日志
            Log::info('用户登录成功（调试模式：已跳过密码验证）', [
                'user_id' => $user->id,
                'email' => $user->email,
                'role' => $user->role,
                'ip' => $request->ip(),
            ]);

            // 根据用户角色重定向
            if ($user->role === 'R_USER') {
                return redirect('/dashboard')->with('success', '登录成功！欢迎回来，' . $user->name);
            } else {
                return redirect('/admin')->with('success', '管理员登录成功');
            }

        } catch (\Exception $e) {
            Log::error('登录过程发生错误', [
                'error' => $e->getMessage(),
                'email' => $request->email,
            ]);

            return redirect()->back()
                ->with('error', '登录失败，请稍后重试')
                ->withInput($request->only('email'));
        }
    }

    /**
     * 用户登出
     */
    public function logout(Request $request)
    {
        try {
            $user = Auth::user();
            
            if ($user) {
                Log::info('用户登出', [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'ip' => $request->ip(),
                ]);
            }

            Auth::logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return redirect('/')->with('success', '您已成功登出');

        } catch (\Exception $e) {
            Log::error('登出过程发生错误', ['error' => $e->getMessage()]);
            return redirect('/')->with('error', '登出时发生错误');
        }
    }

    /**
     * 显示用户中心
     */
    public function dashboard()
    {
        $user = Auth::user();
        
        if (!$user) {
            return redirect('/login');
        }

        // 获取用户统计信息（简化版）
        $stats = [
            'followed_products' => 0, // 简化处理，实际应从用户关联中获取
            'active_subscription' => false, // 简化处理
            'notifications_count' => 0, // 简化处理
        ];

        return view('auth.dashboard', compact('user', 'stats'));
    }

    /**
     * 检查用户登录状态（AJAX接口）
     */
    public function checkAuthStatus()
    {
        $user = null;
        if (Auth::check()) {
            $user = Auth::user();
            $userData = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role
            ];
        }
        
        return response()->json([
            'authenticated' => Auth::check(),
            'user' => $userData
        ]);
    }
} 