<!-- 语言切换组件 -->
<div class="language-dropdown">
    <button class="language-btn text-gray-600 hover:text-gray-900">
        <span>{{ app()->getLocale() == 'zh_CN' ? '🇨🇳' : '🇺🇸' }}</span>
        <span>{{ app()->getLocale() == 'zh_CN' ? '中文' : 'English' }}</span>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
    </button>
    <div class="language-dropdown-content">
        <a href="#" onclick="switchLanguage('zh_CN')">
            🇨🇳 中文
        </a>
        <a href="#" onclick="switchLanguage('en')">
            🇺🇸 English
        </a>
        <div class="language-divider"></div>
        <a href="#" onclick="resetLanguage()" class="reset-language">
            🔄 重置为中文
        </a>
    </div>
</div>

<style>
/* 语言切换按钮样式 */
.language-dropdown {
    position: relative;
    display: inline-block;
}

.language-dropdown-content {
    display: none;
    position: absolute;
    background-color: white;
    min-width: 140px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1000;
    border-radius: 8px;
    overflow: hidden;
    right: 0;
    top: 100%;
    margin-top: 5px;
}

.language-dropdown:hover .language-dropdown-content {
    display: block;
}

.language-dropdown-content a {
    color: #374151;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    transition: background-color 0.3s;
}

.language-dropdown-content a:hover {
    background-color: #f3f4f6;
}

.language-dropdown-content a.reset-language {
    color: #6b7280;
    font-size: 0.875rem;
}

.language-dropdown-content a.reset-language:hover {
    background-color: #f9fafb;
    color: #374151;
}

.language-divider {
    height: 1px;
    background-color: #e5e7eb;
    margin: 4px 0;
}

.language-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: background-color 0.3s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.language-btn:hover {
    background-color: #f3f4f6;
}
</style>

<script>
// 语言切换函数
function switchLanguage(locale) {
    // 创建表单提交
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/language/switch/${locale}`;
    form.style.display = 'none';
    
    // 添加CSRF token
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = '_token';
    csrfInput.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    form.appendChild(csrfInput);
    
    // 添加返回URL参数
    const returnUrlInput = document.createElement('input');
    returnUrlInput.type = 'hidden';
    returnUrlInput.name = 'return_url';
    returnUrlInput.value = window.location.href;
    form.appendChild(returnUrlInput);
    
    // 提交表单
    document.body.appendChild(form);
    form.submit();
}

// 重置语言函数
function resetLanguage() {
    if (confirm('确定要重置语言为中文吗？')) {
        // 创建表单提交
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/language/reset';
        form.style.display = 'none';
        
        // 添加CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        form.appendChild(csrfInput);
        
        // 添加返回URL参数
        const returnUrlInput = document.createElement('input');
        returnUrlInput.type = 'hidden';
        returnUrlInput.name = 'return_url';
        returnUrlInput.value = window.location.href;
        form.appendChild(returnUrlInput);
        
        // 提交表单
        document.body.appendChild(form);
        form.submit();
    }
}
</script> 