<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>商品品牌管理</span>
          <el-button type="primary" @click="handleAdd" :icon="Plus">
            添加品牌
          </el-button>
        </div>
      </template>

      <div class="brand-list">
        <el-tag
          v-for="brand in brands"
          :key="brand"
          closable
          @close="handleDelete(brand)"
          @click="handleEdit(brand)"
          class="brand-tag"
          type="success"
        >
          {{ brand }}
        </el-tag>
      </div>
    </el-card>

    <!-- 添加/编辑品牌弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogMode === 'add' ? '添加品牌' : '编辑品牌'"
      width="400px"
    >
      <el-form :model="formData" label-width="80px">
        <el-form-item label="品牌名称">
          <el-input v-model="formData.name" placeholder="请输入品牌名称" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">
            {{ dialogMode === 'add' ? '添加' : '更新' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const dialogVisible = ref(false)
const dialogMode = ref<'add' | 'edit'>('add')
const brands = ref<string[]>([])

const formData = reactive({
  name: '',
  oldName: ''
})

onMounted(() => {
  loadBrands()
})

const loadBrands = async () => {
  try {
    // 这里应该调用实际的API
    console.log('加载品牌列表')
    // const response = await productApi.getOptions()
    // brands.value = response.data.brands || []
    brands.value = ['Apple', 'Samsung', 'Huawei', 'Xiaomi', 'OPPO', 'Vivo'] // 模拟数据
  } catch (error) {
    ElMessage.error('加载品牌失败')
  }
}

const handleAdd = () => {
  dialogMode.value = 'add'
  formData.name = ''
  formData.oldName = ''
  dialogVisible.value = true
}

const handleEdit = (brand: string) => {
  dialogMode.value = 'edit'
  formData.name = brand
  formData.oldName = brand
  dialogVisible.value = true
}

const handleDelete = async (brand: string) => {
  try {
    // 这里应该调用实际的API检查是否有商品使用该品牌
    console.log('删除品牌', brand)
    ElMessage.success('删除成功')
    loadBrands()
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

const handleSubmit = async () => {
  if (!formData.name.trim()) {
    ElMessage.warning('请输入品牌名称')
    return
  }

  try {
    if (dialogMode.value === 'add') {
      // 这里应该调用实际的API
      console.log('添加品牌', formData.name)
      ElMessage.success('添加成功')
    } else {
      // 这里应该调用实际的API
      console.log('更新品牌', formData.oldName, formData.name)
      ElMessage.success('更新成功')
    }
    
    dialogVisible.value = false
    loadBrands()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.brand-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.brand-tag {
  cursor: pointer;
  font-size: 14px;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.brand-tag:hover {
  transform: scale(1.05);
}

.dialog-footer {
  text-align: right;
}
</style> 