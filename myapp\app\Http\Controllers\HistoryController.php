<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\Product;
use App\Models\UserProductFollow;
use App\Models\Notification;

class HistoryController extends Controller
{
    /**
     * 显示历史商品页面
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();
            
            // 获取用户关注的商品历史
            $followedProducts = $this->getUserFollowedProducts($user, $request);
            
            // 获取用户收到的通知历史
            $notifications = $this->getUserNotifications($user, $request);
            
            // 获取热门商品（用于推荐）
            $popularProducts = $this->getPopularProducts();
            
            // 记录访问日志
            Log::info('用户访问历史页面', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'timestamp' => now(),
                'followed_products_count' => $followedProducts->count(),
                'notifications_count' => $notifications->count(),
            ]);
            
            return view('history', compact(
                'followedProducts',
                'notifications', 
                'popularProducts'
            ));
            
        } catch (\Exception $e) {
            Log::error('历史页面加载失败', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return redirect()->route('dashboard')
                ->with('error', '加载历史数据失败，请稍后重试。');
        }
    }
    
    /**
     * 获取用户关注的商品
     */
    private function getUserFollowedProducts($user, $request)
    {
        $query = $user->followedProducts()
            ->withPivot(['email_notifications', 'wechat_notifications', 'created_at'])
            ->withCount('followers')
            ->orderBy('user_product_follows.created_at', 'desc');
        
        // 搜索过滤
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('products.name', 'like', "%{$search}%")
                  ->orWhere('products.description', 'like', "%{$search}%")
                  ->orWhere('products.brand', 'like', "%{$search}%");
            });
        }
        
        // 分类过滤
        if ($request->has('category') && !empty($request->category)) {
            $query->where('products.category', $request->category);
        }
        
        // 状态过滤
        if ($request->has('status') && !empty($request->status)) {
            if ($request->status === 'active') {
                $query->where('products.is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('products.is_active', false);
            } elseif ($request->status === 'out_of_stock') {
                $query->where('products.stock_status', 'out_of_stock');
            }
        }
        
        return $query->paginate(20);
    }
    
    /**
     * 获取用户通知历史
     */
    private function getUserNotifications($user, $request)
    {
        $query = $user->notifications()
            ->with('product')
            ->orderBy('created_at', 'desc');
        
        // 通知类型过滤
        if ($request->has('notification_type') && !empty($request->notification_type)) {
            $query->where('type', $request->notification_type);
        }
        
        // 通知渠道过滤  
        if ($request->has('channel') && !empty($request->channel)) {
            $query->where('channel', $request->channel);
        }
        
        return $query->limit(50)->get();
    }
    
    /**
     * 获取热门商品
     */
    private function getPopularProducts()
    {
        return Product::withCount('followers')
            ->where('is_active', true)
            ->orderBy('followers_count', 'desc')
            ->limit(10)
            ->get();
    }
    
    /**
     * 取消关注商品
     */
    public function unfollowProduct(Request $request, $productId)
    {
        try {
            $user = Auth::user();
            
            $follow = UserProductFollow::where('user_id', $user->id)
                ->where('product_id', $productId)
                ->first();
                
            if (!$follow) {
                return response()->json([
                    'success' => false,
                    'message' => '未找到关注记录'
                ], 404);
            }
            
            $follow->delete();
            
            Log::info('用户取消关注商品', [
                'user_id' => $user->id,
                'product_id' => $productId,
                'timestamp' => now(),
            ]);
            
            return response()->json([
                'success' => true,
                'message' => '已取消关注'
            ]);
            
        } catch (\Exception $e) {
            Log::error('取消关注商品失败', [
                'user_id' => Auth::id(),
                'product_id' => $productId,
                'error' => $e->getMessage(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '取消关注失败，请稍后重试'
            ], 500);
        }
    }
    
    /**
     * 更新商品关注设置
     */
    public function updateFollowSettings(Request $request, $productId)
    {
        try {
            $user = Auth::user();
            
            $validated = $request->validate([
                'email_notifications' => 'boolean',
                'wechat_notifications' => 'boolean',
            ]);
            
            $follow = UserProductFollow::where('user_id', $user->id)
                ->where('product_id', $productId)
                ->first();
                
            if (!$follow) {
                return response()->json([
                    'success' => false,
                    'message' => '未找到关注记录'
                ], 404);
            }
            
            $follow->update($validated);
            
            Log::info('用户更新商品关注设置', [
                'user_id' => $user->id,
                'product_id' => $productId,
                'settings' => $validated,
                'timestamp' => now(),
            ]);
            
            return response()->json([
                'success' => true,
                'message' => '设置已更新'
            ]);
            
        } catch (\Exception $e) {
            Log::error('更新商品关注设置失败', [
                'user_id' => Auth::id(),
                'product_id' => $productId,
                'error' => $e->getMessage(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '更新设置失败，请稍后重试'
            ], 500);
        }
    }
    
    /**
     * 获取商品详情（AJAX）
     */
    public function getProductDetails($productId)
    {
        try {
            $product = Product::with(['region', 'followers' => function($query) {
                $query->where('user_id', Auth::id());
            }])->findOrFail($productId);
            
            return response()->json([
                'success' => true,
                'product' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'description' => $product->description,
                    'price' => $product->price,
                    'brand' => $product->brand,
                    'category' => $product->category,
                    'status' => $product->status,
                    'image_url' => $product->image_url,
                    'product_url' => $product->product_url,
                    'region_name' => $product->region->name ?? '',
                    'followers_count' => $product->followers_count,
                    'is_followed' => $product->followers->isNotEmpty(),
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取商品详情失败', [
                'product_id' => $productId,
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '获取商品详情失败'
            ], 500);
        }
    }
    
    /**
     * 关注商品
     */
    public function followProduct(Request $request, $productId)
    {
        try {
            $user = Auth::user();
            
            // 检查商品是否存在
            $product = Product::findOrFail($productId);
            
            // 检查是否已经关注
            $existingFollow = UserProductFollow::where('user_id', $user->id)
                ->where('product_id', $productId)
                ->first();
                
            if ($existingFollow) {
                return response()->json([
                    'success' => false,
                    'message' => '您已经关注了这个商品'
                ], 400);
            }
            
            // 验证请求数据
            $validated = $request->validate([
                'email_notifications' => 'boolean',
                'wechat_notifications' => 'boolean',
            ]);
            
            // 创建关注记录
            UserProductFollow::create([
                'user_id' => $user->id,
                'product_id' => $productId,
                'email_notifications' => $validated['email_notifications'] ?? true,
                'wechat_notifications' => $validated['wechat_notifications'] ?? false,
                'notification_settings' => [
                    'price_drop' => true,
                    'back_in_stock' => true,
                    'new_similar_products' => false,
                ]
            ]);
            
            Log::info('用户关注商品', [
                'user_id' => $user->id,
                'product_id' => $productId,
                'product_name' => $product->name,
                'settings' => $validated,
                'timestamp' => now(),
            ]);
            
            return response()->json([
                'success' => true,
                'message' => '关注成功！您将收到该商品的价格变动和库存通知。'
            ]);
            
        } catch (\Exception $e) {
            Log::error('关注商品失败', [
                'user_id' => Auth::id(),
                'product_id' => $productId,
                'error' => $e->getMessage(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '关注失败，请稍后重试'
            ], 500);
        }
    }
    
    /**
     * 检查商品是否已关注
     */
    public function checkFollowStatus($productId)
    {
        try {
            $user = Auth::user();
            
            $isFollowed = UserProductFollow::where('user_id', $user->id)
                ->where('product_id', $productId)
                ->exists();
                
            return response()->json([
                'success' => true,
                'is_followed' => $isFollowed
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '检查关注状态失败'
            ], 500);
        }
    }
}
