@echo off
chcp 65001 >nul
echo ===========================================
echo 注册功能修复脚本
echo ===========================================
echo.

rem 检查项目目录
if not exist "myapp\artisan" (
    echo [错误] 未找到Laravel项目，请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

rem 进入项目目录
cd myapp
echo [信息] 已进入Laravel项目目录
echo.

rem 设置PHP路径（多个可能的路径）
set "PHP_PATHS=D:\phpstudy_pro\Extensions\php\php8.1.9nts\php.exe;D:\phpstudy_pro\Extensions\php\php8.0.28nts\php.exe;D:\phpstudy_pro\Extensions\php\php7.4.33nts\php.exe;php.exe"

set "PHP_FOUND="
for %%p in ("%PHP_PATHS:;=" "%") do (
    if exist "%%~p" (
        set "PHP_EXE=%%~p"
        set "PHP_FOUND=1"
        goto :found_php
    )
)

rem 如果在环境变量中找不到php，尝试系统路径
where php.exe >nul 2>&1
if %errorlevel% equ 0 (
    set "PHP_EXE=php.exe"
    set "PHP_FOUND=1"
)

:found_php
if not defined PHP_FOUND (
    echo [错误] 找不到PHP可执行文件，请检查phpstudy安装或PHP环境变量
    pause
    exit /b 2
)

echo [信息] 使用PHP: %PHP_EXE%
echo.

echo [步骤 1/5] 检查数据库连接...
"%PHP_EXE%" artisan db:show
if %errorlevel% neq 0 (
    echo [错误] 数据库连接失败，请检查.env配置
    pause
    exit /b 3
)
echo [成功] 数据库连接正常
echo.

echo [步骤 2/5] 检查regions表数据...
"%PHP_EXE%" artisan tinker --execute="use App\Models\Region; $count = Region::count(); echo '地区数量: ' . $count;"
echo.

echo [步骤 3/5] 创建默认地区数据...
"%PHP_EXE%" artisan tinker --execute="use App\Models\Region; if(Region::count() == 0) { Region::create(['name' => '北京市', 'code' => 'BJ']); Region::create(['name' => '上海市', 'code' => 'SH']); Region::create(['name' => '广东省', 'code' => 'GD']); Region::create(['name' => '浙江省', 'code' => 'ZJ']); Region::create(['name' => '江苏省', 'code' => 'JS']); echo '已创建默认地区数据'; } else { echo '地区数据已存在，跳过创建'; }"
echo.

echo [步骤 4/5] 测试注册功能...
echo 创建测试用户（如果不存在）...
"%PHP_EXE%" artisan tinker --execute="use App\Models\User; try { $user = User::where('email', '<EMAIL>')->first(); if(\$user) { echo '测试用户已存在: ' . \$user->email; } else { \$user = User::create(['name' => '测试注册用户', 'email' => '<EMAIL>', 'password' => bcrypt('test123'), 'role' => 'R_USER', 'status' => 'active', 'email_verified_at' => now()]); echo '测试用户创建成功: ' . \$user->email; } } catch(Exception \$e) { echo '创建用户失败: ' . \$e->getMessage(); }"
echo.

echo [步骤 5/5] 验证用户数据...
"%PHP_EXE%" artisan tinker --execute="use App\Models\User; \$user = User::where('email', '<EMAIL>')->first(); if(\$user) { echo '用户验证成功 - 姓名: ' . \$user->name . ', 邮箱: ' . \$user->email . ', 角色: ' . \$user->role . ', 状态: ' . \$user->status; } else { echo '用户验证失败'; }"
echo.

echo ===========================================
echo ✅ 注册功能修复完成！
echo ===========================================
echo.
echo 修复内容：
echo 1. ✅ 地区字段改为可选
echo 2. ✅ 移除服务条款强制验证
echo 3. ✅ 添加默认角色R_USER
echo 4. ✅ 创建默认地区数据
echo 5. ✅ 改进错误处理
echo.
echo 现在您可以尝试注册新用户了！
echo 访问: http://localhost:8000/register
echo.
echo 测试账号信息：
echo 邮箱: <EMAIL>
echo 密码: test123
echo.

pause 