<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogType === 'add' ? '添加用户' : '编辑用户'"
    width="500px"
    align-center
  >
    <ElForm ref="formRef" :model="formData" :rules="rules" label-width="80px">
      <ElFormItem label="用户名" prop="name">
        <ElInput v-model="formData.name" placeholder="请输入用户名" />
      </ElFormItem>
      <ElFormItem label="邮箱" prop="email">
        <ElInput v-model="formData.email" placeholder="请输入邮箱地址" />
      </ElFormItem>
      <ElFormItem label="手机号" prop="phone">
        <ElInput v-model="formData.phone" placeholder="请输入手机号" />
      </ElFormItem>
      <ElFormItem label="角色" prop="role">
        <ElSelect v-model="formData.role" placeholder="请选择角色">
          <ElOption label="超级管理员" value="R_SUPER" />
          <ElOption label="管理员" value="R_ADMIN" />
          <ElOption label="编辑员" value="R_EDITOR" />
          <ElOption label="用户" value="R_USER" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem label="状态" prop="status">
        <ElSelect v-model="formData.status" placeholder="请选择状态">
          <ElOption label="启用" :value="1" />
          <ElOption label="禁用" :value="0" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem v-if="dialogType === 'add'" label="密码" prop="password">
        <ElInput 
          v-model="formData.password" 
          type="password" 
          show-password 
          placeholder="请输入密码" 
        />
      </ElFormItem>
      <ElFormItem v-if="dialogType === 'add'" label="确认密码" prop="confirmPassword">
        <ElInput 
          v-model="formData.confirmPassword" 
          type="password" 
          show-password 
          placeholder="请再次输入密码" 
        />
      </ElFormItem>
      <ElFormItem v-if="dialogType === 'edit'" label="新密码" prop="password">
        <ElInput 
          v-model="formData.password" 
          type="password" 
          show-password 
          placeholder="留空则不修改密码" 
        />
      </ElFormItem>
      <ElFormItem v-if="dialogType === 'edit'" label="确认密码" prop="confirmPassword">
        <ElInput 
          v-model="formData.confirmPassword" 
          type="password" 
          show-password 
          placeholder="请再次输入新密码" 
        />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="dialogVisible = false">取消</ElButton>
        <ElButton type="primary" :loading="loading" @click="handleSubmit">
          {{ loading ? '提交中...' : '提交' }}
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { UserService, type User, type UserCreateParams, type UserUpdateParams } from '@/api/userApi'
  import type { FormInstance, FormRules } from 'element-plus'
  import { ElMessage } from 'element-plus'

  interface Props {
    visible: boolean
    type: string
    userData?: Partial<User>
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void
    (e: 'submit'): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 对话框显示控制
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })

  const dialogType = computed(() => props.type)
  const loading = ref(false)

  // 表单实例
  const formRef = ref<FormInstance>()

  // 表单数据
  const formData = reactive({
    name: '',
    email: '',
    phone: '',
    role: 'R_USER',
    status: 1,
    password: '',
    confirmPassword: ''
  })

  // 表单验证规则
  const rules: FormRules = {
    name: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
    ],
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    phone: [
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
    ],
    role: [
      { required: true, message: '请选择角色', trigger: 'blur' }
    ],
    status: [
      { required: true, message: '请选择状态', trigger: 'blur' }
    ],
    password: [
      { 
        validator: (rule, value, callback) => {
          if (dialogType.value === 'add' && !value) {
            callback(new Error('请输入密码'))
          } else if (value && value.length < 6) {
            callback(new Error('密码长度不能少于6位'))
          } else if (dialogType.value === 'edit' && value && value.length < 6) {
            callback(new Error('密码长度不能少于6位'))
          } else {
            callback()
          }
        }, 
        trigger: 'blur' 
      }
    ],
    confirmPassword: [
      {
        validator: (rule, value, callback) => {
          if (dialogType.value === 'add' && !value) {
            callback(new Error('请再次输入密码'))
          } else if (value !== formData.password) {
            callback(new Error('两次输入的密码不一致'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]
  }

  // 初始化表单数据
  const initFormData = () => {
    const isEdit = props.type === 'edit' && props.userData
    const row = props.userData

    Object.assign(formData, {
      name: isEdit ? row?.name || '' : '',
      email: isEdit ? row?.email || '' : '',
      phone: isEdit ? row?.phone || '' : '',
      role: isEdit ? row?.role || 'R_USER' : 'R_USER',
      status: isEdit ? (row?.status !== undefined ? row.status : 1) : 1,
      password: '',
      confirmPassword: ''
    })
  }

  // 统一监听对话框状态变化
  watch(
    () => [props.visible, props.type, props.userData],
    ([visible]) => {
      if (visible) {
        initFormData()
        nextTick(() => {
          formRef.value?.clearValidate()
        })
      }
    },
    { immediate: true }
  )

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        loading.value = true
        try {
          if (dialogType.value === 'add') {
            // 创建用户
            const createData: UserCreateParams = {
              name: formData.name,
              email: formData.email,
              phone: formData.phone,
              password: formData.password,
              role: formData.role,
              status: formData.status
            }
            
            const response = await UserService.createUser(createData)
            
            if (response.code === 200) {
              ElMessage.success('用户创建成功')
              dialogVisible.value = false
              emit('submit')
            } else {
              ElMessage.error(response.msg || '创建失败')
            }
          } else {
            // 更新用户
            const updateData: UserUpdateParams = {
              name: formData.name,
              email: formData.email,
              phone: formData.phone,
              role: formData.role,
              status: formData.status
            }

            // 如果填写了密码，则包含密码更新
            if (formData.password) {
              updateData.password = formData.password
            }
            
            const response = await UserService.updateUser(props.userData?.id!, updateData)
            
            if (response.code === 200) {
              ElMessage.success('用户更新成功')
        dialogVisible.value = false
        emit('submit')
            } else {
              ElMessage.error(response.msg || '更新失败')
            }
          }
        } catch (error: any) {
          ElMessage.error(error.message || '操作失败')
        } finally {
          loading.value = false
        }
      }
    })
  }
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
</style>
