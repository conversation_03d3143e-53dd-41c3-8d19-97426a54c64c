<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class PaymentOrder extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'plan_id',
        'region_id',
        'amount',
        'original_amount',
        'discount_amount',
        'currency',
        'payment_method',
        'billing_cycle',
        'status',
        'order_number',
        'transaction_id',
        'paypal_order_id',
        'failed_reason',
        'paid_at',
        'expires_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'original_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'paid_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * 用户关联
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 套餐关联
     */
    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    /**
     * 地区关联
     */
    public function region(): BelongsTo
    {
        return $this->belongsTo(Region::class);
    }

    /**
     * 订阅关联
     */
    public function subscription(): HasOne
    {
        return $this->hasOne(Subscription::class);
    }

    /**
     * 获取已支付的订单
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * 获取待支付的订单
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * 获取已失败的订单
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * 获取已过期的订单
     */
    public function scopeExpired($query)
    {
        return $query->where('status', 'expired');
    }

    /**
     * 检查订单是否已过期
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at < now();
    }

    /**
     * 获取支付方式文本
     */
    public function getPaymentMethodTextAttribute(): string
    {
        return match($this->payment_method) {
            'alipay' => '支付宝',
            'wechat' => '微信支付',
            'paypal' => 'PayPal',
            default => $this->payment_method,
        };
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            'pending' => '待支付',
            'paid' => '已支付',
            'failed' => '支付失败',
            'expired' => '已过期',
            'cancelled' => '已取消',
            default => $this->status,
        };
    }
}
