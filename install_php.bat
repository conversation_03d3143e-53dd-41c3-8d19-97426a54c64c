@echo off
chcp 65001 >nul
echo.
echo =============================================
echo         自动安装PHP环境 (Windows)
echo =============================================
echo.

:: 检查是否已安装PHP
where php >nul 2>&1
if not errorlevel 1 (
    echo ✅ PHP已安装
    php -v
    echo.
    echo 如需重新安装，请手动删除现有PHP并重新运行此脚本
    pause
    exit /b 0
)

echo 📥 开始下载PHP...
echo.

:: 创建临时目录
if exist "temp_php" rd /s /q temp_php
if not exist "temp_php" mkdir temp_php
cd temp_php

:: PHP下载地址 (使用国内镜像)
set PHP_VERSION=8.0.30
set PHP_URL=https://windows.php.net/downloads/releases/php-%PHP_VERSION%-nts-Win32-vs16-x64.zip

echo 🌐 下载地址: %PHP_URL%
echo 📁 下载到: %cd%
echo.

:: 使用PowerShell下载PHP
echo 正在下载PHP %PHP_VERSION%...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri '%PHP_URL%' -OutFile 'php.zip'}"

if not exist "php.zip" (
    echo ❌ PHP下载失败
    cd ..
    rd /s /q temp_php
    pause
    exit /b 1
)

echo ✅ PHP下载完成
echo.

:: 解压PHP到C:\PHP
echo 📂 解压PHP到 C:\PHP...
if exist "C:\PHP" (
    echo 删除现有PHP目录...
    rd /s /q "C:\PHP"
)

:: 使用PowerShell解压
powershell -Command "& {Expand-Archive -Path 'php.zip' -DestinationPath 'C:\PHP' -Force}"

if not exist "C:\PHP\php.exe" (
    echo ❌ PHP解压失败
    cd ..
    rd /s /q temp_php
    pause
    exit /b 1
)

echo ✅ PHP解压完成
echo.

:: 配置PHP
echo ⚙️ 配置PHP...
cd /d "C:\PHP"

:: 复制php.ini配置文件
if exist "php.ini-development" (
    copy "php.ini-development" "php.ini" >nul
    echo ✅ 创建php.ini配置文件
) else (
    echo ❌ 未找到php.ini-development文件
)

:: 修改php.ini配置
echo 📝 修改PHP配置...
powershell -Command "$filePath = 'php.ini'; $content = Get-Content $filePath; $content = $content -replace ';extension_dir = \"ext\"', 'extension_dir = \"ext\"'; $content = $content -replace 'memory_limit = 128M', 'memory_limit = 1G'; $content = $content -replace ';extension=curl', 'extension=curl'; $content = $content -replace ';extension=gd2', 'extension=gd2'; $content = $content -replace ';extension=mbstring', 'extension=mbstring'; $content = $content -replace ';extension=openssl', 'extension=openssl'; $content = $content -replace ';extension=pdo_mysql', 'extension=pdo_mysql'; $content = $content -replace ';extension=pdo_sqlite', 'extension=pdo_sqlite'; $content = $content -replace ';extension=sockets', 'extension=sockets'; $content = $content -replace ';extension=fileinfo', 'extension=fileinfo'; Set-Content -Path $filePath -Value $content"


echo ✅ PHP配置完成

echo ✅ PHP配置完成
echo.

:: 添加PHP到系统PATH
echo 🔧 添加PHP到系统PATH...

:: 使用PowerShell添加到PATH
powershell -Command "& {$env:Path += ';C:\PHP'; [Environment]::SetEnvironmentVariable('Path', $env:Path, [EnvironmentVariableTarget]::Machine)}"

echo ✅ PATH配置完成
echo.

:: 清理临时文件
cd /d "%~dp0"
rd /s /q temp_php

:: 验证安装
echo 🔍 验证PHP安装...
"C:\PHP\php.exe" -v

if errorlevel 1 (
    echo ❌ PHP安装验证失败
    pause
    exit /b 1
)

echo.
echo 🎉 PHP安装完成！
echo.
echo ==========================================
echo          安装完成信息
echo ==========================================
echo.
echo 📁 PHP安装路径: C:\PHP
echo 🔧 已添加到系统PATH
echo 📝 配置文件: C:\PHP\php.ini
echo.
echo 💡 重要提示:
echo    请重新打开命令行窗口以使PATH生效
echo    或者直接使用 C:\PHP\php.exe 运行PHP命令
echo.
echo 🚀 下一步: 运行 install_filament.bat 安装管理后台
echo.
echo ==========================================
pause 