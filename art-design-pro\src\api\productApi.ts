import request from '@/utils/http'

export interface ProductItem {
  id: number
  name: string
  sku: string
  description?: string
  brand?: string
  category?: string
  price?: number
  original_price?: number
  currency: string
  stock_quantity: number
  stock_status: 'in_stock' | 'out_of_stock' | 'discontinued'
  stock_status_text: string
  image_url?: string
  product_url?: string
  region_id: number
  region?: {
    id: number
    name: string
    code: string
  }
  attributes?: any
  is_active: boolean
  status_text: string
  followers_count: number
  last_updated_at?: string
  created_at?: string
  updated_at?: string
}

export interface ProductStats {
  total: number
  active: number
  inactive: number
  in_stock: number
  out_of_stock: number
  discontinued: number
}

export interface ProductOptions {
  regions: Array<{
    id: number
    name: string
    code: string
  }>
  categories: string[]
  brands: string[]
  currencies: string[]
  stock_statuses: Array<{
    value: string
    label: string
  }>
}

export interface ProductListParams {
  page?: number
  per_page?: number
  search?: string
  category?: string
  brand?: string
  region_id?: number
  price_min?: number
  price_max?: number
  stock_status?: string
  is_active?: number
  sort_by?: string
  sort_direction?: 'asc' | 'desc'
}

export interface ProductCreateParams {
  name: string
  sku: string
  description?: string
  brand?: string
  category?: string
  price?: number
  original_price?: number
  currency: string
  stock_quantity: number
  stock_status: string
  image_url?: string
  product_url?: string
  region_id: number
  attributes?: string
  is_active?: boolean
}

export interface ProductUpdateParams extends Partial<ProductCreateParams> {
  id: number
}

export interface ProductBatchParams {
  action: 'delete' | 'activate' | 'deactivate' | 'update_stock_status'
  ids: number[]
  stock_status?: string
}

export class ProductService {
  // 获取商品列表
  static getProductList(params?: ProductListParams) {
    return request.get<{
      list: ProductItem[]
      pagination: {
        current_page: number
        last_page: number
        per_page: number
        total: number
        from: number
        to: number
      }
    }>({
      url: '/api/admin/products',
      params
    })
  }

  // 获取商品详情
  static getProductDetail(id: number) {
    return request.get<ProductItem>({
      url: `/api/admin/products/${id}`
    })
  }

  // 获取商品统计
  static getProductStats() {
    return request.get<ProductStats>({
      url: '/api/admin/products/stats'
    })
  }

  // 获取商品选项数据
  static getProductOptions() {
    return request.get<ProductOptions>({
      url: '/api/admin/products/options'
    })
  }

  // 创建商品
  static createProduct(data: ProductCreateParams) {
    return request.post<ProductItem>({
      url: '/api/admin/products',
      data
    })
  }

  // 更新商品
  static updateProduct(id: number, data: Partial<ProductCreateParams>) {
    return request.put<ProductItem>({
      url: `/api/admin/products/${id}`,
      data
    })
  }

  // 删除商品
  static deleteProduct(id: number) {
    return request.del<any>({
      url: `/api/admin/products/${id}`
    })
  }

  // 切换商品状态（上架/下架）
  static toggleProductStatus(id: number) {
    return request.put<{ id: number; is_active: boolean; status_text: string }>({
      url: `/api/admin/products/${id}/toggle`
    })
  }

  // 批量操作商品
  static batchOperation(data: ProductBatchParams) {
    return request.post<{ success_count: number; fail_count: number }>({
      url: '/api/admin/products/batch',
      data
    })
  }
} 