# 通用界面和结构语法规范

## 项目结构规范

### 后端 (<PERSON><PERSON>)
```
myapp/
├── app/
│   ├── Http/
│   │   ├── Controllers/
│   │   │   └── Api/          # API控制器
│   │   │       ├── AuthController.php
│   │   │       └── UserController.php
│   │   └── Middleware/       # 中间件
│   ├── Models/               # 数据模型
│   └── Services/             # 业务逻辑服务
├── database/
│   ├── migrations/           # 数据库迁移
│   └── seeders/             # 数据填充
├── routes/
│   └── api.php              # API路由定义
└── config/                  # 配置文件
```

### 前端 (Vue3)
```
art-design-pro/
├── src/
│   ├── api/                 # API接口定义
│   ├── views/               # 页面组件
│   │   ├── auth/           # 认证相关页面
│   │   └── system/         # 系统管理页面
│   ├── components/          # 通用组件
│   ├── store/              # 状态管理
│   ├── utils/              # 工具函数
│   │   └── http/           # HTTP请求工具
│   └── types/              # TypeScript类型定义
└── docs/                   # 文档目录
```

## API接口规范

### 响应格式标准
```typescript
// 统一响应格式
interface ApiResponse<T = any> {
  code: number      // 状态码 (200: 成功, 400: 客户端错误, 500: 服务器错误)
  msg: string       // 响应消息
  data: T          // 响应数据
}

// 分页响应格式
interface PaginatedResponse<T> {
  code: number
  msg: string
  data: {
    data: T[]        // 数据列表
    current_page: number
    per_page: number
    total: number
    last_page: number
    from: number
    to: number
  }
}
```

### 请求参数规范
```typescript
// 登录参数
interface LoginParams {
  userName: string   // 用户名或邮箱
  password: string   // 密码
}

// 用户创建参数
interface UserCreateParams {
  name: string       // 用户名
  email: string      // 邮箱
  phone?: string     // 手机号(可选)
  password: string   // 密码
  role: string       // 角色
  status?: number    // 状态 (0: 禁用, 1: 启用)
}

// 用户更新参数
interface UserUpdateParams {
  name: string       // 用户名
  email: string      // 邮箱
  phone?: string     // 手机号(可选)
  role: string       // 角色
  status?: number    // 状态
  password?: string  // 密码(可选，用于密码修改)
}
```

## 前端组件规范

### Vue3 组件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
  // 1. 导入依赖
  import { ref, reactive, computed, watch, nextTick } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  
  // 2. 定义Props和Emits接口
  interface Props {
    visible: boolean
    type: string
    userData?: Partial<User>
  }
  
  interface Emits {
    (e: 'update:visible', value: boolean): void
    (e: 'submit'): void
  }
  
  // 3. 定义props和emits
  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()
  
  // 4. 响应式数据
  const loading = ref(false)
  const formData = reactive({
    name: '',
    email: '',
    phone: '',
    role: 'R_USER',
    status: 1
  })
  
  // 5. 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })
  
  // 6. 方法定义
  const handleSubmit = async () => {
    // 实现逻辑
  }
  
  // 7. 生命周期和监听器
  watch(
    () => props.visible,
    (visible) => {
      if (visible) {
        // 处理逻辑
      }
    }
  )
</script>

<style lang="scss" scoped>
  // 样式定义
</style>
```

### 表单验证规范
```typescript
// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  password: [
    { 
      validator: (rule, value, callback) => {
        if (dialogType.value === 'add' && !value) {
          callback(new Error('请输入密码'))
        } else if (value && value.length < 6) {
          callback(new Error('密码长度不能少于6位'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
}
```

## 后端代码规范

### 控制器结构
```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class UserController extends Controller
{
    /**
     * 获取用户列表
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // 1. 参数验证
            $validator = Validator::make($request->all(), [
                'page' => 'integer|min:1',
                'per_page' => 'integer|min:1|max:100',
                'search' => 'string|max:255'
            ]);
            
            if ($validator->fails()) {
                return response()->json([
                    'code' => 422,
                    'msg' => '参数验证失败',
                    'data' => $validator->errors()
                ], 422);
            }
            
            // 2. 业务逻辑处理
            $users = User::query()
                ->when($request->search, function ($query, $search) {
                    $query->where('name', 'like', "%{$search}%")
                          ->orWhere('email', 'like', "%{$search}%");
                })
                ->paginate($request->get('per_page', 10));
            
            // 3. 返回响应
            return response()->json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $users
            ]);
            
        } catch (\Exception $e) {
            // 4. 错误处理
            return response()->json([
                'code' => 500,
                'msg' => '服务器内部错误',
                'data' => null
            ], 500);
        }
    }
}
```

### 数据验证规范
```php
// 创建用户验证规则
$validator = Validator::make($request->all(), [
    'name' => 'required|string|max:255',
    'email' => 'required|string|email|max:255|unique:users,email',
    'phone' => 'nullable|string|max:20',
    'password' => 'required|string|min:6',
    'role' => 'required|string|in:R_SUPER,R_ADMIN,R_EDITOR,R_USER',
    'status' => 'integer|in:0,1'
]);

// 更新用户验证规则
$validator = Validator::make($request->all(), [
    'name' => 'required|string|max:255',
    'email' => 'required|string|email|max:255|unique:users,email,' . $id,
    'phone' => 'nullable|string|max:20',
    'role' => 'required|string|in:R_SUPER,R_ADMIN,R_EDITOR,R_USER',
    'status' => 'integer|in:0,1',
    'password' => 'nullable|string|min:6'
]);
```

## 数据库设计规范

### 用户表结构
```sql
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    phone VARCHAR(20) NULL,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'R_USER',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '0:禁用 1:启用',
    avatar VARCHAR(255) NULL,
    last_login_at TIMESTAMP NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status)
);
```

### 角色权限定义
```php
// 角色常量定义
const ROLES = [
    'R_SUPER' => '超级管理员',
    'R_ADMIN' => '管理员',
    'R_EDITOR' => '编辑员',
    'R_USER' => '普通用户'
];

// 状态常量定义
const STATUS = [
    0 => '禁用',
    1 => '启用'
];
```

## 错误处理规范

### 前端错误处理
```typescript
// HTTP错误处理
try {
  const response = await UserService.login(loginData)
  
  if (response.code !== 200) {
    throw new Error(response.msg || 'Request failed')
  }
  
  // 处理成功响应
  handleSuccess(response.data)
  
} catch (error) {
  if (error instanceof HttpError) {
    // 处理HTTP错误
    ElMessage.error(error.message)
  } else {
    // 处理其他错误
    ElMessage.error('操作失败，请稍后重试')
    console.error('Unexpected error:', error)
  }
}
```

### 后端错误处理
```php
try {
    // 业务逻辑
    $result = $this->userService->createUser($userData);
    
    return response()->json([
        'code' => 200,
        'msg' => '创建成功',
        'data' => $result
    ]);
    
} catch (ValidationException $e) {
    return response()->json([
        'code' => 422,
        'msg' => '数据验证失败',
        'data' => $e->errors()
    ], 422);
    
} catch (\Exception $e) {
    Log::error('创建用户失败', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
    
    return response()->json([
        'code' => 500,
        'msg' => '服务器内部错误',
        'data' => null
    ], 500);
}
```

## 代码注释规范

### PHP注释规范
```php
/**
 * 创建新用户
 * 
 * @param Request $request 请求对象
 * @return JsonResponse JSON响应
 */
public function store(Request $request): JsonResponse
{
    // 验证请求参数
    $validator = Validator::make($request->all(), [
        'name' => 'required|string|max:255',
        'email' => 'required|string|email|max:255|unique:users'
    ]);
    
    // 创建用户数据
    $userData = [
        'name' => $request->name,
        'email' => $request->email,
        'password' => Hash::make($request->password)
    ];
    
    return response()->json([
        'code' => 200,
        'msg' => '用户创建成功',
        'data' => User::create($userData)
    ]);
}
```

### TypeScript注释规范
```typescript
/**
 * 用户登录处理函数
 * 
 * @description 处理用户登录逻辑，包括表单验证、API调用、状态管理等
 * @returns Promise<void>
 */
const handleSubmit = async (): Promise<void> => {
  // 表单验证
  const valid = await formRef.value?.validate()
  if (!valid) return
  
  // 发送登录请求
  const response = await UserService.login(formData)
  
  // 处理登录响应
  if (response.code === 200) {
    // 存储用户信息和token
    userStore.setToken(response.data.token)
    userStore.setUserInfo(response.data.user)
    
    // 跳转到首页
    router.push('/')
  }
}
```

## 最佳实践

### 1. 数据一致性
- 前后端数据格式保持一致
- 使用TypeScript类型定义确保类型安全
- 统一的错误处理和响应格式

### 2. 安全性
- 所有API接口都要进行认证和授权检查
- 密码使用Hash加密存储
- 输入数据要进行验证和过滤

### 3. 性能优化
- 使用分页查询避免一次性加载大量数据
- 前端使用防抖和节流优化用户体验
- 合理使用缓存机制

### 4. 可维护性
- 代码结构清晰，职责分离
- 充分的注释和文档
- 统一的编码规范和命名约定

### 5. 测试覆盖
- 单元测试覆盖核心业务逻辑
- 集成测试验证API接口
- 端到端测试确保功能完整性 