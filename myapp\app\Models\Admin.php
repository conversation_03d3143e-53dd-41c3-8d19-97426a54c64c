<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log;

class Admin extends User
{
    /**
     * 管理员角色列表
     */
    const ADMIN_ROLES = ['R_SUPER', 'R_ADMIN', 'R_EDITOR'];

    /**
     * 查询范围：只查询管理员用户
     */
    public function scopeAdmins(Builder $query): Builder
    {
        return $query->whereIn('role', self::ADMIN_ROLES);
    }

    /**
     * 查询范围：只查询活跃的管理员用户
     */
    public function scopeActiveAdmins(Builder $query): Builder
    {
        return $query->admins()->where('status', 'active');
    }

    /**
     * 检查是否为超级管理员
     */
    public function isSuperAdmin(): bool
    {
        return $this->role === 'R_SUPER';
    }

    /**
     * 检查是否为管理员
     */
    public function isAdmin(): bool
    {
        return $this->role === 'R_ADMIN';
    }

    /**
     * 检查是否为编辑者
     */
    public function isEditor(): bool
    {
        return $this->role === 'R_EDITOR';
    }

    /**
     * 检查是否有管理员权限
     */
    public function hasAdminRole(): bool
    {
        return in_array($this->role, self::ADMIN_ROLES);
    }

    /**
     * 获取角色显示名称
     */
    public function getRoleDisplayNameAttribute(): string
    {
        return match($this->role) {
            'R_SUPER' => '超级管理员',
            'R_ADMIN' => '管理员',
            'R_EDITOR' => '编辑者',
            default => '未知角色',
        };
    }

    /**
     * 获取权限级别（数字越小权限越高）
     */
    public function getPermissionLevelAttribute(): int
    {
        return match($this->role) {
            'R_SUPER' => 1,
            'R_ADMIN' => 2,
            'R_EDITOR' => 3,
            default => 999,
        };
    }

    /**
     * 检查是否有权限管理指定用户
     */
    public function canManageUser(User $user): bool
    {
        // 超级管理员可以管理所有用户
        if ($this->isSuperAdmin()) {
            return true;
        }

        // 管理员可以管理编辑者和普通用户
        if ($this->isAdmin()) {
            return in_array($user->role, ['R_EDITOR', 'R_USER']);
        }

        // 编辑者只能管理普通用户
        if ($this->isEditor()) {
            return $user->role === 'R_USER';
        }

        return false;
    }
} 