<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class CheckAdminRole
{
    /**
     * 处理传入的请求，检查用户是否有管理员权限
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // 检查用户是否已登录
        if (!Auth::check()) {
            Log::write('CheckAdminRole: 用户未登录，跳转登录页面');
            return redirect()->route('login');
        }

        $user = Auth::user();
        
        // 检查用户角色是否为管理员角色
        $adminRoles = ['R_SUPER', 'R_ADMIN', 'R_EDITOR'];
        
        if (!in_array($user->role, $adminRoles)) {
            Log::write('CheckAdminRole: 用户角色不符合管理员要求，当前角色：' . $user->role);
            
            // 如果是普通用户，重定向到前端首页
            if ($user->role === 'R_USER') {
                return redirect()->route('home')->with('error', '您没有权限访问后台管理系统');
            }
            
            // 其他情况返回403错误
            abort(403, '您没有权限访问此页面');
        }

        Log::write('CheckAdminRole: 用户角色验证通过，用户角色：' . $user->role);
        return $next($request);
    }
} 