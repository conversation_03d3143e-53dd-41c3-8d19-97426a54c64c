@echo off
chcp 65001 >nul
echo ===========================================
echo PHP路径设置和用户账号创建脚本
echo ===========================================
echo.

REM 设置PHP路径到当前会话
set "PHP_PATH=D:\phpstudy_pro\Extensions\php\php8.1.9nts"
set "PATH=%PHP_PATH%;%PATH%"

echo 正在设置PHP路径: %PHP_PATH%
echo.

REM 测试PHP是否可用
echo 测试PHP版本:
"%PHP_PATH%\php.exe" -v
if %errorlevel% neq 0 (
    echo PHP路径设置失败，请检查phpstudy安装路径
    echo 请手动确认以下路径是否正确：
    echo %PHP_PATH%
    pause
    exit /b 1
)
echo.

REM 进入项目目录
cd /d "%~dp0myapp"
echo 已进入Laravel项目目录: %CD%
echo.

REM 清除缓存
echo 清除Laravel缓存:
"%PHP_PATH%\php.exe" artisan cache:clear
"%PHP_PATH%\php.exe" artisan config:clear
"%PHP_PATH%\php.exe" artisan route:clear
echo.

REM 运行数据库迁移
echo 运行数据库迁移:
"%PHP_PATH%\php.exe" artisan migrate --force
echo.

REM 创建默认用户账号
echo 创建默认用户账号:
echo.

echo 1. 创建超级管理员账号...
"%PHP_PATH%\php.exe" artisan tinker --execute="use App\Models\User; try { User::create(['name' => '超级管理员', 'email' => '<EMAIL>', 'password' => bcrypt('admin123'), 'role' => 'R_SUPER', 'status' => 'active', 'email_verified_at' => now()]); echo '超级管理员账号创建成功'; } catch (Exception $e) { echo '超级管理员账号可能已存在: ' . $e->getMessage(); }"
echo.

echo 2. 创建普通管理员账号...
"%PHP_PATH%\php.exe" artisan tinker --execute="use App\Models\User; try { User::create(['name' => '管理员', 'email' => '<EMAIL>', 'password' => bcrypt('manager123'), 'role' => 'R_ADMIN', 'status' => 'active', 'email_verified_at' => now()]); echo '管理员账号创建成功'; } catch (Exception $e) { echo '管理员账号可能已存在: ' . $e->getMessage(); }"
echo.

echo 3. 创建前端测试用户...
"%PHP_PATH%\php.exe" artisan tinker --execute="use App\Models\User; try { User::create(['name' => '测试用户', 'email' => '<EMAIL>', 'password' => bcrypt('user123'), 'role' => 'R_USER', 'status' => 'active', 'email_verified_at' => now()]); echo '前端用户账号创建成功'; } catch (Exception $e) { echo '前端用户账号可能已存在: ' . $e->getMessage(); }"
echo.

echo ===========================================
echo 账号创建完成！登录信息如下：
echo ===========================================
echo.
echo 【后台管理系统登录】- 访问: http://localhost:8000/admin
echo 1. 超级管理员:
echo    邮箱: <EMAIL>
echo    密码: admin123
echo.
echo 2. 普通管理员:
echo    邮箱: <EMAIL>  
echo    密码: manager123
echo.
echo 【前端用户登录】- 访问: http://localhost:8000/login
echo 3. 前端用户:
echo    邮箱: <EMAIL>
echo    密码: user123
echo.
echo ===========================================
echo 启动开发服务器
echo ===========================================
echo.
echo 请在浏览器中访问: 
echo - 前端首页: http://localhost:8000
echo - 后台管理: http://localhost:8000/admin
echo.
echo 按Ctrl+C停止服务器
echo.
"%PHP_PATH%\php.exe" artisan serve --host=0.0.0.0 --port=8000

pause 