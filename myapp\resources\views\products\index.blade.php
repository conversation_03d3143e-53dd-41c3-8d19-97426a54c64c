<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ __('app.products.title', ['name' => __('app.nav.product_alert_system')]) }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        body { 
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif; 
            background: linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e1b4b 100%);
            min-height: 100vh;
        }
        .card-hover { 
            transition: all 0.3s ease; 
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .card-hover:hover { 
            transform: translateY(-5px); 
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
            border-color: rgba(59, 130, 246, 0.5);
        }
        .filter-dropdown {
            max-height: 200px;
            overflow-y: auto;
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .glass-card:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(59, 130, 246, 0.5);
        }
        .product-image-glow {
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .search-gradient {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
        }
    </style>
</head>
<body class="text-white">
    <!-- 导航栏 -->
    <nav class="bg-black/20 backdrop-blur-md border-b border-white/10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-8">
                    <a href="/" class="text-xl font-bold text-white">{{ __('app.nav.product_alert_system') }}</a>
                    <nav class="hidden md:flex space-x-8">
                        <a href="/" class="text-gray-300 hover:text-white transition-colors">{{ __('app.nav.home') }}</a>
                        <a href="{{ route('products.index') }}" class="text-blue-400 font-semibold">{{ __('app.nav.browse_products') }}</a>
                        @auth
                            <a href="{{ route('history') }}" class="text-gray-300 hover:text-white transition-colors">{{ __('app.nav.my_follows') }}</a>
                        @endauth
                    </nav>
                </div>
                <div class="flex items-center space-x-4">
                    @auth
                        <a href="/dashboard" class="text-gray-300 hover:text-white transition-colors">{{ __('app.nav.dashboard') }}</a>
                        <form action="/logout" method="POST" class="inline">
                            @csrf
                            <button type="submit" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">{{ __('app.nav.logout') }}</button>
                        </form>
                    @else
                        <a href="/subscribe" class="text-gray-300 hover:text-white transition-colors">{{ __('app.nav.subscribe') }}</a>
                        <a href="/login" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">{{ __('app.nav.login') }}</a>
                    @endauth
                    
                    <!-- 语言切换按钮 -->
                    @include('components.language-switcher')
                </div>
            </div>
        </div>
    </nav>

    <!-- 页面标题 -->
    <div class="bg-black/10 backdrop-blur-sm border-b border-white/10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center">
                <h1 class="text-3xl font-bold text-white mb-2">{{ __('app.products.title', ['name' => __('app.nav.product_alert_system')]) }}</h1>
                <p class="text-lg text-gray-300">{{ __('app.products.subtitle') }}</p>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 错误信息 -->
        @if(session('error'))
            <div class="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-6 backdrop-blur-sm">
                <p class="text-red-200">{{ session('error') }}</p>
            </div>
        @endif

        <!-- 搜索和筛选区域 -->
        <div class="glass-card search-gradient rounded-2xl p-6 mb-8">
            <form method="GET" action="{{ route('products.index') }}" class="space-y-4">
                <!-- 搜索框 -->
                <div class="flex flex-col md:flex-row gap-4">
                    <div class="flex-1">
                        <input type="text" 
                               name="search" 
                               value="{{ request('search') }}" 
                               placeholder="{{ __('app.products.search_placeholder') }}" 
                               class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent backdrop-blur-sm">
                    </div>
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                        {{ __('app.products.search_button') }}
                    </button>
                </div>

                <!-- 筛选选项 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- 分类筛选 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">{{ __('app.products.category') }}</label>
                        <select name="category" class="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:ring-2 focus:ring-blue-500 backdrop-blur-sm">
                            <option value="" class="text-gray-900">{{ __('app.products.all_categories') }}</option>
                            @foreach($categories as $category)
                                <option value="{{ $category }}" {{ request('category') == $category ? 'selected' : '' }} class="text-gray-900">
                                    {{ $category }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- 品牌筛选 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">{{ __('app.products.brand') }}</label>
                        <select name="brand" class="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:ring-2 focus:ring-blue-500 backdrop-blur-sm">
                            <option value="" class="text-gray-900">{{ __('app.products.all_brands') }}</option>
                            @foreach($brands as $brand)
                                <option value="{{ $brand }}" {{ request('brand') == $brand ? 'selected' : '' }} class="text-gray-900">
                                    {{ $brand }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- 地区筛选 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">{{ __('app.products.region') }}</label>
                        <select name="region_id" class="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:ring-2 focus:ring-blue-500 backdrop-blur-sm">
                            <option value="" class="text-gray-900">{{ __('app.products.all_regions') }}</option>
                            @foreach($regions as $region)
                                <option value="{{ $region->id }}" {{ request('region_id') == $region->id ? 'selected' : '' }} class="text-gray-900">
                                    {{ $region->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- 排序 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">{{ __('app.products.sort_by') }}</label>
                        <select name="sort_by" class="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:ring-2 focus:ring-blue-500 backdrop-blur-sm">
                            <option value="created_at" {{ request('sort_by', 'created_at') == 'created_at' ? 'selected' : '' }} class="text-gray-900">{{ __('app.products.latest_listing') }}</option>
                            <option value="price_asc" {{ request('sort_by') == 'price_asc' ? 'selected' : '' }} class="text-gray-900">{{ __('app.products.price_low_to_high') }}</option>
                            <option value="price_desc" {{ request('sort_by') == 'price_desc' ? 'selected' : '' }} class="text-gray-900">{{ __('app.products.price_high_to_low') }}</option>
                            <option value="popularity" {{ request('sort_by') == 'popularity' ? 'selected' : '' }} class="text-gray-900">{{ __('app.products.most_popular') }}</option>
                            <option value="name" {{ request('sort_by') == 'name' ? 'selected' : '' }} class="text-gray-900">{{ __('app.products.name_sort') }}</option>
                        </select>
                    </div>
                </div>

                <!-- 价格范围 -->
                <div class="flex items-center space-x-4">
                    <span class="text-sm font-medium text-gray-300">{{ __('app.products.price_range') }}</span>
                    <input type="number" 
                           name="price_min" 
                           value="{{ request('price_min') }}" 
                           placeholder="{{ __('app.products.min_price') }}" 
                           class="px-3 py-2 bg-white/10 border border-white/20 rounded-lg w-32 text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 backdrop-blur-sm">
                    <span class="text-gray-400">-</span>
                    <input type="number" 
                           name="price_max" 
                           value="{{ request('price_max') }}" 
                           placeholder="{{ __('app.products.max_price') }}" 
                           class="px-3 py-2 bg-white/10 border border-white/20 rounded-lg w-32 text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 backdrop-blur-sm">
                    <a href="{{ route('products.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                        {{ __('app.products.clear_filter') }}
                    </a>
                </div>
            </form>
        </div>

        <!-- 商品统计 -->
        <div class="flex justify-between items-center mb-6">
            <div>
                <p class="text-gray-300">
                    {{ __('app.products.total_products', ['total' => $products->total()]) }}
                    @if(request()->hasAny(['search', 'category', 'brand', 'region_id', 'price_min', 'price_max']))
                        {{ __('app.products.filtered') }}
                    @endif
                </p>
            </div>
            <div class="text-sm text-gray-400">
                {{ __('app.products.display_range', ['first' => $products->firstItem() ?? 0, 'last' => $products->lastItem() ?? 0]) }}
            </div>
        </div>

        <!-- 商品网格 -->
        @if($products->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
                @foreach($products as $product)
                    <div class="card-hover glass-card rounded-2xl overflow-hidden">
                        <!-- 商品图片 -->
                        <div class="aspect-w-1 aspect-h-1 bg-gray-800 relative">
                            <img src="{{ $product->image_url ?: 'https://via.placeholder.com/300x300' }}" 
                                 alt="{{ $product->name }}" 
                                 class="w-full h-48 object-cover product-image-glow">
                            <!-- 关注状态标识 -->
                            @auth
                                @if(in_array($product->id, $followedProductIds))
                                    <div class="absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                                        {{ __('app.products.followed') }}
                                    </div>
                                @endif
                            @endauth
                            <!-- 折扣标识 -->
                            @if($product->has_discount)
                                <div class="absolute top-2 left-2 bg-gradient-to-r from-red-500 to-pink-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                                    -{{ $product->discount_percentage }}%
                                </div>
                            @endif
                        </div>

                        <!-- 商品信息 -->
                        <div class="p-4">
                            <div class="mb-2">
                                <h3 class="font-semibold text-white line-clamp-2 text-sm">{{ $product->name }}</h3>
                                <p class="text-xs text-gray-400 mt-1">{{ $product->brand }}</p>
                            </div>

                            <!-- 价格 -->
                            <div class="mb-3">
                                <div class="flex items-center space-x-2">
                                    <span class="text-lg font-bold text-green-400">¥{{ number_format($product->price, 2) }}</span>
                                    @if($product->original_price && $product->original_price > $product->price)
                                        <span class="text-sm text-gray-500 line-through">¥{{ number_format($product->original_price, 2) }}</span>
                                    @endif
                                </div>
                                <p class="text-xs text-gray-400">{{ $product->followers_count }} {{ __('app.products.followers') }}</p>
                            </div>

                            <!-- 标签 -->
                            <div class="flex flex-wrap gap-1 mb-3">
                                <span class="bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full text-xs backdrop-blur-sm">{{ $product->category }}</span>
                                @if($product->region)
                                    <span class="bg-green-500/20 text-green-300 px-2 py-1 rounded-full text-xs backdrop-blur-sm">{{ $product->region->name }}</span>
                                @endif
                                @if($product->stock_status == 'in_stock')
                                    <span class="bg-green-500/20 text-green-300 px-2 py-1 rounded-full text-xs backdrop-blur-sm">{{ __('app.products.in_stock') }}</span>
                                @elseif($product->stock_status == 'low_stock')
                                    <span class="bg-yellow-500/20 text-yellow-300 px-2 py-1 rounded-full text-xs backdrop-blur-sm">{{ __('app.products.low_stock') }}</span>
                                @else
                                    <span class="bg-red-500/20 text-red-300 px-2 py-1 rounded-full text-xs backdrop-blur-sm">{{ __('app.products.out_of_stock') }}</span>
                                @endif
                            </div>

                            <!-- 操作按钮 -->
                            <div class="flex space-x-2">
                                <a href="{{ route('products.show', $product->id) }}" 
                                   class="flex-1 bg-blue-500 hover:bg-blue-600 text-white text-center py-2 px-3 rounded-lg text-sm transition-all duration-300 transform hover:scale-105 font-semibold">
                                    {{ __('app.products.view_details') }}
                                </a>
                                @auth
                                    @if(in_array($product->id, $followedProductIds))
                                        <button onclick="unfollowProduct({{ $product->id }})" 
                                                class="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-lg text-sm transition-all duration-300 transform hover:scale-105 font-semibold">
                                            {{ __('app.products.unfollow') }}
                                        </button>
                                    @else
                                        <button onclick="followProduct({{ $product->id }})" 
                                                class="bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-lg text-sm transition-all duration-300 transform hover:scale-105 font-semibold">
                                            {{ __('app.products.follow') }}
                                        </button>
                                    @endif
                                @else
                                    <a href="/login" class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-lg text-sm font-semibold">
                                        {{ __('app.products.login') }}
                                    </a>
                                @endauth
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- 分页 -->
            <div class="flex justify-center">
                <div class="bg-white/5 backdrop-blur-sm rounded-lg p-2">
                    {{ $products->appends(request()->query())->links() }}
                </div>
            </div>
        @else
            <!-- 空状态 -->
            <div class="text-center py-16">
                <div class="glass-card rounded-2xl p-8 max-w-md mx-auto">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5"></path>
                    </svg>
                    <h3 class="text-lg font-semibold text-white mb-2">{{ __('app.products.no_products') }}</h3>
                    <p class="text-gray-300 mb-4">
                        @if(request()->hasAny(['search', 'category', 'brand', 'region_id', 'price_min', 'price_max']))
                            {{ __('app.products.no_products_filtered') }}
                        @else
                            {{ __('app.products.no_products_update') }}
                        @endif
                    </p>
                    @if(request()->hasAny(['search', 'category', 'brand', 'region_id', 'price_min', 'price_max']))
                        <a href="{{ route('products.index') }}" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                            {{ __('app.products.view_all_products') }}
                        </a>
                    @endif
                </div>
            </div>
        @endif
    </main>

    <!-- JavaScript -->
    <script>
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        // 关注商品
        function followProduct(productId) {
            fetch(`/products/${productId}/follow`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify({
                    email_notifications: true,
                    wechat_notifications: false
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert(data.message || '{{ __('app.products.follow_error') }}');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('{{ __('app.products.follow_error') }}');
            });
        }

        // 取消关注商品
        function unfollowProduct(productId) {
            if (!confirm('{{ __('app.products.unfollow_confirm') }}')) {
                return;
            }

            fetch(`/history/products/${productId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': csrfToken
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('{{ __('app.products.unfollowed') }}');
                    location.reload();
                } else {
                    alert(data.message || '{{ __('app.products.unfollow_error') }}');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('{{ __('app.products.unfollow_error') }}');
            });
        }

        // 自动提交表单（搜索和筛选）
        document.querySelectorAll('select[name="category"], select[name="brand"], select[name="region_id"], select[name="sort_by"]').forEach(select => {
            select.addEventListener('change', function() {
                this.form.submit();
            });
        });
    </script>
</body>
</html> 