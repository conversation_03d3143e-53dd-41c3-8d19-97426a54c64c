@echo off
chcp 65001 >nul
echo.
echo =============================================
echo    启动FilamentPHP管理后台 (PHPStudy环境)
echo =============================================
echo.

:: 设置PHPStudy的PHP路径
set PHP_PATH=D:\phpstudy_pro\Extensions\php\php8.0.2nts\php.exe

:: 检查是否在正确的目录
if not exist "myapp\artisan" (
    echo ❌ 错误: 请在Laravel项目根目录运行此脚本
    echo 当前目录: %cd%
    echo 应该包含myapp文件夹
    pause
    exit /b 1
)

:: 进入Laravel项目目录
cd myapp

echo 📁 项目目录: %cd%
echo.

:: 检查FilamentPHP是否已安装
if not exist "vendor\filament" (
    echo ❌ 错误: FilamentPHP未安装
    echo 💡 请先运行 install_filament_phpstudy.bat 安装FilamentPHP
    pause
    exit /b 1
)

:: 显示启动信息
echo ==========================================
echo           服务器启动信息
echo ==========================================
echo.
echo 🌐 管理后台地址: http://localhost:8000/admin
echo 🏠 前端首页地址: http://localhost:8000
echo 📋 测试页面地址: http://localhost:8000/test
echo.
echo 💡 提示:
echo    - 按 Ctrl+C 停止服务器
echo    - 管理后台需要登录才能访问
echo    - FilamentPHP版本: 2.17 (兼容PHP 8.0)
echo.
echo ==========================================
echo.

:: 启动Laravel开发服务器
echo 🚀 启动Laravel开发服务器...
echo.
%PHP_PATH% artisan serve --host=127.0.0.1 --port=8000

echo.
echo 📴 服务器已停止
pause 