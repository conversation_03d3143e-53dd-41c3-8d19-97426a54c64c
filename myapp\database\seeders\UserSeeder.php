<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $now = Carbon::now();
        
        $users = [
            [
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'phone' => '13800138001',
                'email_verified_at' => $now,
                'password' => Hash::make('123456'),
                'role' => 'R_SUPER',
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Admin',
                'email' => '<EMAIL>',
                'phone' => '13800138002',
                'email_verified_at' => $now,
                'password' => Hash::make('123456'),
                'role' => 'R_ADMIN',
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Editor',
                'email' => '<EMAIL>',
                'phone' => '13800138003',
                'email_verified_at' => $now,
                'password' => Hash::make('123456'),
                'role' => 'R_EDITOR',
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Normal User',
                'email' => '<EMAIL>',
                'phone' => '13800138004',
                'email_verified_at' => $now,
                'password' => Hash::make('123456'),
                'role' => 'R_USER',
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'phone' => '13800138005',
                'email_verified_at' => $now,
                'password' => Hash::make('123456'),
                'role' => 'R_USER',
                'status' => 0, // 禁用状态的测试用户
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ];

        // 清空现有用户（可选）
        DB::table('users')->truncate();
        
        // 插入测试用户
        DB::table('users')->insert($users);
        
        echo "Created " . count($users) . " test users:\n";
        foreach ($users as $user) {
            echo "- {$user['name']} ({$user['email']}) - Role: {$user['role']}, Status: " . ($user['status'] ? 'Active' : 'Inactive') . ", Phone: {$user['phone']}\n";
        }
    }
} 