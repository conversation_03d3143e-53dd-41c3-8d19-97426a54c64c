<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Region;
use App\Models\Plan;
use App\Models\Product;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run(): void
    {
        // 调用用户种子数据
        $this->call([
            UserSeeder::class,
            BannerSeeder::class,
        ]);

        // 创建测试用户
        User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'phone' => '13800138001',
            'password' => Hash::make('123456'),
            'role' => 'R_SUPER',
            'status' => 'active', // 使用正确的枚举值
        ]);

        User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'phone' => '13800138002',
            'password' => Hash::make('123456'),
            'role' => 'R_ADMIN',
            'status' => 'active', // 使用正确的枚举值
        ]);

        User::create([
            'name' => 'Editor',
            'email' => '<EMAIL>',
            'phone' => '13800138003',
            'password' => Hash::make('123456'),
            'role' => 'R_EDITOR',
            'status' => 'active', // 使用正确的枚举值
        ]);

        User::create([
            'name' => 'Normal User',
            'email' => '<EMAIL>',
            'phone' => '13800138004',
            'password' => Hash::make('123456'),
            'role' => 'R_USER',
            'status' => 'active', // 使用正确的枚举值
        ]);

        // 创建地区数据
        $china = Region::create([
            'name' => '中国',
            'code' => 'CN',
            'type' => 'country',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        $beijing = Region::create([
            'name' => '北京',
            'code' => 'BJ',
            'type' => 'state',
            'parent_id' => $china->id,
            'is_active' => true,
            'sort_order' => 1,
        ]);

        $shanghai = Region::create([
            'name' => '上海',
            'code' => 'SH',
            'type' => 'state',
            'parent_id' => $china->id,
            'is_active' => true,
            'sort_order' => 2,
        ]);

        // 创建套餐数据
        Plan::create([
            'name' => 'Basic',
            'slug' => 'basic',
            'description' => '基础套餐，适合个人用户',
            'price' => 9.99,
            'currency' => 'CNY',
            'billing_cycle' => 'monthly',
            'duration_days' => 30,
            'max_products' => 10,
            'max_notifications' => 100,
            'features' => json_encode(['商品监控', '价格提醒', '邮件通知']),
            'is_popular' => false,
            'is_active' => true,
            'sort_order' => 1,
        ]);

        Plan::create([
            'name' => 'Professional',
            'slug' => 'professional',
            'description' => '专业套餐，适合商业用户',
            'price' => 19.99,
            'currency' => 'CNY',
            'billing_cycle' => 'monthly',
            'duration_days' => 30,
            'max_products' => 50,
            'max_notifications' => 500,
            'features' => json_encode(['商品监控', '价格提醒', '库存通知', '邮件通知', '微信通知', '24小时客服']),
            'is_popular' => true,
            'is_active' => true,
            'sort_order' => 2,
        ]);

        Plan::create([
            'name' => 'Enterprise',
            'slug' => 'enterprise',
            'description' => '企业套餐，适合大型企业',
            'price' => 49.99,
            'currency' => 'CNY',
            'billing_cycle' => 'monthly',
            'duration_days' => 30,
            'max_products' => 0, // 无限制
            'max_notifications' => 0, // 无限制
            'features' => json_encode(['无限商品监控', '价格提醒', '库存通知', '邮件通知', '微信通知', 'API接口', '专属客服', '数据报表']),
            'is_popular' => false,
            'is_active' => true,
            'sort_order' => 3,
        ]);

        // 创建测试商品数据
        Product::create([
            'name' => 'iPhone 15 Pro',
            'sku' => 'IPHONE15PRO001',
            'description' => '苹果iPhone 15 Pro 256GB 深空黑色',
            'brand' => 'Apple',
            'category' => '手机',
            'price' => 8999.00,
            'original_price' => 9999.00,
            'currency' => 'CNY',
            'stock_quantity' => 50,
            'stock_status' => 'in_stock',
            'image_url' => 'https://example.com/iphone15pro.jpg',
            'product_url' => 'https://www.apple.com.cn/iphone-15-pro/',
            'region_id' => $china->id,
            'is_active' => true,
            'last_updated_at' => now(),
        ]);

        Product::create([
            'name' => 'MacBook Pro 14',
            'sku' => 'MBP14M3001',
            'description' => 'MacBook Pro 14英寸 M3芯片 512GB SSD',
            'brand' => 'Apple',
            'category' => '笔记本电脑',
            'price' => 15999.00,
            'original_price' => 16999.00,
            'currency' => 'CNY',
            'stock_quantity' => 0,
            'stock_status' => 'out_of_stock',
            'image_url' => 'https://example.com/macbookpro14.jpg',
            'product_url' => 'https://www.apple.com.cn/macbook-pro/',
            'region_id' => $china->id,
            'is_active' => true,
            'last_updated_at' => now(),
        ]);

        Product::create([
            'name' => 'iPad Air',
            'sku' => 'IPADAIR2024001',
            'description' => 'iPad Air 11英寸 M2芯片 256GB WiFi版',
            'brand' => 'Apple',
            'category' => '平板电脑',
            'price' => 4999.00,
            'original_price' => 4999.00,
            'currency' => 'CNY',
            'stock_quantity' => 100,
            'stock_status' => 'in_stock',
            'image_url' => 'https://example.com/ipadair.jpg',
            'product_url' => 'https://www.apple.com.cn/ipad-air/',
            'region_id' => $china->id,
            'is_active' => true,
            'last_updated_at' => now(),
        ]);
    }
}
