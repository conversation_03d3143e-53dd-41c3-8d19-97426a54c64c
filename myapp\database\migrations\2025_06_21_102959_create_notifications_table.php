<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id'); // 用户ID
            $table->unsignedBigInteger('product_id')->nullable(); // 商品ID（可选）
            $table->string('type'); // 通知类型：stock_alert, price_change, new_product等
            $table->string('channel'); // 通知渠道：email, wechat, sms
            $table->string('title'); // 通知标题
            $table->text('content'); // 通知内容
            $table->json('data')->nullable(); // 额外数据
            $table->enum('status', ['pending', 'sent', 'failed', 'cancelled'])->default('pending'); // 发送状态
            $table->timestamp('sent_at')->nullable(); // 发送时间
            $table->text('error_message')->nullable(); // 错误信息
            $table->integer('retry_count')->default(0); // 重试次数
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
            $table->index(['user_id', 'status']);
            $table->index(['type', 'status']);
            $table->index(['status', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('notifications');
    }
};
