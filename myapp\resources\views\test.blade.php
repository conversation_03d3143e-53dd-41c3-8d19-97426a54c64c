<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <!-- 页面基本信息设置 -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面 - Lara<PERSON> HTML展示</title>
    
    <!-- 内联CSS样式 - 让页面更美观 -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 50px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 600px;
            width: 90%;
        }
        
        .title {
            font-size: 3rem;
            margin-bottom: 20px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .subtitle {
            font-size: 1.5rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .content {
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.8;
        }
        
        .emoji {
            font-size: 4rem;
            margin: 20px 0;
        }
        
        .info {
            font-size: 1rem;
            opacity: 0.7;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .pulse {
            animation: pulse 2s ease-in-out infinite alternate;
        }
        
        @keyframes pulse {
            from { transform: scale(1); }
            to { transform: scale(1.05); }
        }
        
        .fixed-notice {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #4caf50;
        }
    </style>
</head>
<body>
    <!-- 主要内容容器 -->
    <div class="container">
        <!-- 修复通知 -->
        <div class="fixed-notice">
            <p>✅ <strong>路由问题已修复！</strong></p>
            <p>Laravel正在从正确的目录加载路由文件</p>
        </div>
        
        <!-- 主标题 -->
        <h1 class="title pulse">✨ 测试页面 ✨</h1>
        
        <!-- 副标题 -->
        <h2 class="subtitle">Laravel HTML 页面展示</h2>
        
        <!-- 表情符号装饰 -->
        <div class="emoji">🚀</div>
        
        <!-- 主要内容文字 -->
        <div class="content">
            <p>这是一个简单的HTML页面用于测试展示。</p>
            <p>页面成功加载，Laravel路由工作正常！</p>
            <p>所有功能都运行正常 ✅</p>
        </div>
        
        <!-- 页面信息 -->
        <div class="info">
            <p>🕒 当前时间: {{ date('Y-m-d H:i:s') }}</p>
            <p>🌐 访问地址: {{ request()->url() }}</p>
            <p>💻 Laravel版本: {{ app()->version() }}</p>
            <p>📁 项目目录: myapp</p>
        </div>
    </div>
</body>
</html> 