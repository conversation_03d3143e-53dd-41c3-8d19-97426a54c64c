<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // 商品名称
            $table->string('sku')->unique(); // 商品SKU
            $table->text('description')->nullable(); // 商品描述
            $table->string('brand')->nullable(); // 品牌
            $table->string('category')->nullable(); // 分类
            $table->decimal('price', 10, 2)->nullable(); // 当前价格
            $table->decimal('original_price', 10, 2)->nullable(); // 原价
            $table->string('currency', 3)->default('USD'); // 货币
            $table->integer('stock_quantity')->default(0); // 库存数量
            $table->enum('stock_status', ['in_stock', 'out_of_stock', 'discontinued'])->default('in_stock'); // 库存状态
            $table->string('image_url')->nullable(); // 商品图片URL
            $table->string('product_url')->nullable(); // 商品链接
            $table->unsignedBigInteger('region_id'); // 所属地区
            $table->json('attributes')->nullable(); // 商品属性JSON
            $table->boolean('is_active')->default(true); // 是否启用
            $table->timestamp('last_updated_at')->nullable(); // 最后更新时间
            $table->timestamps();

            $table->foreign('region_id')->references('id')->on('regions');
            $table->index(['region_id', 'is_active']);
            $table->index(['stock_status', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('products');
    }
};
