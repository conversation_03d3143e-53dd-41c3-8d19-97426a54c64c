<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('regions', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // 地区名称，如 "United States", "California"
            $table->string('code', 10)->unique(); // 地区代码，如 "US", "CA"
            $table->string('type')->default('country'); // 类型：country, state, city
            $table->unsignedBigInteger('parent_id')->nullable(); // 父级地区ID
            $table->boolean('is_active')->default(true); // 是否启用
            $table->integer('sort_order')->default(0); // 排序
            $table->text('description')->nullable(); // 描述
            $table->timestamps();

            $table->index(['type', 'is_active']);
            $table->foreign('parent_id')->references('id')->on('regions')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('regions');
    }
};
