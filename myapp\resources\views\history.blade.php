<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ __('app.history.title', ['name' => __('app.nav.product_alert_system')]) }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        body { 
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif; 
            background: linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e1b4b 100%);
            min-height: 100vh;
        }
        .gradient-bg { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
        }
        .product-card { 
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        .product-card:hover { 
            transform: translateY(-5px); 
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
            background: rgba(255, 255, 255, 0.1);
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .glass-card:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(59, 130, 246, 0.5);
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.15);
        }
        .form-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(16px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .input-glass {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
        }
        .input-glass::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        .input-glass:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(59, 130, 246, 0.5);
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
        }
        .notification-card {
            background: rgba(255, 255, 255, 0.03);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.08);
        }
        .notification-card:hover {
            background: rgba(255, 255, 255, 0.08);
        }
    </style>
</head>
<body class="text-white">
    <!-- 导航栏 -->
    <nav class="bg-black/20 backdrop-blur-md border-b border-white/10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="/" class="text-xl font-bold text-white">{{ __('app.nav.product_alert_system') }}</a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="/" class="text-gray-300 hover:text-white transition-colors">{{ __('app.nav.back_to_home') }}</a>
                    @auth
                        <a href="/dashboard" class="text-gray-300 hover:text-white transition-colors">{{ __('app.nav.dashboard') }}</a>
                        <form action="/logout" method="POST" class="inline">
                            @csrf
                            <button type="submit" class="text-gray-300 hover:text-white transition-colors">{{ __('app.nav.logout') }}</button>
                        </form>
                    @else
                        <a href="/subscribe" class="text-gray-300 hover:text-white transition-colors">{{ __('app.nav.subscribe') }}</a>
                        <a href="/login" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">{{ __('app.nav.login') }}</a>
                    @endauth
                    
                    <!-- 语言切换按钮 -->
                    @include('components.language-switcher')
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 错误消息 -->
        @if(session('error'))
            <div class="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-6 backdrop-blur-sm">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-red-200">{{ session('error') }}</p>
                    </div>
                </div>
            </div>
        @endif

        <!-- 页面标题和筛选 -->
        <div class="mb-8">
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-white mb-4">{{ __('app.history.title', ['name' => __('app.nav.product_alert_system')]) }}</h1>
                <p class="text-xl text-gray-300">{{ __('app.history.description') }}</p>
            </div>

            <!-- 筛选和搜索表单 -->
            <form method="GET" action="{{ route('history') }}" class="form-card rounded-2xl p-6 mb-6">
                <div class="grid md:grid-cols-4 gap-4">
                    <!-- 分类筛选 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">{{ __('app.history.category') }}</label>
                        <select name="category" class="input-glass w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">{{ __('app.history.all_categories') }}</option>
                            <option value="electronics" {{ request('category') == 'electronics' ? 'selected' : '' }}>{{ __('app.history.electronics') }}</option>
                            <option value="fashion" {{ request('category') == 'fashion' ? 'selected' : '' }}>{{ __('app.history.fashion') }}</option>
                            <option value="home" {{ request('category') == 'home' ? 'selected' : '' }}>{{ __('app.history.home') }}</option>
                            <option value="sports" {{ request('category') == 'sports' ? 'selected' : '' }}>{{ __('app.history.sports') }}</option>
                        </select>
                    </div>

                    <!-- 状态筛选 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">{{ __('app.history.status') }}</label>
                        <select name="status" class="input-glass w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">{{ __('app.history.all_statuses') }}</option>
                            <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>{{ __('app.history.active') }}</option>
                            <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>{{ __('app.history.inactive') }}</option>
                            <option value="out_of_stock" {{ request('status') == 'out_of_stock' ? 'selected' : '' }}>{{ __('app.history.out_of_stock') }}</option>
                        </select>
                    </div>

                    <!-- 通知类型筛选 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">{{ __('app.history.notification_type') }}</label>
                        <select name="notification_type" class="input-glass w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">{{ __('app.history.all_types') }}</option>
                            <option value="price_drop" {{ request('notification_type') == 'price_drop' ? 'selected' : '' }}>{{ __('app.history.price_drop') }}</option>
                            <option value="back_in_stock" {{ request('notification_type') == 'back_in_stock' ? 'selected' : '' }}>{{ __('app.history.back_in_stock') }}</option>
                            <option value="new_product" {{ request('notification_type') == 'new_product' ? 'selected' : '' }}>{{ __('app.history.new_product') }}</option>
                        </select>
                    </div>

                    <!-- 搜索 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">{{ __('app.history.search') }}</label>
                        <input 
                            type="text" 
                            name="search"
                            value="{{ request('search') }}"
                            placeholder="{{ __('app.history.search_placeholder') }}"
                            class="input-glass w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                    </div>
                </div>

                <div class="mt-4 flex justify-between items-center">
                    <div class="text-sm text-gray-300">
                        共关注 <span class="font-semibold text-blue-400">{{ $followedProducts->total() }}</span> 个商品
                    </div>
                    <div class="flex space-x-2">
                        <a href="{{ route('history') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                            {{ __('app.history.clear_filter') }}
                        </a>
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                            {{ __('app.history.apply_filter') }}
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- 统计卡片 -->
        <div class="grid md:grid-cols-4 gap-6 mb-8">
            <div class="stat-card rounded-2xl p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                        <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-300">{{ __('app.history.followed_products') }}</p>
                        <p class="text-2xl font-bold text-white">{{ $followedProducts->total() }}</p>
                    </div>
                </div>
            </div>

            <div class="stat-card rounded-2xl p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                        <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5-5-5h5V4h5v13z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-300">{{ __('app.history.received_notifications') }}</p>
                        <p class="text-2xl font-bold text-white">{{ $notifications->count() }}</p>
                    </div>
                </div>
            </div>

            <div class="stat-card rounded-2xl p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                        <svg class="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-300">{{ __('app.history.popular_products') }}</p>
                        <p class="text-2xl font-bold text-white">{{ $popularProducts->count() }}</p>
                    </div>
                </div>
            </div>

            <div class="stat-card rounded-2xl p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-orange-500/20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                        <svg class="w-6 h-6 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-300">{{ __('app.history.today_active') }}</p>
                        <p class="text-2xl font-bold text-white">{{ $notifications->where('created_at', '>=', today())->count() }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 关注的商品列表 -->
        @if($followedProducts->count() > 0)
            <div class="glass-card rounded-2xl mb-8">
                <div class="px-6 py-4 border-b border-white/10">
                    <h2 class="text-lg font-semibold text-white">{{ __('app.history.followed_products') }}</h2>
                </div>
                
                <div class="divide-y divide-white/10">
                    @foreach($followedProducts as $product)
                        <div class="product-card p-6" data-product-id="{{ $product->id }}">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <img src="{{ $product->image_url ?: 'https://via.placeholder.com/80x80' }}" 
                                         alt="{{ $product->name }}" 
                                         class="w-20 h-20 rounded-lg object-cover shadow-lg">
                                    <div>
                                        <h3 class="text-lg font-semibold text-white">{{ $product->name }}</h3>
                                        <p class="text-sm text-gray-300">{{ $product->brand }} | {{ $product->category }}</p>
                                        <div class="flex items-center mt-2 space-x-4">
                                            <span class="bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full text-xs font-semibold backdrop-blur-sm">
                                                {{ $product->is_active ? __('app.history.active') : __('app.history.inactive') }}
                                            </span>
                                            <span class="text-sm text-gray-400">
                                                {{ __('app.history.followed_at', ['date' => $product->pivot->created_at->format('Y-m-d H:i')]) }}
                                            </span>
                                        </div>
                                        <div class="flex items-center mt-2 space-x-2">
                                            @if($product->pivot->email_notifications)
                                                <span class="bg-green-500/20 text-green-300 px-2 py-1 rounded-full text-xs backdrop-blur-sm">{{ __('app.history.email_notification') }}</span>
                                            @endif
                                            @if($product->pivot->wechat_notifications)
                                                <span class="bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full text-xs backdrop-blur-sm">{{ __('app.history.wechat_notification') }}</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-white">¥{{ number_format($product->price, 2) }}</div>
                                    <div class="text-sm text-gray-400">{{ $product->followers_count }} {{ __('app.history.followers') }}</div>
                                    <div class="mt-2 flex space-x-2">
                                        @if($product->product_url)
                                            <a href="{{ $product->product_url }}" target="_blank" 
                                               class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded font-semibold text-sm transition-all duration-300 transform hover:scale-105">
                                                {{ __('app.history.view_product') }}
                                            </a>
                                        @endif
                                        <button onclick="updateSettings({{ $product->id }})" 
                                                class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded font-semibold text-sm transition-all duration-300 transform hover:scale-105">
                                            {{ __('app.history.update_settings') }}
                                        </button>
                                        <button onclick="unfollowProduct({{ $product->id }})" 
                                                class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded font-semibold text-sm transition-all duration-300 transform hover:scale-105">
                                            {{ __('app.history.unfollow') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
                
                <!-- 分页 -->
                <div class="px-6 py-4 border-t border-white/10">
                    {{ $followedProducts->links() }}
                </div>
            </div>
        @else
            <div class="glass-card rounded-2xl p-8 text-center">
                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
                <h3 class="text-lg font-semibold text-white mb-2">{{ __('app.history.no_products_followed') }}</h3>
                <p class="text-gray-300 mb-4">{{ __('app.history.start_following') }}</p>
                <a href="/" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                    {{ __('app.history.browse_products') }}
                </a>
            </div>
        @endif

        <!-- 通知历史 -->
        @if($notifications->count() > 0)
            <div class="glass-card rounded-2xl">
                <div class="px-6 py-4 border-b border-white/10">
                    <h2 class="text-lg font-semibold text-white">{{ __('app.history.recent_notifications') }}</h2>
                </div>
                
                <div class="divide-y divide-white/10">
                    @foreach($notifications->take(10) as $notification)
                        <div class="notification-card p-6">
                            <div class="flex items-start space-x-4">
                                <div class="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center flex-shrink-0 backdrop-blur-sm">
                                    <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5-5-5h5V4h5v13z"></path>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-white">{{ $notification->title }}</h4>
                                    <p class="text-gray-300 mt-1">{{ $notification->content }}</p>
                                    <div class="flex items-center mt-2 space-x-4">
                                        <span class="text-sm text-gray-400">
                                            {{ $notification->created_at->diffForHumans() }}
                                        </span>
                                        @if($notification->product)
                                            <span class="text-sm text-blue-400">
                                                {{ $notification->product->name }}
                                            </span>
                                        @endif
                                        <span class="bg-gray-500/20 text-gray-300 px-2 py-1 rounded-full text-xs backdrop-blur-sm">
                                            {{ $notification->channel == 'email' ? __('app.history.email') : __('app.history.wechat') }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif
    </main>

    <!-- JavaScript -->
    <script>
        // 设置CSRF token
        window.axios = window.axios || {};
        window.axios.defaults = window.axios.defaults || {};
        window.axios.defaults.headers = window.axios.defaults.headers || {};
        window.axios.defaults.headers.common = window.axios.defaults.headers.common || {};
        window.axios.defaults.headers.common['X-CSRF-TOKEN'] = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        // 取消关注商品
        function unfollowProduct(productId) {
            if (!confirm('{{ __('app.history.unfollow_confirmation') }}')) {
                return;
            }

            fetch(`/history/products/${productId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.querySelector(`[data-product-id="${productId}"]`).remove();
                    alert('{{ __('app.history.unfollowed') }}');
                    location.reload();
                } else {
                    alert(data.message || '{{ __('app.history.operation_failed') }}');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('{{ __('app.history.operation_failed') }}');
            });
        }

        // 更新设置
        function updateSettings(productId) {
            // 这里可以打开一个模态框来编辑设置
            alert('{{ __('app.history.settings_development') }}');
        }
    </script>
</body>
</html> 