<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            // 先删除现有的role字段
            $table->dropColumn('role');
        });
        
        Schema::table('users', function (Blueprint $table) {
            // 重新创建role字段，设置足够的长度
            $table->string('role', 50)->default('R_USER')->after('password')->comment('用户角色: R_SUPER, R_ADMIN, R_EDITOR, R_USER');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            // 删除重新创建的字段
            $table->dropColumn('role');
        });
        
        Schema::table('users', function (Blueprint $table) {
            // 恢复原来的短字段
            $table->string('role')->default('R_USER')->after('password');
        });
    }
};
