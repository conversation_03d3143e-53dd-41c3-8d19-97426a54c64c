<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Banner extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'image',
        'link',
        'description',
        'is_active',
        'sort_order',
        'target',
        'start_time',
        'end_time',
        'alt_text',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
    ];

    /**
     * 获取激活的轮播图
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 获取当前有效的轮播图（考虑时间范围）
     */
    public function scopeValid($query)
    {
        $now = now();
        return $query->where('is_active', true)
            ->where(function ($q) use ($now) {
                $q->whereNull('start_time')
                  ->orWhere('start_time', '<=', $now);
            })
            ->where(function ($q) use ($now) {
                $q->whereNull('end_time')
                  ->orWhere('end_time', '>=', $now);
            });
    }

    /**
     * 按排序获取
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('id');
    }

    /**
     * 获取图片完整URL
     */
    public function getImageUrlAttribute(): string
    {
        if (filter_var($this->image, FILTER_VALIDATE_URL)) {
            // 如果是完整URL，直接返回
            return $this->image;
        }
        
        // 如果是相对路径，使用Storage
        return asset('storage/' . $this->image);
    }

    /**
     * 检查轮播图是否在有效期内
     */
    public function getIsValidAttribute(): bool
    {
        $now = now();
        
        // 检查开始时间
        if ($this->start_time && $this->start_time > $now) {
            return false;
        }
        
        // 检查结束时间
        if ($this->end_time && $this->end_time < $now) {
            return false;
        }
        
        return true;
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute(): string
    {
        if (!$this->is_active) {
            return '已禁用';
        }
        
        if (!$this->is_valid) {
            return '已过期';
        }
        
        return '正常';
    }

    /**
     * 获取链接打开方式文本
     */
    public function getTargetTextAttribute(): string
    {
        return match($this->target) {
            '_blank' => '新窗口',
            '_self' => '当前窗口',
            default => '当前窗口',
        };
    }
} 