<template>
  <div class="app-container">
    <!-- 头部操作栏 -->
    <div class="header-actions">
      <el-row :gutter="16" align="middle">
        <el-col :span="12">
          <h2 class="page-title">用户权限管理</h2>
        </el-col>
        <el-col :span="12" class="text-right">
          <el-button type="primary" @click="handleBatchAssign" :icon="Plus">
            批量分配权限
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="用户名">
          <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable />
        </el-form-item>
        <el-form-item label="角色">
          <el-select v-model="searchForm.role" placeholder="请选择角色" clearable>
            <el-option label="超级管理员" value="R_SUPER" />
            <el-option label="管理员" value="R_ADMIN" />
            <el-option label="编辑员" value="R_EDITOR" />
            <el-option label="用户" value="R_USER" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
          <el-button @click="handleReset" :icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 用户权限列表 -->
    <el-card class="list-card">
      <el-table :data="userList" v-loading="loading" style="width: 100%">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" min-width="120" />
        <el-table-column prop="email" label="邮箱" min-width="150" />
        <el-table-column label="角色" width="120">
          <template #default="{ row }">
            <el-tag :type="getRoleTagType(row.role)">
              {{ getRoleLabel(row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="权限" min-width="300">
          <template #default="{ row }">
            <div class="permission-tags">
              <el-tag 
                v-for="permission in row.permissions" 
                :key="permission"
                size="small"
                class="permission-tag"
                :type="getPermissionTagType(permission)"
              >
                {{ getPermissionLabel(permission) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="last_login" label="最后登录" width="180">
          <template #default="{ row }">
            {{ formatTime(row.last_login) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleManagePermissions(row)" :icon="Setting">
              权限管理
            </el-button>
            <el-button type="info" size="small" @click="handleViewPermissions(row)" :icon="View">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :small="false"
        :disabled="loading"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="pagination"
      />
    </el-card>

    <!-- 权限管理弹窗 -->
    <el-dialog
      v-model="permissionDialogVisible"
      :title="`管理用户权限 - ${selectedUser?.username}`"
      width="800px"
    >
      <div class="permission-management">
        <div class="permission-categories">
          <h3>权限分类</h3>
          <el-tree
            :data="permissionTree"
            show-checkbox
            node-key="id"
            :default-checked-keys="userPermissions"
            @check="handlePermissionCheck"
            class="permission-tree"
          />
        </div>
      </div>
      <template #footer>
        <el-button @click="permissionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSavePermissions" :loading="saveLoading">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, Setting, View } from '@element-plus/icons-vue'

// 定义组件名称
defineOptions({
  name: 'UserPermissions'
})

// 响应式数据
const loading = ref(false)
const userList = ref([])
const searchForm = reactive({
  username: '',
  role: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 权限管理弹窗
const permissionDialogVisible = ref(false)
const selectedUser = ref(null)
const saveLoading = ref(false)
const userPermissions = ref([])

// 权限树结构
const permissionTree = ref([
  {
    id: 'dashboard',
    label: '仪表板',
    children: [
      { id: 'dashboard.view', label: '查看' },
      { id: 'dashboard.analysis', label: '数据分析' }
    ]
  },
  {
    id: 'banner',
    label: '轮播图管理',
    children: [
      { id: 'banner.view', label: '查看' },
      { id: 'banner.add', label: '新增' },
      { id: 'banner.edit', label: '编辑' },
      { id: 'banner.delete', label: '删除' }
    ]
  },
  {
    id: 'user',
    label: '用户管理',
    children: [
      { id: 'user.view', label: '查看' },
      { id: 'user.add', label: '新增' },
      { id: 'user.edit', label: '编辑' },
      { id: 'user.delete', label: '删除' },
      { id: 'user.permissions', label: '权限管理' }
    ]
  },
  {
    id: 'system',
    label: '系统管理',
    children: [
      { id: 'system.role', label: '角色管理' },
      { id: 'system.menu', label: '菜单管理' },
      { id: 'system.settings', label: '系统设置' }
    ]
  }
])

// 方法
const loadUserList = async () => {
  loading.value = true
  try {
    // 模拟数据
    const mockData = [
      {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        role: 'R_SUPER',
        permissions: ['dashboard.view', 'dashboard.analysis', 'banner.view', 'banner.add', 'banner.edit', 'banner.delete'],
        last_login: '2024-01-15 14:30:00'
      },
      {
        id: 2,
        username: 'manager',
        email: '<EMAIL>',
        role: 'R_ADMIN',
        permissions: ['dashboard.view', 'banner.view', 'banner.edit', 'user.view'],
        last_login: '2024-01-14 09:15:00'
      },
      {
        id: 3,
        username: 'editor',
        email: '<EMAIL>',
        role: 'R_EDITOR',
        permissions: ['dashboard.view', 'banner.view', 'banner.edit'],
        last_login: '2024-01-13 16:45:00'
      }
    ]
    
    userList.value = mockData
    pagination.total = mockData.length
  } catch (error) {
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadUserList()
}

const handleReset = () => {
  Object.assign(searchForm, {
    username: '',
    role: ''
  })
  handleSearch()
}

const handleBatchAssign = () => {
  ElMessage.info('批量分配权限功能')
}

const handleManagePermissions = (row: any) => {
  selectedUser.value = row
  userPermissions.value = row.permissions || []
  permissionDialogVisible.value = true
}

const handleViewPermissions = (row: any) => {
  const permissions = row.permissions.map(p => getPermissionLabel(p)).join(', ')
  ElMessageBox.alert(
    permissions || '无权限',
    `${row.username} 的权限详情`,
    {
      confirmButtonText: '确定'
    }
  )
}

const handlePermissionCheck = (data: any, checked: any) => {
  // 权限选择逻辑
  console.log('权限选择:', data, checked)
}

const handleSavePermissions = async () => {
  try {
    saveLoading.value = true
    
    // 模拟保存
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('权限保存成功')
    permissionDialogVisible.value = false
    loadUserList()
  } catch (error) {
    ElMessage.error('权限保存失败')
  } finally {
    saveLoading.value = false
  }
}

// 工具函数
const getRoleTagType = (role: string) => {
  const typeMap = {
    'R_SUPER': 'danger',
    'R_ADMIN': 'warning',
    'R_EDITOR': 'info',
    'R_USER': 'success'
  }
  return typeMap[role] || 'info'
}

const getRoleLabel = (role: string) => {
  const labelMap = {
    'R_SUPER': '超级管理员',
    'R_ADMIN': '管理员',
    'R_EDITOR': '编辑员',
    'R_USER': '用户'
  }
  return labelMap[role] || '未知'
}

const getPermissionTagType = (permission: string) => {
  if (permission.includes('delete')) return 'danger'
  if (permission.includes('edit') || permission.includes('add')) return 'warning'
  return 'info'
}

const getPermissionLabel = (permission: string) => {
  const labelMap = {
    'dashboard.view': '仪表板查看',
    'dashboard.analysis': '数据分析',
    'banner.view': '轮播图查看',
    'banner.add': '轮播图新增',
    'banner.edit': '轮播图编辑',
    'banner.delete': '轮播图删除',
    'user.view': '用户查看',
    'user.add': '用户新增',
    'user.edit': '用户编辑',
    'user.delete': '用户删除',
    'user.permissions': '用户权限管理'
  }
  return labelMap[permission] || permission
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadUserList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadUserList()
}

// 生命周期
onMounted(() => {
  loadUserList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.header-actions {
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.list-card {
  margin-bottom: 20px;
}

.text-right {
  text-align: right;
}

.pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.permission-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.permission-tag {
  margin-bottom: 4px;
}

.permission-management {
  max-height: 400px;
  overflow-y: auto;
}

.permission-categories h3 {
  margin-bottom: 16px;
  color: #303133;
}

.permission-tree {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
}
</style> 