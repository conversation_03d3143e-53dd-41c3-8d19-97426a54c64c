<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\Product;
use App\Models\Region;
use App\Models\UserProductFollow;

class ProductController extends Controller
{
    /**
     * 显示商品浏览页面
     */
    public function index(Request $request)
    {
        try {
            // 获取商品列表
            $products = $this->getFilteredProducts($request);
            
            // 获取地区列表
            $regions = Region::where('is_active', true)->get();
            
            // 获取分类列表
            $categories = Product::select('category')
                ->where('is_active', true)
                ->distinct()
                ->pluck('category')
                ->filter()
                ->sort();
            
            // 获取品牌列表
            $brands = Product::select('brand')
                ->where('is_active', true)
                ->distinct()
                ->pluck('brand')
                ->filter()
                ->sort();
            
            // 如果用户已登录，获取关注状态
            $followedProductIds = [];
            if (Auth::check()) {
                $followedProductIds = UserProductFollow::where('user_id', Auth::id())
                    ->pluck('product_id')
                    ->toArray();
            }
            
            // 记录访问日志
            Log::info('用户访问商品浏览页面', [
                'user_id' => Auth::id(),
                'timestamp' => now(),
                'products_count' => $products->total(),
                'filters' => $request->only(['search', 'category', 'brand', 'region_id', 'price_min', 'price_max']),
            ]);
            
            return view('products.index', compact(
                'products',
                'regions',
                'categories',
                'brands',
                'followedProductIds'
            ));
            
        } catch (\Exception $e) {
            Log::error('商品浏览页面加载失败', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return redirect()->back()
                ->with('error', '商品列表加载失败，请稍后重试。');
        }
    }
    
    /**
     * 显示商品详情页面
     */
    public function show($id)
    {
        try {
            $product = Product::with(['region', 'followers'])
                ->withCount('followers')
                ->findOrFail($id);
            
            // 检查用户是否已关注
            $isFollowed = false;
            $followSettings = null;
            if (Auth::check()) {
                $follow = UserProductFollow::where('user_id', Auth::id())
                    ->where('product_id', $id)
                    ->first();
                if ($follow) {
                    $isFollowed = true;
                    $followSettings = $follow;
                }
            }
            
            // 获取相似商品
            $similarProducts = Product::where('category', $product->category)
                ->where('id', '!=', $product->id)
                ->where('is_active', true)
                ->withCount('followers')
                ->limit(8)
                ->get();
            
            // 记录访问日志
            Log::info('用户访问商品详情页面', [
                'user_id' => Auth::id(),
                'product_id' => $id,
                'product_name' => $product->name,
                'timestamp' => now(),
            ]);
            
            return view('products.show', compact(
                'product',
                'isFollowed',
                'followSettings',
                'similarProducts'
            ));
            
        } catch (\Exception $e) {
            Log::error('商品详情页面加载失败', [
                'user_id' => Auth::id(),
                'product_id' => $id,
                'error' => $e->getMessage(),
            ]);
            
            return redirect()->route('products.index')
                ->with('error', '商品详情加载失败，请稍后重试。');
        }
    }
    
    /**
     * 获取过滤后的商品列表
     */
    private function getFilteredProducts(Request $request)
    {
        $query = Product::with(['region'])
            ->withCount('followers')
            ->where('is_active', true);
        
        // 搜索过滤
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('brand', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%");
            });
        }
        
        // 分类过滤
        if ($request->has('category') && !empty($request->category)) {
            $query->where('category', $request->category);
        }
        
        // 品牌过滤
        if ($request->has('brand') && !empty($request->brand)) {
            $query->where('brand', $request->brand);
        }
        
        // 地区过滤
        if ($request->has('region_id') && !empty($request->region_id)) {
            $query->where('region_id', $request->region_id);
        }
        
        // 价格过滤
        if ($request->has('price_min') && !empty($request->price_min)) {
            $query->where('price', '>=', $request->price_min);
        }
        
        if ($request->has('price_max') && !empty($request->price_max)) {
            $query->where('price', '<=', $request->price_max);
        }
        
        // 库存状态过滤
        if ($request->has('stock_status') && !empty($request->stock_status)) {
            $query->where('stock_status', $request->stock_status);
        }
        
        // 排序
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        
        switch ($sortBy) {
            case 'price_asc':
                $query->orderBy('price', 'asc');
                break;
            case 'price_desc':
                $query->orderBy('price', 'desc');
                break;
            case 'popularity':
                $query->orderBy('followers_count', 'desc');
                break;
            case 'name':
                $query->orderBy('name', 'asc');
                break;
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }
        
        return $query->paginate(24);
    }
    
    /**
     * 获取商品API数据
     */
    public function getProductData($id)
    {
        try {
            $product = Product::with(['region'])
                ->withCount('followers')
                ->findOrFail($id);
            
            // 检查用户是否已关注
            $isFollowed = false;
            if (Auth::check()) {
                $isFollowed = UserProductFollow::where('user_id', Auth::id())
                    ->where('product_id', $id)
                    ->exists();
            }
            
            return response()->json([
                'success' => true,
                'product' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'description' => $product->description,
                    'price' => $product->price,
                    'original_price' => $product->original_price,
                    'brand' => $product->brand,
                    'category' => $product->category,
                    'image_url' => $product->image_url,
                    'product_url' => $product->product_url,
                    'stock_status' => $product->stock_status,
                    'region_name' => $product->region->name ?? '',
                    'followers_count' => $product->followers_count,
                    'is_followed' => $isFollowed,
                    'has_discount' => $product->has_discount,
                    'discount_percentage' => $product->discount_percentage,
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取商品数据失败'
            ], 500);
        }
    }
}
