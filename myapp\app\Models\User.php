<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'region_id',
        'timezone',
        'email_notifications',
        'wechat_notifications',
        'wechat_id',
        'notification_preferences',
        'status',
        'role',
        'last_login_at',
        'avatar',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'email_notifications' => 'boolean',
        'wechat_notifications' => 'boolean',
        'notification_preferences' => 'array',
        'last_login_at' => 'datetime',
    ];

    /**
     * 地区关联
     */
    public function region(): BelongsTo
    {
        return $this->belongsTo(Region::class);
    }

    /**
     * 订阅关联
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * 支付订单关联
     */
    public function paymentOrders(): HasMany
    {
        return $this->hasMany(PaymentOrder::class);
    }

    /**
     * 关注的商品关联
     */
    public function followedProducts(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'user_product_follows')
                    ->withPivot(['email_notifications', 'wechat_notifications', 'notification_settings'])
                    ->withTimestamps();
    }

    /**
     * 商品关注记录关联
     */
    public function productFollows(): HasMany
    {
        return $this->hasMany(UserProductFollow::class);
    }

    /**
     * 通知关联
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class);
    }

    /**
     * 获取活跃用户
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * 获取当前活跃订阅
     */
    public function getActiveSubscriptionAttribute()
    {
        return $this->subscriptions()
                    ->where('status', 'active')
                    ->where('expires_at', '>', now())
                    ->first();
    }

    /**
     * 检查是否有活跃订阅
     */
    public function hasActiveSubscription(): bool
    {
        return $this->active_subscription !== null;
    }

    /**
     * 获取订阅状态文本
     */
    public function getSubscriptionStatusAttribute(): string
    {
        if ($this->hasActiveSubscription()) {
            return 'Active';
        }
        return 'Inactive';
    }

    /**
     * 获取用户状态文本
     */
    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            'active' => '正常',
            'inactive' => '未激活',
            'suspended' => '已暂停',
            default => ucfirst($this->status),
        };
    }

    /**
     * 查询范围：只查询前端用户
     */
    public function scopeFrontendUsers($query)
    {
        return $query->where('role', 'R_USER');
    }

    /**
     * 查询范围：只查询后台管理员
     */
    public function scopeBackendAdmins($query)
    {
        return $query->whereIn('role', ['R_SUPER', 'R_ADMIN', 'R_EDITOR']);
    }

    /**
     * 检查是否为前端用户
     */
    public function isFrontendUser(): bool
    {
        return $this->role === 'R_USER';
    }

    /**
     * 检查是否为后台管理员
     */
    public function isBackendAdmin(): bool
    {
        return in_array($this->role, ['R_SUPER', 'R_ADMIN', 'R_EDITOR']);
    }

    /**
     * 检查是否为超级管理员
     */
    public function isSuperAdmin(): bool
    {
        return $this->role === 'R_SUPER';
    }

    /**
     * 获取角色显示名称
     */
    public function getRoleDisplayNameAttribute(): string
    {
        return match($this->role) {
            'R_SUPER' => '超级管理员',
            'R_ADMIN' => '管理员',
            'R_EDITOR' => '编辑者',
            'R_USER' => '前端用户',
            default => '未知角色',
        };
    }

    /**
     * 获取用户类型（前端/后台）
     */
    public function getUserTypeAttribute(): string
    {
        return $this->isFrontendUser() ? 'frontend' : 'backend';
    }
}
