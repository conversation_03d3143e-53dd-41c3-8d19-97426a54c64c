@echo off
chcp 65001 >nul
title Laravel FilamentPHP 管理后台一键安装器
echo.
echo =========================================================
echo           Laravel FilamentPHP 管理后台一键安装
echo =========================================================
echo.
echo 🎯 本脚本将自动为您完成以下操作:
echo    1. 检查并安装PHP环境
echo    2. 安装Composer包管理器
echo    3. 配置国内镜像源
echo    4. 安装FilamentPHP管理后台
echo    5. 创建管理员账号
echo    6. 启动开发服务器
echo.
echo ⚠️  注意事项:
echo    - 需要管理员权限来安装PHP到系统
echo    - 需要稳定的网络连接
echo    - 安装过程可能需要5-10分钟
echo.
set /p confirm="确认开始安装? (y/n): "
if /i not "%confirm%"=="y" (
    echo 安装已取消
    pause
    exit /b 0
)

echo.
echo =========================================================
echo                   开始安装流程
echo =========================================================

:: 步骤1: 检查PHP环境
echo.
echo 📋 步骤 1/6: 检查PHP环境...
where php >nul 2>&1
if errorlevel 1 (
    echo ❌ PHP未安装，开始自动安装...
    echo.
    if exist "install_php.bat" (
        call install_php.bat
        if errorlevel 1 (
            echo ❌ PHP安装失败，请手动安装PHP
            pause
            exit /b 1
        )
        echo ✅ PHP安装完成，需要重新启动脚本
        echo.
        echo 💡 请重新运行此脚本继续安装
        pause
        exit /b 0
    ) else (
        echo ❌ 未找到 install_php.bat 文件
        echo 💡 请手动安装PHP并添加到系统PATH
        pause
        exit /b 1
    )
) else (
    echo ✅ PHP环境检查通过
    php -v
)

:: 步骤2: 检查项目目录
echo.
echo 📋 步骤 2/6: 检查Laravel项目...
if not exist "myapp\artisan" (
    echo ❌ 未找到Laravel项目目录 (myapp)
    echo 当前目录: %cd%
    pause
    exit /b 1
)
echo ✅ Laravel项目检查通过

:: 步骤3: 进入项目目录
echo.
echo 📋 步骤 3/6: 进入项目目录...
cd myapp
echo ✅ 当前目录: %cd%

:: 步骤4: 安装FilamentPHP
echo.
echo 📋 步骤 4/6: 安装FilamentPHP管理后台...
if exist "..\install_filament.bat" (
    copy "..\install_filament.bat" "install_filament.bat" >nul
    call install_filament.bat
    if errorlevel 1 (
        echo ❌ FilamentPHP安装失败
        pause
        exit /b 1
    )
) else (
    echo ❌ 未找到 install_filament.bat 文件
    pause
    exit /b 1
)

:: 步骤5: 检查数据库配置
echo.
echo 📋 步骤 5/6: 检查数据库配置...
if not exist ".env" (
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo ✅ 创建 .env 配置文件
        echo 📝 请配置数据库连接信息
    ) else (
        echo ❌ 未找到 .env.example 文件
    )
) else (
    echo ✅ .env 配置文件已存在
)

:: 步骤6: 启动服务器
echo.
echo 📋 步骤 6/6: 启动开发服务器...
echo.
echo =========================================================
echo                    安装完成！
echo =========================================================
echo.
echo 🎉 FilamentPHP管理后台安装成功！
echo.
echo 🌐 访问地址:
echo    管理后台: http://localhost:8000/admin
echo    前端首页: http://localhost:8000
echo    测试页面: http://localhost:8000/test
echo.
echo 💡 使用说明:
echo    1. 管理后台需要登录才能访问
echo    2. 使用安装时创建的管理员账号登录
echo    3. 按 Ctrl+C 可以停止服务器
echo.
echo =========================================================

set /p start_server="是否现在启动开发服务器? (y/n): "
if /i "%start_server%"=="y" (
    echo.
    echo 🚀 启动Laravel开发服务器...
    php artisan serve --host=127.0.0.1 --port=8000
) else (
    echo.
    echo 💡 稍后可以运行以下命令启动服务器:
    echo    cd myapp
    echo    php artisan serve
)

echo.
echo 📴 安装程序结束
pause 