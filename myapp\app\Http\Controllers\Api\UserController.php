<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use App\Models\User;

class UserController extends Controller
{
    /**
     * 获取用户列表
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 10);
            $search = $request->get('search');
            $role = $request->get('role');
            
            $query = User::query();
            
            // 搜索条件
            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
                });
            }
            
            // 角色筛选
            if ($role) {
                $query->where('role', $role);
            }
            
            $users = $query->orderBy('created_at', 'desc')
                          ->paginate($perPage);
            
            return response()->json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $users
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'msg' => '获取用户列表失败',
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 创建新用户
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255|unique:users,email',
                'phone' => 'nullable|string|max:20',
                'password' => 'required|string|min:6',
                'role' => 'required|string|in:R_SUPER,R_ADMIN,R_EDITOR,R_USER',
                'status' => 'integer|in:0,1' // 改回整数类型，0=禁用，1=启用
            ]);
            
            if ($validator->fails()) {
                return response()->json([
                    'code' => 422,
                    'msg' => '验证失败',
                    'data' => $validator->errors()
                ], 422);
            }
            
            $userData = [
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'password' => Hash::make($request->password),
                'role' => $request->role,
                'status' => $request->get('status', 1) // 改回默认值1（启用）
            ];
            
            $user = User::create($userData);
            
            return response()->json([
                'code' => 200,
                'msg' => '用户创建成功',
                'data' => $user
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'msg' => '创建用户失败',
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 更新用户信息
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $user = User::findOrFail($id);
            
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255|unique:users,email,' . $id,
                'phone' => 'nullable|string|max:20',
                'role' => 'required|string|in:R_SUPER,R_ADMIN,R_EDITOR,R_USER',
                'status' => 'integer|in:0,1' // 改回整数类型，0=禁用，1=启用
            ]);
            
            if ($validator->fails()) {
                return response()->json([
                    'code' => 422,
                    'msg' => '验证失败',
                    'data' => $validator->errors()
                ], 422);
            }
            
            $updateData = [
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'role' => $request->role,
                'status' => $request->get('status', $user->status)
            ];
            
            // 如果提供了新密码，则更新密码
            if ($request->filled('password')) {
                $updateData['password'] = Hash::make($request->password);
            }
            
            $user->update($updateData);
            
            return response()->json([
                'code' => 200,
                'msg' => '用户更新成功',
                'data' => $user
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'msg' => '更新用户失败',
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 删除用户
     */
    public function destroy($id): JsonResponse
    {
        try {
            $user = User::findOrFail($id);
            
            // 防止删除自己
            if ($user->id === auth()->id()) {
                return response()->json([
                    'code' => 403,
                    'msg' => '不能删除自己的账户',
                    'data' => null
                ], 403);
            }
            
            $user->delete();
            
            return response()->json([
                'code' => 200,
                'msg' => '用户删除成功',
                'data' => null
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'msg' => '删除用户失败',
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 切换用户状态
     */
    public function toggleStatus($id): JsonResponse
    {
        try {
            $user = User::findOrFail($id);
            
            // 防止禁用自己
            if ($user->id === auth()->id()) {
                return response()->json([
                    'code' => 403,
                    'msg' => '不能禁用自己的账户',
                    'data' => null
                ], 403);
            }
            
            // 修复状态切换逻辑，使用正确的整数值
            $user->status = $user->status ? 0 : 1;
            $user->save();
            
            return response()->json([
                'code' => 200,
                'msg' => $user->status ? '用户已启用' : '用户已禁用',
                'data' => ['status' => $user->status]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'msg' => '状态切换失败',
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取用户组列表
     */
    public function getGroups(Request $request): JsonResponse
    {
        try {
            // 模拟用户组数据
            $groups = [
                [
                    'id' => 1,
                    'name' => '管理员组',
                    'description' => '系统管理员用户组',
                    'member_count' => 5,
                    'status' => 1,
                    'created_at' => '2024-01-01 10:00:00'
                ],
                [
                    'id' => 2,
                    'name' => '编辑组',
                    'description' => '内容编辑用户组',
                    'member_count' => 12,
                    'status' => 1,
                    'created_at' => '2024-01-02 10:00:00'
                ],
                [
                    'id' => 3,
                    'name' => '访客组',
                    'description' => '普通访客用户组',
                    'member_count' => 100,
                    'status' => 1,
                    'created_at' => '2024-01-03 10:00:00'
                ]
            ];
            
            return response()->json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $groups
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'msg' => '获取用户组失败',
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取用户权限列表
     */
    public function getPermissions(Request $request): JsonResponse
    {
        try {
            // 模拟用户权限数据
            $permissions = [
                [
                    'id' => 1,
                    'username' => 'admin',
                    'email' => '<EMAIL>',
                    'role' => 'R_SUPER',
                    'permissions' => ['dashboard.view', 'dashboard.analysis', 'banner.view', 'banner.add', 'banner.edit', 'banner.delete'],
                    'last_login' => '2024-01-15 14:30:00'
                ],
                [
                    'id' => 2,
                    'username' => 'manager',
                    'email' => '<EMAIL>',
                    'role' => 'R_ADMIN',
                    'permissions' => ['dashboard.view', 'banner.view', 'banner.edit', 'user.view'],
                    'last_login' => '2024-01-14 09:15:00'
                ],
                [
                    'id' => 3,
                    'username' => 'editor',
                    'email' => '<EMAIL>',
                    'role' => 'R_EDITOR',
                    'permissions' => ['dashboard.view', 'banner.view', 'banner.edit'],
                    'last_login' => '2024-01-13 16:45:00'
                ]
            ];
            
            return response()->json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $permissions
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'msg' => '获取用户权限失败',
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取用户日志
     */
    public function getLogs(Request $request): JsonResponse
    {
        try {
            // 模拟用户日志数据
            $logs = [
                [
                    'id' => 1,
                    'username' => 'admin',
                    'action' => 'login',
                    'description' => '管理员登录系统',
                    'ip_address' => '*************',
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'method' => 'POST',
                    'url' => '/api/auth/login',
                    'created_at' => '2024-01-15 09:30:00',
                    'extra_data' => [
                        'login_source' => 'web',
                        'device' => 'desktop'
                    ]
                ],
                [
                    'id' => 2,
                    'username' => 'manager',
                    'action' => 'create',
                    'description' => '创建了新的轮播图',
                    'ip_address' => '*************',
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'method' => 'POST',
                    'url' => '/api/banners',
                    'created_at' => '2024-01-15 10:15:00',
                    'extra_data' => [
                        'banner_id' => 123,
                        'banner_title' => '新年活动'
                    ]
                ]
            ];
            
            return response()->json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $logs
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'msg' => '获取用户日志失败',
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取系统设置
     */
    public function getSettings(): JsonResponse
    {
        try {
            // 模拟系统设置数据
            $settings = [
                'basic' => [
                    'default_page_size' => 20,
                    'session_timeout' => '60',
                    'password_complexity' => ['lowercase', 'number'],
                    'password_min_length' => 8,
                    'allow_multiple_login' => false
                ],
                'security' => [
                    'login_attempts' => 5,
                    'lockout_duration' => '30',
                    'ip_whitelist' => '',
                    'enable_2fa' => false,
                    'api_rate_limit' => 1000
                ],
                'notification' => [
                    'email_notifications' => ['password_change', 'account_locked', 'system_error'],
                    'sms_notifications' => ['critical_error', 'security_breach'],
                    'notification_email' => '<EMAIL>',
                    'notification_phone' => ''
                ]
            ];
            
            return response()->json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $settings
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'msg' => '获取系统设置失败',
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 更新系统设置
     */
    public function updateSettings(Request $request): JsonResponse
    {
        try {
            // 这里应该将设置保存到数据库或配置文件
            // 暂时模拟成功响应
            
            return response()->json([
                'code' => 200,
                'msg' => '系统设置更新成功',
                'data' => null
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'msg' => '更新系统设置失败',
                'data' => null
            ], 500);
        }
    }
} 