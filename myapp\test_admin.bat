@echo off
echo ===================================
echo FilamentPHP 管理后台测试
echo ===================================
echo.
echo 管理员账号信息：
echo 邮箱: <EMAIL>
echo 密码: admin123
echo.
echo 访问地址：
echo http://127.0.0.1:8002/admin
echo http://127.0.0.1:8002/admin/login
echo.
echo 请在PHPStudy中确保以下设置：
echo 1. 网站根目录指向: G:\php\laravel\myapp\public
echo 2. 端口设置为: 8002
echo 3. Apache已启动
echo.
echo 正在检查路由...
D:\phpstudy_pro\Extensions\php\php8.0.2nts\php.exe artisan route:list | findstr filament
echo.
echo 测试完成！请在浏览器中访问上述地址进行测试。
pause 