<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>商品分类管理</span>
          <el-button type="primary" @click="handleAdd" :icon="Plus">
            添加分类
          </el-button>
        </div>
      </template>

      <div class="category-list">
        <el-tag
          v-for="category in categories"
          :key="category"
          closable
          @close="handleDelete(category)"
          @click="handleEdit(category)"
          class="category-tag"
        >
          {{ category }}
        </el-tag>
      </div>
    </el-card>

    <!-- 添加/编辑分类弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogMode === 'add' ? '添加分类' : '编辑分类'"
      width="400px"
    >
      <el-form :model="formData" label-width="80px">
        <el-form-item label="分类名称">
          <el-input v-model="formData.name" placeholder="请输入分类名称" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">
            {{ dialogMode === 'add' ? '添加' : '更新' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const dialogVisible = ref(false)
const dialogMode = ref<'add' | 'edit'>('add')
const categories = ref<string[]>([])

const formData = reactive({
  name: '',
  oldName: ''
})

onMounted(() => {
  loadCategories()
})

const loadCategories = async () => {
  try {
    // 这里应该调用实际的API
    console.log('加载分类列表')
    // const response = await productApi.getOptions()
    // categories.value = response.data.categories || []
    categories.value = ['手机', '电脑', '平板', '配件'] // 模拟数据
  } catch (error) {
    ElMessage.error('加载分类失败')
  }
}

const handleAdd = () => {
  dialogMode.value = 'add'
  formData.name = ''
  formData.oldName = ''
  dialogVisible.value = true
}

const handleEdit = (category: string) => {
  dialogMode.value = 'edit'
  formData.name = category
  formData.oldName = category
  dialogVisible.value = true
}

const handleDelete = async (category: string) => {
  try {
    // 这里应该调用实际的API检查是否有商品使用该分类
    console.log('删除分类', category)
    ElMessage.success('删除成功')
    loadCategories()
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

const handleSubmit = async () => {
  if (!formData.name.trim()) {
    ElMessage.warning('请输入分类名称')
    return
  }

  try {
    if (dialogMode.value === 'add') {
      // 这里应该调用实际的API
      console.log('添加分类', formData.name)
      ElMessage.success('添加成功')
    } else {
      // 这里应该调用实际的API
      console.log('更新分类', formData.oldName, formData.name)
      ElMessage.success('更新成功')
    }
    
    dialogVisible.value = false
    loadCategories()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.category-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.category-tag {
  cursor: pointer;
  font-size: 14px;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.category-tag:hover {
  transform: scale(1.05);
}

.dialog-footer {
  text-align: right;
}
</style> 