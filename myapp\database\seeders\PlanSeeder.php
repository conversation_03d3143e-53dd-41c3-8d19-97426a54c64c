<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Plan;

class PlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // 基础套餐
        Plan::create([
            'name' => 'Basic Plan',
            'slug' => 'basic',
            'description' => 'Perfect for individuals who want to track a few products. Get instant notifications when your favorite items are back in stock or on sale.',
            'price' => 9.99,
            'currency' => 'USD',
            'billing_cycle' => 'monthly',
            'duration_days' => 30,
            'max_products' => 10,
            'max_notifications' => 100,
            'features' => [
                'Track up to 10 products',
                'Email notifications',
                'Basic price alerts',
                'Stock availability alerts',
                'Mobile-friendly dashboard'
            ],
            'is_popular' => false,
            'is_active' => true,
            'sort_order' => 1,
        ]);

        // 专业套餐（热门）
        Plan::create([
            'name' => 'Professional Plan',
            'slug' => 'professional',
            'description' => 'Ideal for power users and small businesses. Advanced features and higher limits to maximize your savings and opportunities.',
            'price' => 19.99,
            'currency' => 'USD',
            'billing_cycle' => 'monthly',
            'duration_days' => 30,
            'max_products' => 50,
            'max_notifications' => 500,
            'features' => [
                'Track up to 50 products',
                'Email & WeChat notifications',
                'Advanced price alerts',
                'Stock availability alerts',
                'Price history tracking',
                'Custom notification settings',
                'Priority customer support',
                'Export data functionality'
            ],
            'is_popular' => true,
            'is_active' => true,
            'sort_order' => 2,
        ]);

        // 企业套餐
        Plan::create([
            'name' => 'Enterprise Plan',
            'slug' => 'enterprise',
            'description' => 'For businesses and teams that need comprehensive product monitoring with unlimited access and premium features.',
            'price' => 49.99,
            'currency' => 'USD',
            'billing_cycle' => 'monthly',
            'duration_days' => 30,
            'max_products' => 0, // 无限制
            'max_notifications' => 0, // 无限制
            'features' => [
                'Unlimited product tracking',
                'Unlimited notifications',
                'All notification channels',
                'Advanced analytics',
                'API access',
                'Team collaboration',
                'Custom integrations',
                'Dedicated account manager',
                'Priority support'
            ],
            'is_popular' => false,
            'is_active' => true,
            'sort_order' => 3,
        ]);

        // 年度套餐（折扣）
        Plan::create([
            'name' => 'Professional Annual',
            'slug' => 'professional-annual',
            'description' => 'Save 20% with our annual Professional plan. All Professional features with better value for long-term users.',
            'price' => 199.99,
            'currency' => 'USD',
            'billing_cycle' => 'yearly',
            'duration_days' => 365,
            'max_products' => 50,
            'max_notifications' => 500,
            'features' => [
                'All Professional Plan features',
                '20% discount (Save $39.89/year)',
                'Annual billing',
                'Priority support'
            ],
            'is_popular' => false,
            'is_active' => true,
            'sort_order' => 4,
        ]);

        // 终身套餐
        Plan::create([
            'name' => 'Lifetime Access',
            'slug' => 'lifetime',
            'description' => 'One-time payment for lifetime access to all Professional features. Best value for long-term users.',
            'price' => 299.99,
            'currency' => 'USD',
            'billing_cycle' => 'lifetime',
            'duration_days' => null,
            'max_products' => 50,
            'max_notifications' => 500,
            'features' => [
                'All Professional Plan features',
                'Lifetime access',
                'One-time payment',
                'Future feature updates included',
                'Priority support'
            ],
            'is_popular' => false,
            'is_active' => true,
            'sort_order' => 5,
        ]);
    }
}
