@echo off
rem 设置代码页为UTF-8，防止乱码
chcp 65001 >nul
echo ===========================================
echo 登录问题诊断与修复脚本
echo ===========================================
echo.

rem --- 核心设置 ---
rem 检查并设置项目根目录
set "PROJECT_ROOT=%~dp0"
if not exist "%PROJECT_ROOT%myapp\artisan" (
    echo [错误] 未能在此目录下找到 'myapp' 项目: %PROJECT_ROOT%
    echo 请将此脚本放到 'myapp' 文件夹所在的目录中。
    pause
    exit /b 1
)
rem 设置PHP可执行文件路径
set "PHP_EXECUTABLE=D:\phpstudy_pro\Extensions\php\php8.1.9nts\php.exe"
if not exist "%PHP_EXECUTABLE%" (
    echo [错误] 找不到PHP可执行文件: %PHP_EXECUTABLE%
    echo 请检查您的phpstudy安装路径是否正确。
    pause
    exit /b 2
)
rem --- 结束核心设置 ---

rem 进入项目目录
cd /d "%PROJECT_ROOT%myapp"
echo [信息] 当前工作目录: %CD%
echo.

echo [步骤 1/5] 正在检查数据库连接...
"%PHP_EXECUTABLE%" artisan db:show
if %errorlevel% neq 0 (
    echo [错误] 数据库连接失败！请检查 'myapp\.env' 文件中的数据库配置。
    pause
    exit /b 3
)
echo [成功] 数据库连接正常。
echo.

echo [步骤 2/5] 正在强制清理旧的测试用户...
rem 使用tinker删除已存在的用户，避免唯一键冲突
"%PHP_EXECUTABLE%" artisan tinker --execute="use App\Models\User; User::where('email', '<EMAIL>')->forceDelete(); User::where('email', '<EMAIL>')->forceDelete(); echo '[成功] 已清理旧的测试用户。';"
echo.

echo [步骤 3/5] 正在重新创建状态为 'active' 的测试用户...
rem 创建前端用户
"%PHP_EXECUTABLE%" artisan tinker --execute="use App\Models\User; User::create(['name' => '测试用户', 'email' => '<EMAIL>', 'password' => bcrypt('user123'), 'role' => 'R_USER', 'status' => 'active', 'email_verified_at' => now()]); echo '[成功] 前端用户 (<EMAIL>) 已创建。';"
rem 创建管理员用户
"%PHP_EXECUTABLE%" artisan tinker --execute="use App\Models\User; User::create(['name' => '超级管理员', 'email' => '<EMAIL>', 'password' => bcrypt('admin123'), 'role' => 'R_SUPER', 'status' => 'active', 'email_verified_at' => now()]); echo '[成功] 管理员用户 (<EMAIL>) 已创建。';"
echo.

echo [步骤 4/5] 正在验证用户数据...
rem 验证前端用户是否存在
"%PHP_EXECUTABLE%" artisan tinker --execute="use App\Models\User; $user = User::where('email', '<EMAIL>')->first(); if (\$user) { echo '[成功] 在数据库中找到前端用户: ' . \$user->email . ' | 状态: ' . \$user->status; } else { echo '[失败] 未在数据库中找到前端用户。'; }"
echo.

echo [步骤 5/5] 正在验证前端用户密码...
rem 使用Hash::check来模拟登录验证
"%PHP_EXECUTABLE%" artisan tinker --execute="use App\Models\User; use Illuminate\Support\Facades\Hash; $user = User::where('email', '<EMAIL>')->first(); if (\$user && Hash::check('user123', \$user->password)) { echo '[成功] 前端用户密码 (user123) 验证通过！'; } else { echo '[失败] 前端用户密码验证失败！'; }"
echo.

echo ===========================================
echo ✅ 诊断与修复完成！
echo ===========================================
echo.
echo 您现在 **应该可以** 使用以下信息登录了。
echo 如果仍然失败，说明问题可能在前端视图或会话配置上。
echo.
echo --- 登录信息 ---
echo.
echo [前端用户] - 访问 http://localhost:8000/login
echo    - 邮箱: <EMAIL>
echo    - 密码: user123
echo.
echo [后台管理员] - 访问 http://localhost:8000/admin
echo    - 邮箱: <EMAIL>
echo    - 密码: admin123
echo.

pause 