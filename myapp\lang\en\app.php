<?php

return [
    // Navigation
    'nav' => [
        'product_alert_system' => 'Product Alert System',
        'browse_products' => 'Browse Products',
        'help' => 'Help',
        'contact_us' => 'Contact Us',
        'language' => 'Language',
        'chinese' => '中文',
        'english' => 'English',
        'back_to_home' => 'Back to Home',
        'home' => 'Home',
        'dashboard' => 'Dashboard',
        'logout' => 'Logout',
        'subscribe' => 'Subscribe',
        'login' => 'Login',
        'my_follows' => 'My Follows',
    ],

    // Dashboard
    'dashboard' => [
        'title' => 'User Dashboard - Product Alert System',
        'welcome' => 'Welcome, :name',
        'product_history' => 'Product History',
        'user_center' => 'User Center',
        'subtitle' => 'Manage your account, subscriptions and product follows',
        'last_login' => 'Last Login',
        'first_login' => 'First Login',
        'edit_profile' => 'Edit Profile',
        'followed_products' => 'Followed Products',
        'subscription_status' => 'Subscription Status',
        'subscribed' => 'Subscribed',
        'not_subscribed' => 'Not Subscribed',
        'monthly_notifications' => 'Monthly Notifications',
        'money_saved' => 'Money Saved',
        'active_subscription_title' => 'You have an active subscription',
        'active_subscription_desc' => 'Enjoy complete product monitoring service',
        'free_account_limit' => 'Free Account Limitation',
        'free_account_desc' => 'Follow up to 3 products, upgrade for more features',
        'upgrade_now' => 'Upgrade Now',
        'recent_notifications' => 'Recent Notifications',
        'welcome_message' => 'Welcome to Product Alert System!',
        'just_now' => 'Just now',
        'no_more_notifications' => 'No more notifications',
        'quick_actions' => 'Quick Actions',
        'upgrade_subscription' => 'Upgrade Subscription',
        'view_product_history' => 'View Product History',
        'add_followed_products' => 'Add Followed Products',
        'notification_settings' => 'Notification Settings',
        'email_notifications' => 'Email Notifications',
        'wechat_notifications' => 'WeChat Notifications',
        'price_alerts' => 'Price Alerts',
        'stock_alerts' => 'Stock Alerts',
        'modify_settings' => 'Modify Settings',
        'feature_in_development' => 'Notification settings feature is under development',
        'no_notification_settings' => 'No notification settings',
        'logout_confirm' => 'Are you sure you want to logout?',
    ],

    // Hero Section
    'hero' => [
        'title' => 'Never Miss Any <span class="text-yellow-300">Great Deals</span>',
        'subtitle' => 'Smart monitoring of product price changes and stock status, get the latest listing and promotion information instantly',
        'start_now' => 'Start Now:',
        'real_time_monitoring' => '✓ Real-time Monitoring',
        'multi_channel_notification' => '✓ Multi-channel Notifications',
        'price_tracking' => '✓ Price Tracking',
        'browse_all_products' => '🛍️ Browse All Products',
        'start_monitoring' => '🚀 Start Monitoring Now',
    ],

    // Main Services
    'services' => [
        'title' => 'Choose Your Service',
        'browse_products' => [
            'title' => 'Browse Products',
            'description' => 'Discover trending products, view detailed information, and follow products you\'re interested in with one click.',
            'button' => 'Start Browsing',
            'tag' => 'Free Browsing',
        ],
        'subscription' => [
            'title' => 'Subscription Service',
            'description' => 'Choose the subscription plan that suits you and start monitoring the products you care about. Support multiple billing methods.',
            'button' => 'Subscribe Now',
            'tag' => '🔥 Popular Choice',
        ],
        'account_login' => [
            'title' => 'Account Login',
            'description' => 'Already have an account? Login to view your subscription status, followed products, and notification history.',
            'button' => 'Login Now',
            'register_link' => 'Don\'t have an account? Sign up',
        ],
        'my_follows' => [
            'title' => 'My Follows',
            'description' => 'View your followed products, price change history, and manage notification records.',
            'button' => 'View My Follows',
            'require_login' => 'Login required',
        ],
    ],

    // Features
    'features' => [
        'title' => 'Why Choose Us?',
        'subtitle' => 'Professional product monitoring service, never miss any opportunity',
        'real_time' => [
            'title' => 'Real-time Monitoring',
            'description' => '24/7 continuous monitoring of product status, detect changes instantly',
        ],
        'multi_channel' => [
            'title' => 'Multi-channel Notifications',
            'description' => 'Support email, WeChat and other notification methods to ensure you don\'t miss out',
        ],
        'smart_analysis' => [
            'title' => 'Smart Analysis',
            'description' => 'Price trend analysis to help you find the best time to buy',
        ],
        'easy_to_use' => [
            'title' => 'Easy to Use',
            'description' => 'Intuitive user interface, setup completed in minutes',
        ],
    ],

    // Pricing
    'pricing' => [
        'title' => 'Choose Your Plan',
        'basic' => [
            'name' => 'Basic',
            'price' => 'Free',
            'features' => [
                'Monitor 5 products',
                'Email notifications',
                'Basic price alerts',
            ],
            'button' => 'Get Started Free',
        ],
        'pro' => [
            'name' => 'Professional',
            'price' => '$4.99/month',
            'features' => [
                'Monitor 50 products',
                'Multi-channel notifications',
                'Price trend analysis',
                'Priority customer support',
            ],
            'button' => 'Subscribe Now',
            'popular' => 'Most Popular',
        ],
        'enterprise' => [
            'name' => 'Enterprise',
            'price' => '$16.99/month',
            'features' => [
                'Unlimited product monitoring',
                'All notification methods',
                'Advanced data analytics',
                'Dedicated account manager',
                'API access',
            ],
            'button' => 'Contact Sales',
        ],
    ],

    // Footer
    'footer' => [
        'about_us' => 'About Us',
        'contact' => 'Contact',
        'privacy' => 'Privacy Policy',
        'terms' => 'Terms of Service',
        'help_center' => 'Help Center',
        'copyright' => '© 2024 Product Alert System. All rights reserved.',
        'email' => 'Email: <EMAIL>',
        'phone' => 'Phone: ******-888-8888',
    ],

    // Common
    'common' => [
        'loading' => 'Loading...',
        'submit' => 'Submit',
        'cancel' => 'Cancel',
        'save' => 'Save',
        'delete' => 'Delete',
        'edit' => 'Edit',
        'view' => 'View',
        'back' => 'Back',
        'next' => 'Next',
        'previous' => 'Previous',
        'close' => 'Close',
        'confirm' => 'Confirm',
        'success' => 'Success',
        'error' => 'Error',
        'warning' => 'Warning',
        'info' => 'Info',
    ],

    // Subscription
    'subscription' => [
        'title' => 'Subscription Service - :name',
    ],

    // History
    'history' => [
        'title' => 'My Follow History - :name',
        'description' => 'Manage your followed products and view notification history',
        'category' => 'Product Category',
        'all_categories' => 'All Categories',
        'electronics' => 'Electronics',
        'fashion' => 'Fashion',
        'home' => 'Home & Garden',
        'sports' => 'Sports & Outdoors',
        'status' => 'Product Status',
        'all_statuses' => 'All Statuses',
        'active' => 'Active',
        'inactive' => 'Inactive',
        'out_of_stock' => 'Out of Stock',
        'notification_type' => 'Notification Type',
        'all_types' => 'All Types',
        'price_drop' => 'Price Drop Alert',
        'back_in_stock' => 'Back in Stock',
        'new_product' => 'New Product',
        'search' => 'Search Products',
        'search_placeholder' => 'Enter product name or brand',
        'clear_filter' => 'Clear Filter',
        'apply_filter' => 'Apply Filter',
        'followed_products' => 'Followed Products',
        'received_notifications' => 'Received Notifications',
        'popular_products' => 'Popular Products',
        'today_active' => 'Today Active',
        'followed_at' => 'Followed at :date',
        'email_notification' => 'Email Notification',
        'wechat_notification' => 'WeChat Notification',
        'followers' => 'followers',
        'view_product' => 'View Product',
        'update_settings' => 'Settings',
        'unfollow' => 'Unfollow',
        'no_products_followed' => 'No products followed yet',
        'start_following' => 'Start following products you\'re interested in to get price and stock notifications',
        'browse_products' => 'Browse Products',
        'recent_notifications' => 'Recent Notifications',
        'email' => 'Email',
        'wechat' => 'WeChat',
        'unfollow_confirmation' => 'Are you sure you want to unfollow this product?',
        'unfollowed' => 'Unfollowed successfully',
        'operation_failed' => 'Operation failed, please try again later',
        'settings_development' => 'Settings feature is under development...',
    ],

    // Products
    'products' => [
        'title' => 'Product Browse - :name',
        'subtitle' => 'Discover trending products and follow price changes with one click',
        'search_placeholder' => 'Search product name, brand or description...',
        'search_button' => 'Search',
        'category' => 'Category',
        'all_categories' => 'All Categories',
        'brand' => 'Brand',
        'all_brands' => 'All Brands',
        'region' => 'Region',
        'all_regions' => 'All Regions',
        'sort_by' => 'Sort By',
        'latest_listing' => 'Latest Listing',
        'price_low_to_high' => 'Price: Low to High',
        'price_high_to_low' => 'Price: High to Low',
        'most_popular' => 'Most Popular',
        'name_sort' => 'Name Sort',
        'price_range' => 'Price Range:',
        'min_price' => 'Min Price',
        'max_price' => 'Max Price',
        'clear_filter' => 'Clear Filter',
        'total_products' => 'Found :total products',
        'filtered' => '(Filtered)',
        'display_range' => 'Showing :first - :last items',
        'followed' => 'Followed',
        'followers' => 'followers',
        'in_stock' => 'In Stock',
        'low_stock' => 'Low Stock',
        'out_of_stock' => 'Out of Stock',
        'view_details' => 'View Details',
        'unfollow' => 'Unfollow',
        'follow' => 'Follow',
        'login' => 'Login',
        'no_products' => 'No products available',
        'no_products_filtered' => 'No products found matching your filter criteria, please try adjusting the filters',
        'no_products_update' => 'Product database is being updated, please check back later',
        'view_all_products' => 'View All Products',
        'follow_error' => 'Follow failed, please try again later',
        'unfollow_confirm' => 'Are you sure you want to unfollow this product?',
        'unfollowed' => 'Unfollowed successfully',
        'unfollow_error' => 'Unfollow failed, please try again later',
    ],
]; 