<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Notification extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'product_id',
        'type',
        'channel',
        'title',
        'content',
        'data',
        'status',
        'sent_at',
        'error_message',
        'retry_count',
    ];

    protected $casts = [
        'data' => 'array',
        'sent_at' => 'datetime',
    ];

    /**
     * 用户关联
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 商品关联
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 获取待发送的通知
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * 获取已发送的通知
     */
    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    /**
     * 按类型筛选
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 按渠道筛选
     */
    public function scopeByChannel($query, $channel)
    {
        return $query->where('channel', $channel);
    }
}
