import request from '@/utils/http'

export interface BannerItem {
  id: number
  title: string
  image: string
  link?: string
  description?: string
  target: '_self' | '_blank'
  alt_text?: string
  sort_order: number
  is_active: boolean
  start_time?: string
  end_time?: string
  created_at?: string
  updated_at?: string
}

export interface BannerStats {
  total: number
  active: number
  valid: number
  expired: number
  scheduled: number
}

export interface BannerListParams {
  page?: number
  per_page?: number
  search?: string
}

export interface BannerCreateParams {
  title: string
  image: string
  link?: string
  description?: string
  target?: '_self' | '_blank'
  alt_text?: string
  sort_order?: number
  start_time?: string
  end_time?: string
  is_active?: boolean
}

export interface BannerUpdateParams extends Partial<BannerCreateParams> {
  id: number
}

export class BannerService {
  // 获取轮播图列表
  static getBannerList(params?: BannerListParams) {
    return request.get<BannerItem[]>({
      url: '/api/banners',
      params
    })
  }

  // 获取轮播图详情
  static getBannerDetail(id: number) {
    return request.get<BannerItem>({
      url: `/api/banners/${id}`
    })
  }

  // 获取轮播图统计
  static getBannerStats() {
    return request.get<BannerStats>({
      url: '/api/banners/stats'
    })
  }

  // 创建轮播图
  static createBanner(data: BannerCreateParams) {
    return request.post<BannerItem>({
      url: '/api/admin/banners',
      data
    })
  }

  // 更新轮播图
  static updateBanner(id: number, data: Partial<BannerCreateParams>) {
    return request.put<BannerItem>({
      url: `/api/admin/banners/${id}`,
      data
    })
  }

  // 删除轮播图
  static deleteBanner(id: number) {
    return request.del<any>({
      url: `/api/admin/banners/${id}`
    })
  }

  // 切换轮播图状态
  static toggleBannerStatus(id: number) {
    return request.put<{ id: number; is_active: boolean; status_text: string }>({
      url: `/api/admin/banners/${id}/toggle`
    })
  }
} 