<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订阅成功 - 商品上新提醒系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .success-animation { animation: successPulse 2s ease-in-out infinite; }
        @keyframes successPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-2px); box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="/" class="text-xl font-bold text-gray-900">商品提醒系统</a>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-green-600 font-medium">订阅成功</span>
                    <a href="/dashboard" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">进入中心</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 成功消息 -->
        @if(session('success'))
            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                    </div>
                </div>
            </div>
        @endif

        <!-- 成功标题 -->
        <div class="text-center mb-8">
            <div class="success-animation inline-flex items-center justify-center w-24 h-24 bg-green-100 rounded-full mb-4">
                <svg class="w-12 h-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">订阅成功！</h1>
            <p class="text-lg text-gray-600">恭喜您成功订阅商品提醒服务</p>
        </div>

        <div class="grid lg:grid-cols-3 gap-8">
            <!-- 订阅详情 -->
            <div class="lg:col-span-2 space-y-6">
                <!-- 订阅信息卡片 -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">订阅详情</h2>
                    
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <span class="text-gray-600">套餐名称：</span>
                                <span class="font-medium text-gray-900">{{ $subscription->plan->name }}</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">计费周期：</span>
                                <span class="font-medium text-gray-900">
                                    @switch($subscription->billing_cycle)
                                        @case('monthly')
                                            月付
                                            @break
                                        @case('yearly')
                                            年付
                                            @break
                                        @case('lifetime')
                                            终身
                                            @break
                                    @endswitch
                                </span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">支付金额：</span>
                                <span class="font-bold text-green-600">¥{{ number_format($subscription->amount, 2) }}</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">服务地区：</span>
                                <span class="font-medium text-gray-900">{{ $subscription->region->name }}</span>
                            </div>
                        </div>
                        
                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <span class="text-gray-600">订阅状态：</span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    已激活
                                </span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">激活时间：</span>
                                <span class="font-medium text-gray-900">{{ $subscription->activated_at->format('Y-m-d H:i') }}</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">到期时间：</span>
                                <span class="font-medium text-gray-900">
                                    @if($subscription->billing_cycle === 'lifetime')
                                        终身有效
                                    @else
                                        {{ $subscription->expires_at->format('Y-m-d H:i') }}
                                    @endif
                                </span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">订单号：</span>
                                <span class="font-mono text-sm text-gray-900">{{ $subscription->paymentOrder->order_number }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 套餐功能 -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">您的套餐功能</h2>
                    
                    @php
                        $features = json_decode($subscription->plan->features, true) ?? [];
                    @endphp
                    
                    <div class="grid md:grid-cols-2 gap-4">
                        @if(!empty($features))
                            @foreach($features as $feature)
                                <div class="flex items-center space-x-3">
                                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span class="text-gray-700">{{ $feature }}</span>
                                </div>
                            @endforeach
                        @else
                            <div class="col-span-2">
                                <div class="space-y-3">
                                    <div class="flex items-center space-x-3">
                                        <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span class="text-gray-700">商品价格监控</span>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span class="text-gray-700">库存状态提醒</span>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span class="text-gray-700">多渠道通知</span>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span class="text-gray-700">24小时客服支持</span>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- 支付信息 -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">支付信息</h2>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">支付方式：</span>
                            <span class="font-medium text-gray-900">
                                @switch($subscription->paymentOrder->payment_method)
                                    @case('alipay')
                                        支付宝
                                        @break
                                    @case('wechat')
                                        微信支付
                                        @break
                                    @case('paypal')
                                        PayPal
                                        @break
                                    @default
                                        {{ $subscription->paymentOrder->payment_method }}
                                @endswitch
                            </span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">交易号：</span>
                            <span class="font-mono text-sm text-gray-900">{{ $subscription->paymentOrder->transaction_id }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">支付时间：</span>
                            <span class="font-medium text-gray-900">{{ $subscription->paymentOrder->paid_at->format('Y-m-d H:i:s') }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">支付状态：</span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                支付成功
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="space-y-6">
                <!-- 下一步操作 -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">开始使用</h3>
                    
                    <div class="space-y-3">
                        <a href="/dashboard" class="card-hover block w-full bg-blue-500 hover:bg-blue-600 text-white text-center py-3 px-4 rounded-lg transition duration-300">
                            进入用户中心
                        </a>
                        
                        <button class="card-hover block w-full bg-green-100 hover:bg-green-200 text-green-700 text-center py-3 px-4 rounded-lg transition duration-300" onclick="alert('添加商品功能正在开发中')">
                            添加关注商品
                        </button>
                        
                        <a href="/history" class="card-hover block w-full bg-gray-100 hover:bg-gray-200 text-gray-700 text-center py-3 px-4 rounded-lg transition duration-300">
                            查看商品历史
                        </a>
                    </div>
                </div>

                <!-- 使用指南 -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 class="font-semibold text-blue-800 mb-2">使用指南</h3>
                    <div class="text-sm text-blue-600 space-y-2">
                        <p>1. 在用户中心管理您的订阅</p>
                        <p>2. 添加您感兴趣的商品</p>
                        <p>3. 设置通知偏好</p>
                        <p>4. 接收价格和库存提醒</p>
                    </div>
                </div>

                <!-- 客服支持 -->
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <h3 class="font-semibold text-gray-800 mb-2">需要帮助？</h3>
                    <div class="text-sm text-gray-600 space-y-1">
                        <p>📞 客服热线：400-123-4567</p>
                        <p>💬 在线客服：9:00-22:00</p>
                        <p>📧 邮箱：<EMAIL></p>
                        <p>📱 微信：商品提醒助手</p>
                    </div>
                </div>

                <!-- 分享推荐 -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h3 class="font-semibold text-yellow-800 mb-2">分享给朋友</h3>
                    <p class="text-sm text-yellow-600 mb-3">推荐朋友使用，双方都可获得优惠</p>
                    <button class="w-full bg-yellow-500 hover:bg-yellow-600 text-white py-2 px-4 rounded-lg text-sm transition duration-300" onclick="shareToFriends()">
                        分享推荐链接
                    </button>
                </div>
            </div>
        </div>

        <!-- 温馨提示 -->
        <div class="mt-8 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6">
            <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="font-semibold text-blue-800 mb-2">温馨提示</h3>
                    <div class="text-sm text-blue-600 space-y-1">
                        <p>• 您的订阅已立即生效，可以开始添加商品关注</p>
                        <p>• 系统将根据您的设置发送通知，请确保通知渠道畅通</p>
                        <p>• 如需修改订阅或有任何问题，请随时联系客服</p>
                        <p>• 感谢您选择我们的服务，祝您使用愉快！</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script>
        // 分享功能
        function shareToFriends() {
            const shareUrl = window.location.origin + '/?ref=' + '{{ Auth::id() }}';
            const shareText = '我在使用这个商品提醒系统，非常好用！推荐给你：' + shareUrl;
            
            if (navigator.share) {
                navigator.share({
                    title: '商品上新提醒系统',
                    text: shareText,
                    url: shareUrl
                });
            } else {
                // 复制到剪贴板
                navigator.clipboard.writeText(shareText).then(() => {
                    alert('推荐链接已复制到剪贴板，快去分享给朋友吧！');
                }).catch(() => {
                    // 降级方案
                    prompt('请复制以下链接分享给朋友：', shareText);
                });
            }
        }

        // 自动关闭成功消息
        setTimeout(function() {
            const alerts = document.querySelectorAll('.bg-green-50');
            alerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 500);
            });
        }, 5000);

        // 页面加载完成后显示庆祝动画
        document.addEventListener('DOMContentLoaded', function() {
            // 可以添加更多动画效果
            console.log('订阅成功页面加载完成');
        });
    </script>
</body>
</html> 
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订阅成功 - 商品上新提醒系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .success-animation { animation: successPulse 2s ease-in-out infinite; }
        @keyframes successPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-2px); box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="/" class="text-xl font-bold text-gray-900">商品提醒系统</a>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-green-600 font-medium">订阅成功</span>
                    <a href="/dashboard" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">进入中心</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 成功消息 -->
        @if(session('success'))
            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                    </div>
                </div>
            </div>
        @endif

        <!-- 成功标题 -->
        <div class="text-center mb-8">
            <div class="success-animation inline-flex items-center justify-center w-24 h-24 bg-green-100 rounded-full mb-4">
                <svg class="w-12 h-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">订阅成功！</h1>
            <p class="text-lg text-gray-600">恭喜您成功订阅商品提醒服务</p>
        </div>

        <div class="grid lg:grid-cols-3 gap-8">
            <!-- 订阅详情 -->
            <div class="lg:col-span-2 space-y-6">
                <!-- 订阅信息卡片 -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">订阅详情</h2>
                    
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <span class="text-gray-600">套餐名称：</span>
                                <span class="font-medium text-gray-900">{{ $subscription->plan->name }}</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">计费周期：</span>
                                <span class="font-medium text-gray-900">
                                    @switch($subscription->billing_cycle)
                                        @case('monthly')
                                            月付
                                            @break
                                        @case('yearly')
                                            年付
                                            @break
                                        @case('lifetime')
                                            终身
                                            @break
                                    @endswitch
                                </span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">支付金额：</span>
                                <span class="font-bold text-green-600">¥{{ number_format($subscription->amount, 2) }}</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">服务地区：</span>
                                <span class="font-medium text-gray-900">{{ $subscription->region->name }}</span>
                            </div>
                        </div>
                        
                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <span class="text-gray-600">订阅状态：</span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    已激活
                                </span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">激活时间：</span>
                                <span class="font-medium text-gray-900">{{ $subscription->activated_at->format('Y-m-d H:i') }}</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">到期时间：</span>
                                <span class="font-medium text-gray-900">
                                    @if($subscription->billing_cycle === 'lifetime')
                                        终身有效
                                    @else
                                        {{ $subscription->expires_at->format('Y-m-d H:i') }}
                                    @endif
                                </span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">订单号：</span>
                                <span class="font-mono text-sm text-gray-900">{{ $subscription->paymentOrder->order_number }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 套餐功能 -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">您的套餐功能</h2>
                    
                    @php
                        $features = json_decode($subscription->plan->features, true) ?? [];
                    @endphp
                    
                    <div class="grid md:grid-cols-2 gap-4">
                        @if(!empty($features))
                            @foreach($features as $feature)
                                <div class="flex items-center space-x-3">
                                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span class="text-gray-700">{{ $feature }}</span>
                                </div>
                            @endforeach
                        @else
                            <div class="col-span-2">
                                <div class="space-y-3">
                                    <div class="flex items-center space-x-3">
                                        <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span class="text-gray-700">商品价格监控</span>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span class="text-gray-700">库存状态提醒</span>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span class="text-gray-700">多渠道通知</span>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span class="text-gray-700">24小时客服支持</span>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- 支付信息 -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">支付信息</h2>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">支付方式：</span>
                            <span class="font-medium text-gray-900">
                                @switch($subscription->paymentOrder->payment_method)
                                    @case('alipay')
                                        支付宝
                                        @break
                                    @case('wechat')
                                        微信支付
                                        @break
                                    @case('paypal')
                                        PayPal
                                        @break
                                    @default
                                        {{ $subscription->paymentOrder->payment_method }}
                                @endswitch
                            </span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">交易号：</span>
                            <span class="font-mono text-sm text-gray-900">{{ $subscription->paymentOrder->transaction_id }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">支付时间：</span>
                            <span class="font-medium text-gray-900">{{ $subscription->paymentOrder->paid_at->format('Y-m-d H:i:s') }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">支付状态：</span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                支付成功
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="space-y-6">
                <!-- 下一步操作 -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">开始使用</h3>
                    
                    <div class="space-y-3">
                        <a href="/dashboard" class="card-hover block w-full bg-blue-500 hover:bg-blue-600 text-white text-center py-3 px-4 rounded-lg transition duration-300">
                            进入用户中心
                        </a>
                        
                        <button class="card-hover block w-full bg-green-100 hover:bg-green-200 text-green-700 text-center py-3 px-4 rounded-lg transition duration-300" onclick="alert('添加商品功能正在开发中')">
                            添加关注商品
                        </button>
                        
                        <a href="/history" class="card-hover block w-full bg-gray-100 hover:bg-gray-200 text-gray-700 text-center py-3 px-4 rounded-lg transition duration-300">
                            查看商品历史
                        </a>
                    </div>
                </div>

                <!-- 使用指南 -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 class="font-semibold text-blue-800 mb-2">使用指南</h3>
                    <div class="text-sm text-blue-600 space-y-2">
                        <p>1. 在用户中心管理您的订阅</p>
                        <p>2. 添加您感兴趣的商品</p>
                        <p>3. 设置通知偏好</p>
                        <p>4. 接收价格和库存提醒</p>
                    </div>
                </div>

                <!-- 客服支持 -->
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <h3 class="font-semibold text-gray-800 mb-2">需要帮助？</h3>
                    <div class="text-sm text-gray-600 space-y-1">
                        <p>📞 客服热线：400-123-4567</p>
                        <p>💬 在线客服：9:00-22:00</p>
                        <p>📧 邮箱：<EMAIL></p>
                        <p>📱 微信：商品提醒助手</p>
                    </div>
                </div>

                <!-- 分享推荐 -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h3 class="font-semibold text-yellow-800 mb-2">分享给朋友</h3>
                    <p class="text-sm text-yellow-600 mb-3">推荐朋友使用，双方都可获得优惠</p>
                    <button class="w-full bg-yellow-500 hover:bg-yellow-600 text-white py-2 px-4 rounded-lg text-sm transition duration-300" onclick="shareToFriends()">
                        分享推荐链接
                    </button>
                </div>
            </div>
        </div>

        <!-- 温馨提示 -->
        <div class="mt-8 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6">
            <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="font-semibold text-blue-800 mb-2">温馨提示</h3>
                    <div class="text-sm text-blue-600 space-y-1">
                        <p>• 您的订阅已立即生效，可以开始添加商品关注</p>
                        <p>• 系统将根据您的设置发送通知，请确保通知渠道畅通</p>
                        <p>• 如需修改订阅或有任何问题，请随时联系客服</p>
                        <p>• 感谢您选择我们的服务，祝您使用愉快！</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script>
        // 分享功能
        function shareToFriends() {
            const shareUrl = window.location.origin + '/?ref=' + '{{ Auth::id() }}';
            const shareText = '我在使用这个商品提醒系统，非常好用！推荐给你：' + shareUrl;
            
            if (navigator.share) {
                navigator.share({
                    title: '商品上新提醒系统',
                    text: shareText,
                    url: shareUrl
                });
            } else {
                // 复制到剪贴板
                navigator.clipboard.writeText(shareText).then(() => {
                    alert('推荐链接已复制到剪贴板，快去分享给朋友吧！');
                }).catch(() => {
                    // 降级方案
                    prompt('请复制以下链接分享给朋友：', shareText);
                });
            }
        }

        // 自动关闭成功消息
        setTimeout(function() {
            const alerts = document.querySelectorAll('.bg-green-50');
            alerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 500);
            });
        }, 5000);

        // 页面加载完成后显示庆祝动画
        document.addEventListener('DOMContentLoaded', function() {
            // 可以添加更多动画效果
            console.log('订阅成功页面加载完成');
        });
    </script>
</body>
</html> 