<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserProductFollow extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'product_id',
        'email_notifications',
        'wechat_notifications',
        'notification_settings',
    ];

    protected $casts = [
        'email_notifications' => 'boolean',
        'wechat_notifications' => 'boolean',
        'notification_settings' => 'array',
    ];

    /**
     * 用户关联
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 商品关联
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
