<?php

// 测试登录API接口
$url = 'http://127.0.0.1:8002/api/auth/login';
$data = [
    'userName' => '<EMAIL>',
    'password' => '123456'
];

$options = [
    'http' => [
        'header' => "Content-Type: application/json\r\n",
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "请求失败\n";
    print_r($http_response_header);
} else {
    echo "响应结果:\n";
    echo $result . "\n";
}

// 解析响应
$response = json_decode($result, true);
if ($response) {
    echo "\n解析后的响应:\n";
    print_r($response);
} 