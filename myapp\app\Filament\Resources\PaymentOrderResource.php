<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PaymentOrderResource\Pages;
use App\Filament\Resources\PaymentOrderResource\RelationManagers;
use App\Models\PaymentOrder;
use App\Models\Plan;
use App\Models\Region;
use App\Models\User;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PaymentOrderResource extends Resource
{
    protected static ?string $model = PaymentOrder::class;

    protected static ?string $navigationIcon = 'heroicon-o-credit-card';

    protected static ?string $navigationLabel = '支付订单';

    protected static ?string $modelLabel = '支付订单';

    protected static ?string $pluralModelLabel = '支付订单';

    protected static ?string $navigationGroup = '订单管理';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('订单信息')
                    ->schema([
                        Forms\Components\TextInput::make('order_number')
                            ->label('订单号')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),
                        
                        Forms\Components\Select::make('user_id')
                            ->label('用户')
                            ->relationship('user', 'name')
                            ->required()
                            ->searchable(),
                        
                        Forms\Components\Select::make('plan_id')
                            ->label('套餐')
                            ->relationship('plan', 'name')
                            ->required(),
                        
                        Forms\Components\Select::make('region_id')
                            ->label('地区')
                            ->relationship('region', 'name')
                            ->required(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('金额信息')
                    ->schema([
                        Forms\Components\TextInput::make('original_amount')
                            ->label('原价')
                            ->numeric()
                            ->step(0.01)
                            ->prefix('¥'),
                        
                        Forms\Components\TextInput::make('discount_amount')
                            ->label('优惠金额')
                            ->numeric()
                            ->step(0.01)
                            ->prefix('¥'),
                        
                        Forms\Components\TextInput::make('amount')
                            ->label('实付金额')
                            ->required()
                            ->numeric()
                            ->step(0.01)
                            ->prefix('¥'),
                        
                        Forms\Components\TextInput::make('currency')
                            ->label('货币')
                            ->default('CNY')
                            ->maxLength(3),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('支付信息')
                    ->schema([
                        Forms\Components\Select::make('payment_method')
                            ->label('支付方式')
                            ->options([
                                'alipay' => '支付宝',
                                'wechat' => '微信支付',
                                'paypal' => 'PayPal',
                            ])
                            ->required(),
                        
                        Forms\Components\Select::make('billing_cycle')
                            ->label('计费周期')
                            ->options([
                                'monthly' => '月付',
                                'yearly' => '年付',
                                'lifetime' => '终身',
                            ])
                            ->required(),
                        
                        Forms\Components\Select::make('status')
                            ->label('状态')
                            ->options([
                                'pending' => '待支付',
                                'paid' => '已支付',
                                'failed' => '支付失败',
                                'expired' => '已过期',
                                'cancelled' => '已取消',
                            ])
                            ->required(),
                        
                        Forms\Components\TextInput::make('transaction_id')
                            ->label('交易号')
                            ->maxLength(255),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('其他信息')
                    ->schema([
                        Forms\Components\Textarea::make('failed_reason')
                            ->label('失败原因')
                            ->maxLength(500),
                        
                        Forms\Components\DateTimePicker::make('paid_at')
                            ->label('支付时间'),
                        
                        Forms\Components\DateTimePicker::make('expires_at')
                            ->label('过期时间'),
                    ])
                    ->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('order_number')
                    ->label('订单号')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('user.name')
                    ->label('用户')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('plan.name')
                    ->label('套餐')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('amount')
                    ->label('金额')
                    ->money('CNY')
                    ->sortable(),
                
                Tables\Columns\BadgeColumn::make('status')
                    ->label('状态')
                    ->colors([
                        'warning' => 'pending',
                        'success' => 'paid',
                        'danger' => 'failed',
                        'secondary' => 'expired',
                        'secondary' => 'cancelled',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'pending' => '待支付',
                        'paid' => '已支付',
                        'failed' => '支付失败',
                        'expired' => '已过期',
                        'cancelled' => '已取消',
                        default => $state,
                    }),
                
                Tables\Columns\TextColumn::make('payment_method')
                    ->label('支付方式')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'alipay' => '支付宝',
                        'wechat' => '微信支付',
                        'paypal' => 'PayPal',
                        default => $state,
                    }),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('状态')
                    ->options([
                        'pending' => '待支付',
                        'paid' => '已支付',
                        'failed' => '支付失败',
                        'expired' => '已过期',
                        'cancelled' => '已取消',
                    ]),
                
                Tables\Filters\SelectFilter::make('payment_method')
                    ->label('支付方式')
                    ->options([
                        'alipay' => '支付宝',
                        'wechat' => '微信支付',
                        'paypal' => 'PayPal',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()->label('查看'),
                Tables\Actions\EditAction::make()->label('编辑'),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make()->label('批量删除'),
            ])
            ->defaultSort('created_at', 'desc');
    }
    
    public static function getRelations(): array
    {
        return [
            //
        ];
    }
    
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPaymentOrders::route('/'),
            'create' => Pages\CreatePaymentOrder::route('/create'),
            'view' => Pages\ViewPaymentOrder::route('/{record}'),
            'edit' => Pages\EditPaymentOrder::route('/{record}/edit'),
        ];
    }    
}
