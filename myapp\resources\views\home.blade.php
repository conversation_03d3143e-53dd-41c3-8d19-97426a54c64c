<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ __('app.nav.product_alert_system') }}</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <script src="https://cdn.tailwindcss.com/3.3.3"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css">
    <style>
        body {
            font-family: 'Comic Sans MS', cursive, sans-serif;
            background-color: #fff9f2;
        }
        
        .underline-orange {
            position: relative;
            display: inline-block;
        }
        
        .underline-orange::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: #ff8c42;
            border-radius: 3px;
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(255, 140, 66, 0.2);
        }
        
        .btn-hover {
            transition: all 0.3s ease;
        }
        
        .btn-hover:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(255, 140, 66, 0.3);
        }
        
        .carousel-slide {
            transition: opacity 0.5s ease-in-out;
        }
        
        .carousel-slide.active {
            opacity: 1;
        }
        
        .carousel-slide:not(.active) {
            opacity: 0;
            position: absolute;
            top: 0;
            left: 0;
        }
        
        /* 语言切换样式 */
        .language-dropdown {
            position: absolute;
            top: 4px;
            left: 4px;
            z-index: 20;
        }
        
        .language-btn {
            background: rgba(255, 255, 255, 0.9);
            color: #f97316;
            padding: 8px 16px;
            border-radius: 20px;
            border: 1px solid rgba(255, 140, 66, 0.3);
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .language-btn:hover {
            background: white;
            box-shadow: 0 2px 8px rgba(255, 140, 66, 0.2);
        }
        
        .language-dropdown-content {
            display: none;
            position: absolute;
            background: white;
            min-width: 120px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
            top: 100%;
            left: 0;
            margin-top: 5px;
            border: 1px solid rgba(255, 140, 66, 0.2);
        }
        
        .language-dropdown:hover .language-dropdown-content {
            display: block;
        }
        
        .language-dropdown-content a {
            color: #374151;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            transition: background-color 0.3s;
        }
        
        .language-dropdown-content a:hover {
            background: rgba(255, 140, 66, 0.1);
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- 顶部轮播图区域 -->
    <div class="relative h-64 md:h-96 overflow-hidden rounded-b-3xl shadow-md">
        <!-- 语言切换按钮 -->
        <div class="language-dropdown">
            <button class="language-btn">
                <span>{{ app()->getLocale() == 'zh_CN' ? '🇨🇳' : '🇺🇸' }}</span>
                <span>{{ app()->getLocale() == 'zh_CN' ? '中文' : 'English' }}</span>
                <i class="fas fa-chevron-down ml-1"></i>
            </button>
            <div class="language-dropdown-content">
                <a href="#" onclick="switchLanguage('zh_CN')">🇨🇳 中文</a>
                <a href="#" onclick="switchLanguage('en')">🇺🇸 English</a>
            </div>
        </div>
        
        <!-- 轮播图容器 -->
        <div class="relative h-full w-full" id="carouselContainer">
            <!-- 加载状态 -->
            <div id="carouselLoading" class="carousel-slide active h-full w-full flex items-center justify-center bg-gradient-to-br from-orange-200 to-orange-300">
                <div class="text-center text-white">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
                    <p class="text-lg">Loading banner...</p>
                </div>
            </div>
            
            <!-- 默认背景（当没有轮播图时显示） -->
            <div id="carouselDefault" class="carousel-slide h-full w-full bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600" style="display: none;">
                <div class="h-full flex items-center justify-center">
                    <div class="text-center text-white">
                        <h1 class="text-4xl md:text-6xl font-bold mb-4">商品提醒系统</h1>
                        <p class="text-xl md:text-2xl">Product Alert System</p>
                    </div>
                </div>
            </div>
            
            <!-- 动态轮播图将在这里插入 -->
            <div id="carouselSlides"></div>
        </div>
        
        <!-- 联系客服按钮 -->
        <button class="absolute top-4 right-4 bg-white text-orange-500 px-4 py-2 rounded-full shadow-md btn-hover z-10">
            <i class="fas fa-headset mr-2"></i>Contact Customer Service
        </button>
    </div>

    <!-- 功能按钮区 -->
    <div class="container mx-auto px-4 py-8 flex flex-col md:flex-row justify-center gap-6">
        <a href="/subscribe" class="bg-orange-500 text-white px-6 py-3 rounded-xl shadow-md flex flex-col items-center btn-hover">
            <i class="fas fa-crown text-2xl mb-2"></i>
            <span>subscription</span>
        </a>
        <a href="/login" class="bg-orange-500 text-white px-6 py-3 rounded-xl shadow-md flex flex-col items-center btn-hover">
            <i class="fas fa-user text-2xl mb-2"></i>
            <span>log in</span>
        </a>
        <a href="/products" class="bg-orange-500 text-white px-6 py-3 rounded-xl shadow-md flex flex-col items-center btn-hover">
            <i class="fas fa-shopping-bag text-2xl mb-2"></i>
            <span>New Arrivals</span>
        </a>
    </div>

    <!-- About Us 部分 -->
    <div class="container mx-auto px-4 py-8 max-w-4xl">
        <h2 class="text-3xl font-bold text-center mb-6">
            <span class="underline-orange">About us</span>
        </h2>
        <p class="text-gray-700 text-center mb-6">
            Provide new arrival reminders for H-family bags, supporting over 30 types of bags including Picotin, Lindy, Evelyne, Herbag, Garden Party, etc., as well as new arrival notifications for small leather goods such as KellyGO, ConstanceGO, and Redor.
        </p>
        <p class="text-gray-700 text-center">
            Supported regions: 16 countries and regions including the United States, the United Kingdom, France, Germany, Japan, etc. Reminder methods: E-mail.
        </p>
    </div>

    <!-- 订阅套餐 -->
    <div class="container mx-auto px-4 py-8">
        <h2 class="text-3xl font-bold text-center mb-8">Subscription Plans</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto">
            <!-- 月卡 -->
            <div class="bg-white p-6 rounded-2xl shadow-md card-hover">
                <h3 class="text-xl font-bold text-center mb-4">Monthly Card Subscription</h3>
                <div class="text-orange-500 text-3xl font-bold text-center mb-2">
                    $<span class="text-4xl">30</span>
                    <span class="text-lg text-gray-500">USD</span>
                </div>
                <p class="text-orange-400 text-center mb-4">Subscription period: 31 days</p>
                <button class="w-full bg-orange-500 text-white py-3 rounded-lg btn-hover font-semibold">
                    Subscribe Now
                </button>
            </div>
            
            <!-- 季卡 -->
            <div class="bg-white p-6 rounded-2xl shadow-md card-hover">
                <h3 class="text-xl font-bold text-center mb-4">Quarterly Card Subscription</h3>
                <div class="text-orange-500 text-3xl font-bold text-center mb-2">
                    $<span class="text-4xl">81</span>
                    <span class="text-lg text-gray-500">USD</span>
                </div>
                <p class="text-orange-400 text-center mb-4">Subscription period: 93 days</p>
                <button class="w-full bg-orange-500 text-white py-3 rounded-lg btn-hover font-semibold">
                    Subscribe Now
                </button>
            </div>
            
            <!-- 半年卡 -->
            <div class="bg-white p-6 rounded-2xl shadow-md card-hover">
                <h3 class="text-xl font-bold text-center mb-4">Semi-annual Card Subscription</h3>
                <div class="text-orange-500 text-3xl font-bold text-center mb-2">
                    $<span class="text-4xl">142</span>
                    <span class="text-lg text-gray-500">USD</span>
                </div>
                <p class="text-orange-400 text-center mb-4">Subscription period: 186 days</p>
                <button class="w-full bg-orange-500 text-white py-3 rounded-lg btn-hover font-semibold">
                    Subscribe Now
                </button>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-white py-6 mt-8 border-t border-orange-100">
        <div class="container mx-auto px-4 text-center">
            <div class="flex justify-center gap-4 mb-4">
                <a href="#" class="text-orange-500 hover:text-orange-700 transition-colors">服务条款</a>
                <span class="text-gray-300">|</span>
                <a href="#" class="text-orange-500 hover:text-orange-700 transition-colors">隐私政策</a>
            </div>
            <p class="text-gray-500 text-sm">商品提醒管理系统 © 2024</p>
            <p class="text-gray-400 text-xs mt-2">专业的商品监控与提醒服务</p>
        </div>
    </footer>

    <script>
        // 语言切换功能
        function switchLanguage(locale) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/language/switch/${locale}`;
            
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = '_token';
            csrfInput.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            form.appendChild(csrfInput);
            
            const returnUrlInput = document.createElement('input');
            returnUrlInput.type = 'hidden';
            returnUrlInput.name = 'return_url';
            returnUrlInput.value = window.location.href;
            form.appendChild(returnUrlInput);
            
            document.body.appendChild(form);
            form.submit();
        }
        
        // 轮播图相关变量
        let banners = [];
        let currentSlide = 0;
        let slideInterval = null;
        
        // 加载轮播图数据
        async function loadBanners() {
            try {
                const response = await fetch('/api/banners', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    if (data.success && data.data && data.data.length > 0) {
                        banners = data.data;
                        renderBanners();
                        startAutoSlide();
                    } else {
                        showDefault();
                    }
                } else {
                    showDefault();
                }
                
            } catch (error) {
                console.error('加载轮播图失败:', error);
                showDefault();
            }
        }
        
        // 显示默认背景
        function showDefault() {
            document.getElementById('carouselLoading').style.display = 'none';
            document.getElementById('carouselDefault').style.display = 'block';
            document.getElementById('carouselDefault').classList.add('active');
        }
        
        // 渲染轮播图
        function renderBanners() {
            if (!banners || banners.length === 0) {
                showDefault();
                return;
            }
            
            const slidesContainer = document.getElementById('carouselSlides');
            slidesContainer.innerHTML = '';
            
            // 隐藏加载状态
            document.getElementById('carouselLoading').style.display = 'none';
            
            // 生成轮播图项目
            banners.forEach((banner, index) => {
                const slide = document.createElement('div');
                slide.className = `carousel-slide h-full w-full ${index === 0 ? 'active' : ''}`;
                slide.style.cursor = banner.link && banner.link !== '#' ? 'pointer' : 'default';
                
                slide.innerHTML = `
                    <img src="${banner.image_url}" 
                         alt="${banner.alt_text || banner.title}" 
                         class="w-full h-full object-cover"
                         onerror="this.parentElement.innerHTML='<div class=\\"h-full bg-gradient-to-br from-orange-400 to-orange-600 flex items-center justify-center\\"><div class=\\"text-center text-white\\"><h2 class=\\"text-2xl font-bold\\">${banner.title}</h2><p class=\\"mt-2\\">${banner.description || ''}</p></div></div>'"
                         />
                    ${banner.title || banner.description ? `
                        <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-6">
                            <div class="text-white">
                                ${banner.title ? `<h3 class="text-xl font-bold mb-2">${banner.title}</h3>` : ''}
                                ${banner.description ? `<p class="text-sm opacity-90">${banner.description}</p>` : ''}
                            </div>
                        </div>
                    ` : ''}
                `;
                
                // 添加点击事件
                if (banner.link && banner.link !== '#' && banner.link !== '') {
                    slide.addEventListener('click', () => {
                        if (banner.target === '_blank') {
                            window.open(banner.link, '_blank');
                        } else {
                            window.location.href = banner.link;
                        }
                    });
                }
                
                slidesContainer.appendChild(slide);
            });
            
            // 开始轮播
            if (banners.length > 1) {
                startAutoSlide();
            }
        }
        
        // 显示指定幻灯片
        function showSlide(index) {
            const slides = document.querySelectorAll('#carouselSlides .carousel-slide');
            slides.forEach((slide, i) => {
                slide.classList.toggle('active', i === index);
            });
        }
        
        // 下一张幻灯片
        function nextSlide() {
            if (banners.length === 0) return;
            currentSlide = (currentSlide + 1) % banners.length;
            showSlide(currentSlide);
        }
        
        // 开始自动播放
        function startAutoSlide() {
            if (banners.length <= 1) return;
            
            slideInterval = setInterval(() => {
                nextSlide();
            }, 5000); // 每5秒切换一次
        }
        
        // 停止自动播放
        function stopAutoSlide() {
            if (slideInterval) {
                clearInterval(slideInterval);
                slideInterval = null;
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 加载轮播图
            loadBanners();
            
            // 鼠标悬停时停止自动播放
            const carousel = document.getElementById('carouselContainer');
            if (carousel) {
                carousel.addEventListener('mouseenter', stopAutoSlide);
                carousel.addEventListener('mouseleave', () => {
                    if (banners.length > 1) {
                        startAutoSlide();
                    }
                });
            }
            
            // 平滑滚动
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });
        });
        
        // 页面可见性变化时控制自动播放
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                stopAutoSlide();
            } else {
                if (banners.length > 1) {
                    startAutoSlide();
                }
            }
        });
    </script>
</body>
</html> 