<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\RegisterController;
use App\Http\Controllers\LoginController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\HistoryController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\LanguageController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// 语言切换路由（放在最前面以确保优先匹配）
Route::post('/language/switch/{locale}', [LanguageController::class, 'switchLanguage'])->name('language.switch');
Route::post('/language/reset', [LanguageController::class, 'resetLanguage'])->name('language.reset');
Route::get('/language/current', [LanguageController::class, 'getCurrentLanguage'])->name('language.current');
Route::get('/language/supported', [LanguageController::class, 'getSupportedLanguages'])->name('language.supported');

// 首页路由
Route::get('/', function () {
    return view('home');
})->name('home');

// 商品浏览路由（公开访问）
Route::get('/products', [ProductController::class, 'index'])->name('products.index');
Route::get('/products/{id}', [ProductController::class, 'show'])->name('products.show');
Route::get('/api/products/{id}/data', [ProductController::class, 'getProductData'])->name('api.products.data');

// 订阅相关路由（只允许前端用户访问）
Route::get('/subscribe', [SubscriptionController::class, 'showSubscriptionForm'])->name('subscription.form');
Route::post('/subscribe', [SubscriptionController::class, 'processSubscription'])->name('subscription.process')->middleware(['auth', 'user.role']);

// 支付相关路由（只允许前端用户访问）
Route::middleware(['auth', 'user.role'])->group(function () {
    Route::get('/subscription/payment/{orderId}', [SubscriptionController::class, 'showPaymentPage'])->name('subscription.payment');
    Route::post('/subscription/payment/{orderId}', [SubscriptionController::class, 'processPayment'])->name('subscription.process-payment');
    Route::get('/subscription/success/{subscriptionId}', [SubscriptionController::class, 'subscriptionSuccess'])->name('subscription.success');
    Route::post('/subscription/cancel/{subscriptionId}', [SubscriptionController::class, 'cancelSubscription'])->name('subscription.cancel');
});

// 用户登录路由
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login'])->name('login.submit');
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

// 用户注册路由
Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
Route::post('/register', [RegisterController::class, 'register'])->name('register.submit');
Route::get('/register/success', [RegisterController::class, 'registrationSuccess'])->name('register.success');

// 前端用户中心（只允许前端用户访问）
Route::middleware(['auth', 'user.role'])->group(function () {
    Route::get('/dashboard', [LoginController::class, 'dashboard'])->name('dashboard');
    
    // 历史商品相关路由
    Route::get('/history', [HistoryController::class, 'index'])->name('history');
    Route::delete('/history/products/{productId}', [HistoryController::class, 'unfollowProduct'])->name('history.unfollow');
    Route::put('/history/products/{productId}/settings', [HistoryController::class, 'updateFollowSettings'])->name('history.update-settings');
    Route::get('/api/products/{productId}/details', [HistoryController::class, 'getProductDetails'])->name('api.product.details');
    
    // 商品关注相关路由
    Route::post('/products/{productId}/follow', [HistoryController::class, 'followProduct'])->name('products.follow');
    Route::post('/products/{productId}/update-notification-settings', [HistoryController::class, 'updateFollowSettings'])->name('products.update-notification-settings');
    Route::get('/api/products/{productId}/follow-status', [HistoryController::class, 'checkFollowStatus'])->name('api.products.follow-status');
});

// 后台管理员路由重定向
Route::middleware(['auth', 'admin.role'])->group(function () {
    // 管理员访问前端首页时重定向到后台
    Route::get('/admin-redirect', function () {
        return redirect('/admin')->with('info', '管理员用户已重定向到后台管理系统');
    })->name('admin.redirect');
});

// API路由
Route::post('/api/check-email', [RegisterController::class, 'checkEmail'])->name('api.check-email');
Route::post('/api/auth-status', [LoginController::class, 'checkAuthStatus'])->name('api.auth-status');
Route::post('/api/subscription/price', [SubscriptionController::class, 'getPlanPrice'])->name('api.subscription.price');

// 测试页面
Route::get('/test', function () {
    return 'Laravel 应用运行正常！当前时间：' . date('Y-m-d H:i:s');
}); 