<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - 商品提醒系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm">
        <div class="max-w-md mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <h1 class="text-xl font-bold text-gray-900">商品提醒系统</h1>
                <div class="space-x-2">
                    <a href="/" class="text-gray-600 hover:text-gray-900">首页</a>
                    <a href="/login" class="bg-blue-500 text-white px-3 py-1 rounded">登录</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 注册表单 -->
    <div class="min-h-screen flex items-center justify-center py-12 px-4">
        <div class="max-w-md w-full space-y-8">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-gray-900">创建账号</h2>
                <p class="mt-2 text-sm text-gray-600">
                    已有账号？<a href="/login" class="text-blue-600 hover:text-blue-500">立即登录</a>
                </p>
            </div>

            <!-- 消息提示 -->
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    {{ session('error') }}
                </div>
            @endif

            <!-- 注册表单 -->
            <form class="mt-8 space-y-6" action="/register" method="POST">
                @csrf
                <div class="space-y-4">
                    <!-- 姓名 -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700">姓名</label>
                        <input 
                            id="name" 
                            name="name" 
                            type="text" 
                            required 
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-500 @enderror"
                            placeholder="请输入您的姓名"
                            value="{{ old('name') }}"
                        >
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- 邮箱 -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">邮箱地址</label>
                        <input 
                            id="email" 
                            name="email" 
                            type="email" 
                            required 
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('email') border-red-500 @enderror"
                            placeholder="请输入您的邮箱"
                            value="{{ old('email') }}"
                        >
                        @error('email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- 密码 -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700">密码</label>
                        <input 
                            id="password" 
                            name="password" 
                            type="password" 
                            required 
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('password') border-red-500 @enderror"
                            placeholder="请输入密码（至少6位）"
                        >
                        @error('password')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- 确认密码 -->
                    <div>
                        <label for="password_confirmation" class="block text-sm font-medium text-gray-700">确认密码</label>
                        <input 
                            id="password_confirmation" 
                            name="password_confirmation" 
                            type="password" 
                            required 
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="请再次输入密码"
                        >
                    </div>

                    <!-- 地区选择 -->
                    <div>
                        <label for="region_id" class="block text-sm font-medium text-gray-700">所在地区（可选）</label>
                        <select 
                            id="region_id" 
                            name="region_id" 
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="">请选择地区</option>
                            <option value="1">中国 - 北京</option>
                            <option value="2">中国 - 上海</option>
                            <option value="3">中国 - 广州</option>
                            <option value="4">中国 - 深圳</option>
                            <option value="5">中国 - 珠海</option>
                            <option value="6">美国 - 洛杉矶</option>
                            <option value="7">美国 - 旧金山</option>
                            <option value="8">美国 - 纽约</option>
                        </select>
                        @error('region_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- 通知设置 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">通知设置</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" name="email_notifications" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">接收邮件通知</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="wechat_notifications" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">接收微信通知</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="price_alerts" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">价格变动提醒</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="stock_alerts" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">库存变动提醒</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 服务条款 -->
                <div class="flex items-start">
                    <input 
                        id="agree_terms" 
                        name="agree_terms" 
                        type="checkbox" 
                        required
                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                    >
                    <label for="agree_terms" class="ml-2 block text-sm text-gray-700">
                        我已阅读并同意<a href="#" class="text-blue-600 hover:text-blue-500">服务条款</a>和<a href="#" class="text-blue-600 hover:text-blue-500">隐私政策</a>
                    </label>
                </div>

                <!-- 注册按钮 -->
                <div>
                    <button 
                        type="submit" 
                        class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                        创建账号
                    </button>
                </div>
            </form>
        </div>
    </div>
</body>
</html> 