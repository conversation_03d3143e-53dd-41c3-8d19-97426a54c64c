<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('plans', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // 套餐名称，如 "Basic Plan", "Premium Plan"
            $table->string('slug')->unique(); // 套餐标识符
            $table->text('description'); // 套餐描述
            $table->decimal('price', 10, 2); // 价格
            $table->string('currency', 3)->default('USD'); // 货币代码
            $table->enum('billing_cycle', ['monthly', 'yearly', 'lifetime']); // 计费周期
            $table->integer('duration_days')->nullable(); // 持续天数（lifetime为null）
            $table->integer('max_products')->default(0); // 最大关注商品数量，0表示无限制
            $table->integer('max_notifications')->default(0); // 最大通知数量，0表示无限制
            $table->json('features')->nullable(); // 功能特性JSON
            $table->boolean('is_popular')->default(false); // 是否为热门套餐
            $table->boolean('is_active')->default(true); // 是否启用
            $table->integer('sort_order')->default(0); // 排序
            $table->timestamps();

            $table->index(['is_active', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('plans');
    }
};
