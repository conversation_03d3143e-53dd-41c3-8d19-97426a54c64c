<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('banners', function (Blueprint $table) {
            $table->id();
            $table->string('title'); // 轮播图标题
            $table->string('image'); // 图片路径
            $table->string('link')->nullable(); // 点击链接
            $table->text('description')->nullable(); // 描述
            $table->boolean('is_active')->default(true); // 是否启用
            $table->integer('sort_order')->default(0); // 排序，数字越小越靠前
            $table->enum('target', ['_self', '_blank'])->default('_self'); // 链接打开方式
            $table->timestamp('start_time')->nullable(); // 开始显示时间
            $table->timestamp('end_time')->nullable(); // 结束显示时间
            $table->string('alt_text')->nullable(); // 图片alt属性
            $table->timestamps();

            $table->index(['is_active', 'sort_order']);
            $table->index(['start_time', 'end_time']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('banners');
    }
}; 