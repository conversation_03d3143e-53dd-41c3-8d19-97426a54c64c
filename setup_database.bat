@echo off
chcp 65001 >nul
echo.
echo =============================================
echo         数据库配置向导 (PHPStudy环境)
echo =============================================
echo.

:: 设置PHPStudy的PHP路径
set PHP_PATH=D:\phpstudy_pro\Extensions\php\php8.0.2nts\php.exe

echo 📋 当前数据库配置问题:
echo    - 数据库 'laravel_app' 不存在
echo    - 需要在PHPStudy中创建数据库
echo.

echo 🛠️ 解决方案选择:
echo    1. 使用PHPStudy的MySQL (推荐)
echo    2. 修改为SQLite (需要启用SQLite扩展)
echo.

set /p choice="请选择解决方案 (1/2): "

if "%choice%"=="1" (
    echo.
    echo 📋 选择方案1: 使用PHPStudy的MySQL
    echo.
    echo 💡 请按照以下步骤操作:
    echo.
    echo 1️⃣ 打开PHPStudy控制面板
    echo 2️⃣ 启动MySQL服务
    echo 3️⃣ 点击"数据库"或"phpMyAdmin"
    echo 4️⃣ 创建新数据库:
    echo    - 数据库名: laravel_app
    echo    - 字符集: utf8mb4_unicode_ci
    echo.
    echo 5️⃣ 确认数据库连接信息:
    echo    - 主机: 127.0.0.1
    echo    - 端口: 3306
    echo    - 用户名: root
    echo    - 密码: root123 (或您设置的密码)
    echo.
    echo ⚠️  如果密码不对，请修改 .env 文件中的 DB_PASSWORD
    echo.
    
    set /p confirm="数据库创建完成后，按任意键继续..."
    
    echo.
    echo 🔄 测试数据库连接...
    %PHP_PATH% artisan migrate:status
    
    if errorlevel 1 (
        echo ❌ 数据库连接失败
        echo 💡 请检查:
        echo    1. PHPStudy MySQL服务是否启动
        echo    2. 数据库 laravel_app 是否已创建
        echo    3. .env 文件中的数据库密码是否正确
        pause
        exit /b 1
    ) else (
        echo ✅ 数据库连接成功！
        echo.
        echo 🚀 开始运行数据库迁移...
        %PHP_PATH% artisan migrate
        
        if errorlevel 1 (
            echo ❌ 数据库迁移失败
            pause
            exit /b 1
        ) else (
            echo ✅ 数据库迁移完成！
            echo.
            echo 👤 创建管理员用户...
            %PHP_PATH% artisan make:filament-user
        )
    )
) else if "%choice%"=="2" (
    echo.
    echo 📋 选择方案2: 使用SQLite
    echo.
    echo ⚠️  需要在PHPStudy中启用SQLite扩展
    echo.
    echo 💡 请按照以下步骤操作:
    echo 1️⃣ 打开 D:\phpstudy_pro\Extensions\php\php8.0.2nts\php.ini
    echo 2️⃣ 找到并取消注释以下行:
    echo    ;extension=sqlite3
    echo    ;extension=pdo_sqlite
    echo    改为:
    echo    extension=sqlite3
    echo    extension=pdo_sqlite
    echo 3️⃣ 重启PHPStudy
    echo.
    
    set /p confirm="配置完成后，按任意键继续..."
    
    echo.
    echo 🔄 修改数据库配置为SQLite...
    
    :: 创建SQLite数据库文件
    if not exist "database\database.sqlite" (
        echo. > database\database.sqlite
        echo ✅ 创建SQLite数据库文件
    )
    
    :: 修改.env文件
    powershell -Command "(Get-Content .env) -replace 'DB_CONNECTION=mysql', 'DB_CONNECTION=sqlite' -replace 'DB_DATABASE=laravel_app', 'DB_DATABASE=database/database.sqlite' | Set-Content .env.sqlite"
    copy .env.sqlite .env
    
    echo ✅ 数据库配置已修改为SQLite
    echo.
    echo 🚀 开始运行数据库迁移...
    %PHP_PATH% artisan migrate
    
    if errorlevel 1 (
        echo ❌ SQLite扩展未启用，请按照上述步骤启用
        pause
        exit /b 1
    ) else (
        echo ✅ 数据库迁移完成！
        echo.
        echo 👤 创建管理员用户...
        %PHP_PATH% artisan make:filament-user
    )
) else (
    echo ❌ 无效选择
    pause
    exit /b 1
)

echo.
echo 🎉 数据库配置完成！
echo.
echo 💡 下一步: 运行 start_filament_phpstudy.bat 启动服务器
pause 