<template>
  <div class="app-container">
    <!-- 头部操作栏 -->
    <div class="header-actions">
      <el-row :gutter="16" align="middle">
        <el-col :span="12">
          <h2 class="page-title">用户日志</h2>
        </el-col>
        <el-col :span="12" class="text-right">
          <el-button type="primary" @click="handleExport" :icon="Download">
            导出日志
          </el-button>
          <el-button type="danger" @click="handleClearLogs" :icon="Delete">
            清空日志
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="用户名">
          <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable />
        </el-form-item>
        <el-form-item label="操作类型">
          <el-select v-model="searchForm.action" placeholder="请选择操作类型" clearable>
            <el-option label="登录" value="login" />
            <el-option label="登出" value="logout" />
            <el-option label="创建" value="create" />
            <el-option label="编辑" value="edit" />
            <el-option label="删除" value="delete" />
            <el-option label="查看" value="view" />
          </el-select>
        </el-form-item>
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
          <el-button @click="handleReset" :icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 日志统计卡片 -->
    <el-row :gutter="16" class="stats-cards">
      <el-col :span="6" v-for="stat in statsData" :key="stat.key">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon" :class="stat.iconClass">
              <el-icon :size="24">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stat.value }}</div>
              <div class="stats-label">{{ stat.label }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 日志列表 -->
    <el-card class="list-card">
      <el-table :data="logList" v-loading="loading" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" min-width="120" />
        <el-table-column label="操作类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getActionTagType(row.action)">
              {{ getActionLabel(row.action) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="操作描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="ip_address" label="IP地址" width="140" />
        <el-table-column prop="user_agent" label="用户代理" min-width="200" show-overflow-tooltip />
        <el-table-column prop="created_at" label="操作时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleViewDetail(row)" :icon="View">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :small="false"
        :disabled="loading"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="pagination"
      />
    </el-card>

    <!-- 日志详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="日志详情"
      width="800px"
    >
      <div class="log-detail" v-if="selectedLog">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="ID">{{ selectedLog.id }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ selectedLog.username }}</el-descriptions-item>
          <el-descriptions-item label="操作类型">
            <el-tag :type="getActionTagType(selectedLog.action)">
              {{ getActionLabel(selectedLog.action) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="操作描述">{{ selectedLog.description }}</el-descriptions-item>
          <el-descriptions-item label="IP地址">{{ selectedLog.ip_address }}</el-descriptions-item>
          <el-descriptions-item label="用户代理">{{ selectedLog.user_agent }}</el-descriptions-item>
          <el-descriptions-item label="请求方法">{{ selectedLog.method }}</el-descriptions-item>
          <el-descriptions-item label="请求URL">{{ selectedLog.url }}</el-descriptions-item>
          <el-descriptions-item label="操作时间">{{ formatTime(selectedLog.created_at) }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="log-extra" v-if="selectedLog.extra_data">
          <h4>额外数据</h4>
          <pre>{{ JSON.stringify(selectedLog.extra_data, null, 2) }}</pre>
        </div>
      </div>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, Delete, Search, Refresh, View, User, Warning, Check, InfoFilled } from '@element-plus/icons-vue'

// 定义组件名称
defineOptions({
  name: 'UserLogs'
})

// 响应式数据
const loading = ref(false)
const logList = ref([])
const searchForm = reactive({
  username: '',
  action: '',
  dateRange: []
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 统计数据
const statsData = ref([
  { key: 'total', label: '总日志数', value: 0, icon: InfoFilled, iconClass: 'text-blue-500' },
  { key: 'today', label: '今日日志', value: 0, icon: Check, iconClass: 'text-green-500' },
  { key: 'login', label: '登录次数', value: 0, icon: User, iconClass: 'text-orange-500' },
  { key: 'error', label: '错误日志', value: 0, icon: Warning, iconClass: 'text-red-500' }
])

// 详情弹窗
const detailDialogVisible = ref(false)
const selectedLog = ref(null)

// 方法
const loadLogList = async () => {
  loading.value = true
  try {
    // 模拟数据
    const mockData = [
      {
        id: 1,
        username: 'admin',
        action: 'login',
        description: '管理员登录系统',
        ip_address: '*************',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        method: 'POST',
        url: '/api/auth/login',
        created_at: '2024-01-15 09:30:00',
        extra_data: {
          login_source: 'web',
          device: 'desktop'
        }
      },
      {
        id: 2,
        username: 'manager',
        action: 'create',
        description: '创建了新的轮播图',
        ip_address: '*************',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        method: 'POST',
        url: '/api/banners',
        created_at: '2024-01-15 10:15:00',
        extra_data: {
          banner_id: 123,
          banner_title: '新年活动'
        }
      },
      {
        id: 3,
        username: 'editor',
        action: 'edit',
        description: '编辑了轮播图信息',
        ip_address: '*************',
        user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        method: 'PUT',
        url: '/api/banners/123',
        created_at: '2024-01-15 11:45:00',
        extra_data: {
          banner_id: 123,
          changes: ['title', 'image']
        }
      },
      {
        id: 4,
        username: 'admin',
        action: 'delete',
        description: '删除了过期的轮播图',
        ip_address: '*************',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        method: 'DELETE',
        url: '/api/banners/456',
        created_at: '2024-01-15 14:20:00',
        extra_data: {
          banner_id: 456,
          reason: 'expired'
        }
      }
    ]
    
    logList.value = mockData
    pagination.total = mockData.length
    
    // 更新统计数据
    statsData.value[0].value = mockData.length
    statsData.value[1].value = mockData.filter(log => log.created_at.includes('2024-01-15')).length
    statsData.value[2].value = mockData.filter(log => log.action === 'login').length
    statsData.value[3].value = mockData.filter(log => log.action === 'error').length
    
  } catch (error) {
    ElMessage.error('获取日志列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadLogList()
}

const handleReset = () => {
  Object.assign(searchForm, {
    username: '',
    action: '',
    dateRange: []
  })
  handleSearch()
}

const handleExport = () => {
  ElMessage.success('导出功能开发中...')
}

const handleClearLogs = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有日志吗？此操作不可逆！',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('日志清空成功')
    loadLogList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清空日志失败')
    }
  }
}

const handleViewDetail = (row: any) => {
  selectedLog.value = row
  detailDialogVisible.value = true
}

// 工具函数
const getActionTagType = (action: string) => {
  const typeMap = {
    'login': 'success',
    'logout': 'info',
    'create': 'primary',
    'edit': 'warning',
    'delete': 'danger',
    'view': 'info',
    'error': 'danger'
  }
  return typeMap[action] || 'info'
}

const getActionLabel = (action: string) => {
  const labelMap = {
    'login': '登录',
    'logout': '登出',
    'create': '创建',
    'edit': '编辑',
    'delete': '删除',
    'view': '查看',
    'error': '错误'
  }
  return labelMap[action] || action
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadLogList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadLogList()
}

// 生命周期
onMounted(() => {
  loadLogList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.header-actions {
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.stats-card:hover {
  transform: translateY(-2px);
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.list-card {
  margin-bottom: 20px;
}

.text-right {
  text-align: right;
}

.pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.log-detail {
  margin-bottom: 20px;
}

.log-extra {
  margin-top: 20px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 4px;
}

.log-extra h4 {
  margin-bottom: 12px;
  color: #303133;
}

.log-extra pre {
  background: #fff;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  font-size: 12px;
  color: #606266;
  overflow-x: auto;
}
</style> 