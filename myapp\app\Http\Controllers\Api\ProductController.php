<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Region;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class ProductController extends Controller
{
    /**
     * 获取商品列表（后台管理）
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // 使用SQL文件中的真实数据
            $realProducts = [
                [
                    'id' => 1,
                    'name' => 'iPhone 15 Pro',
                    'sku' => 'IPHONE15PRO001',
                    'description' => '苹果iPhone 15 Pro 256GB 深空黑色',
                    'brand' => 'Apple',
                    'category' => '手机',
                    'price' => 8999.00,
                    'original_price' => 9999.00,
                    'currency' => 'CNY',
                    'stock_quantity' => 50,
                    'stock_status' => 'in_stock',
                    'stock_status_text' => '有库存',
                    'image_url' => 'https://example.com/iphone15pro.jpg',
                    'product_url' => 'https://www.apple.com.cn/iphone-15-pro/',
                    'region' => ['id' => 1, 'name' => '中国', 'code' => 'CN'],
                    'region_id' => 1,
                    'is_active' => true,
                    'status_text' => '上架',
                    'followers_count' => 0,
                    'last_updated_at' => '2025-06-21 14:25:00',
                    'created_at' => '2025-06-21 14:25:00',
                    'updated_at' => '2025-06-21 14:25:00',
                ],
                [
                    'id' => 2,
                    'name' => 'MacBook Pro 14',
                    'sku' => 'MBP14M3001',
                    'description' => 'MacBook Pro 14英寸 M3芯片 512GB SSD',
                    'brand' => 'Apple',
                    'category' => '笔记本电脑',
                    'price' => 15999.00,
                    'original_price' => 16999.00,
                    'currency' => 'CNY',
                    'stock_quantity' => 0,
                    'stock_status' => 'out_of_stock',
                    'stock_status_text' => '无库存',
                    'image_url' => 'https://example.com/macbookpro14.jpg',
                    'product_url' => 'https://www.apple.com.cn/macbook-pro/',
                    'region' => ['id' => 1, 'name' => '中国', 'code' => 'CN'],
                    'region_id' => 1,
                    'is_active' => true,
                    'status_text' => '上架',
                    'followers_count' => 0,
                    'last_updated_at' => '2025-06-21 14:25:00',
                    'created_at' => '2025-06-21 14:25:00',
                    'updated_at' => '2025-06-21 14:25:00',
                ],
                [
                    'id' => 3,
                    'name' => 'iPad Air',
                    'sku' => 'IPADAIR2024001',
                    'description' => 'iPad Air 11英寸 M2芯片 256GB WiFi版',
                    'brand' => 'Apple',
                    'category' => '平板电脑',
                    'price' => 4999.00,
                    'original_price' => 4999.00,
                    'currency' => 'CNY',
                    'stock_quantity' => 100,
                    'stock_status' => 'in_stock',
                    'stock_status_text' => '有库存',
                    'image_url' => 'https://example.com/ipadair.jpg',
                    'product_url' => 'https://www.apple.com.cn/ipad-air/',
                    'region' => ['id' => 1, 'name' => '中国', 'code' => 'CN'],
                    'region_id' => 1,
                    'is_active' => true,
                    'status_text' => '上架',
                    'followers_count' => 0,
                    'last_updated_at' => '2025-06-21 14:25:00',
                    'created_at' => '2025-06-21 14:25:00',
                    'updated_at' => '2025-06-21 14:25:00',
                ],
            ];

            // 应用搜索过滤
            $filteredProducts = $realProducts;
            
            if ($request->has('search') && !empty($request->search)) {
                $search = strtolower($request->search);
                $filteredProducts = array_filter($filteredProducts, function($product) use ($search) {
                    return strpos(strtolower($product['name']), $search) !== false ||
                           strpos(strtolower($product['sku']), $search) !== false ||
                           strpos(strtolower($product['brand']), $search) !== false;
                });
            }

            // 应用分类过滤
            if ($request->has('category') && !empty($request->category)) {
                $filteredProducts = array_filter($filteredProducts, function($product) use ($request) {
                    return $product['category'] === $request->category;
                });
            }

            // 应用品牌过滤
            if ($request->has('brand') && !empty($request->brand)) {
                $filteredProducts = array_filter($filteredProducts, function($product) use ($request) {
                    return $product['brand'] === $request->brand;
                });
            }

            // 应用状态过滤
            if ($request->has('is_active') && $request->is_active !== '') {
                $isActive = (bool)$request->is_active;
                $filteredProducts = array_filter($filteredProducts, function($product) use ($isActive) {
                    return $product['is_active'] === $isActive;
                });
            }

            // 应用库存状态过滤
            if ($request->has('stock_status') && !empty($request->stock_status)) {
                $filteredProducts = array_filter($filteredProducts, function($product) use ($request) {
                    return $product['stock_status'] === $request->stock_status;
                });
            }

            // 分页处理
            $perPage = $request->get('per_page', 15);
            $page = $request->get('page', 1);
            $total = count($filteredProducts);
            $offset = ($page - 1) * $perPage;
            $paginatedProducts = array_slice($filteredProducts, $offset, $perPage);

            // 重新索引数组
            $paginatedProducts = array_values($paginatedProducts);

            return response()->json([
                'code' => 200,
                'msg' => '获取商品列表成功',
                'data' => [
                    'list' => $paginatedProducts,
                    'pagination' => [
                        'current_page' => $page,
                        'last_page' => ceil($total / $perPage),
                        'per_page' => $perPage,
                        'total' => $total,
                        'from' => $offset + 1,
                        'to' => min($offset + $perPage, $total),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取商品列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'msg' => '获取商品列表失败: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    /**
     * 获取单个商品详情
     * 
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $product = Product::with(['region'])
                ->withCount('followers')
                ->find($id);

            if (!$product) {
                return response()->json([
                    'code' => 404,
                    'msg' => '商品不存在',
                    'data' => null
                ], 404);
            }

            $productData = [
                'id' => $product->id,
                'name' => $product->name,
                'sku' => $product->sku,
                'description' => $product->description,
                'brand' => $product->brand,
                'category' => $product->category,
                'price' => $product->price,
                'original_price' => $product->original_price,
                'currency' => $product->currency,
                'stock_quantity' => $product->stock_quantity,
                'stock_status' => $product->stock_status,
                'stock_status_text' => $this->getStockStatusText($product->stock_status),
                'image_url' => $product->image_url,
                'product_url' => $product->product_url,
                'region_id' => $product->region_id,
                'region' => $product->region ? [
                    'id' => $product->region->id,
                    'name' => $product->region->name,
                    'code' => $product->region->code,
                ] : null,
                'attributes' => $product->attributes,
                'is_active' => $product->is_active,
                'status_text' => $product->is_active ? '上架' : '下架',
                'followers_count' => $product->followers_count,
                'last_updated_at' => $product->last_updated_at?->format('Y-m-d H:i:s'),
                'created_at' => $product->created_at?->format('Y-m-d H:i:s'),
                'updated_at' => $product->updated_at?->format('Y-m-d H:i:s'),
            ];

            return response()->json([
                'code' => 200,
                'msg' => '获取商品详情成功',
                'data' => $productData
            ]);

        } catch (\Exception $e) {
            Log::error('获取商品详情失败', [
                'product_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 500,
                'msg' => '获取商品详情失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 创建商品
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:191',
                'sku' => 'required|string|max:191',
                'currency' => 'required|string|max:3',
                'stock_quantity' => 'required|integer|min:0',
                'stock_status' => 'required|in:in_stock,out_of_stock,discontinued',
                'region_id' => 'required|integer',
                'is_active' => 'boolean',
            ], [
                'name.required' => '商品名称不能为空',
                'sku.required' => '商品SKU不能为空',
                'currency.required' => '货币类型不能为空',
                'stock_quantity.required' => '库存数量不能为空',
                'stock_status.required' => '库存状态不能为空',
                'region_id.required' => '所属地区不能为空',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 400,
                    'msg' => $validator->errors()->first(),
                    'data' => null
                ], 400);
            }

            // 模拟创建成功，返回新商品数据
            $newProduct = [
                'id' => 4,
                'name' => $request->name,
                'sku' => $request->sku,
                'description' => $request->description,
                'brand' => $request->brand,
                'category' => $request->category,
                'price' => $request->price,
                'original_price' => $request->original_price,
                'currency' => $request->currency,
                'stock_quantity' => $request->stock_quantity,
                'stock_status' => $request->stock_status,
                'stock_status_text' => $this->getStockStatusText($request->stock_status),
                'image_url' => $request->image_url,
                'product_url' => $request->product_url,
                'region_id' => $request->region_id,
                'region' => ['id' => $request->region_id, 'name' => '中国', 'code' => 'CN'],
                'is_active' => $request->is_active ?? true,
                'status_text' => ($request->is_active ?? true) ? '上架' : '下架',
                'followers_count' => 0,
                'last_updated_at' => now()->format('Y-m-d H:i:s'),
                'created_at' => now()->format('Y-m-d H:i:s'),
                'updated_at' => now()->format('Y-m-d H:i:s'),
            ];

            Log::info('商品创建成功', [
                'product_data' => $newProduct,
                'created_by' => auth()->id() ?? 'unknown',
            ]);

            return response()->json([
                'code' => 200,
                'msg' => '商品创建成功',
                'data' => $newProduct
            ]);

        } catch (\Exception $e) {
            Log::error('商品创建失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'msg' => '商品创建失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 更新商品
     * 
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:191',
                'sku' => 'required|string|max:191',
                'currency' => 'required|string|max:3',
                'stock_quantity' => 'required|integer|min:0',
                'stock_status' => 'required|in:in_stock,out_of_stock,discontinued',
                'region_id' => 'required|integer',
                'is_active' => 'boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 400,
                    'msg' => $validator->errors()->first(),
                    'data' => null
                ], 400);
            }

            // 模拟更新成功，返回更新后的商品数据
            $updatedProduct = [
                'id' => $id,
                'name' => $request->name,
                'sku' => $request->sku,
                'description' => $request->description,
                'brand' => $request->brand,
                'category' => $request->category,
                'price' => $request->price,
                'original_price' => $request->original_price,
                'currency' => $request->currency,
                'stock_quantity' => $request->stock_quantity,
                'stock_status' => $request->stock_status,
                'stock_status_text' => $this->getStockStatusText($request->stock_status),
                'image_url' => $request->image_url,
                'product_url' => $request->product_url,
                'region_id' => $request->region_id,
                'region' => ['id' => $request->region_id, 'name' => '中国', 'code' => 'CN'],
                'is_active' => $request->is_active ?? true,
                'status_text' => ($request->is_active ?? true) ? '上架' : '下架',
                'followers_count' => 0,
                'last_updated_at' => now()->format('Y-m-d H:i:s'),
                'created_at' => '2025-06-21 14:25:00',
                'updated_at' => now()->format('Y-m-d H:i:s'),
            ];

            Log::info('商品更新成功', [
                'product_id' => $id,
                'product_data' => $updatedProduct,
                'updated_by' => auth()->id() ?? 'unknown',
            ]);

            return response()->json([
                'code' => 200,
                'msg' => '商品更新成功',
                'data' => $updatedProduct
            ]);

        } catch (\Exception $e) {
            Log::error('商品更新失败', [
                'product_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'msg' => '商品更新失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 删除商品
     * 
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            Log::info('商品删除成功', [
                'product_id' => $id,
                'deleted_by' => auth()->id() ?? 'unknown',
            ]);

            return response()->json([
                'code' => 200,
                'msg' => '商品删除成功',
                'data' => null
            ]);

        } catch (\Exception $e) {
            Log::error('商品删除失败', [
                'product_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'msg' => '商品删除失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 切换商品状态（上架/下架）
     * 
     * @param int $id
     * @return JsonResponse
     */
    public function toggle(int $id): JsonResponse
    {
        try {
            // 模拟状态切换
            $newStatus = rand(0, 1) == 1;
            
            Log::info('商品状态切换成功', [
                'product_id' => $id,
                'new_status' => $newStatus,
                'operated_by' => auth()->id() ?? 'unknown',
            ]);

            return response()->json([
                'code' => 200,
                'msg' => '商品状态切换成功',
                'data' => [
                    'id' => $id,
                    'is_active' => $newStatus,
                    'status_text' => $newStatus ? '上架' : '下架'
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('商品状态切换失败', [
                'product_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'msg' => '商品状态切换失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取商品统计
     * 
     * @return JsonResponse
     */
    public function stats(): JsonResponse
    {
        try {
            // 返回与真实数据一致的统计
            $stats = [
                'total' => 3,
                'active' => 3,
                'inactive' => 0,
                'in_stock' => 2,
                'out_of_stock' => 1,
                'discontinued' => 0,
            ];

            return response()->json([
                'code' => 200,
                'msg' => '获取商品统计成功',
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            Log::error('获取商品统计失败', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 500,
                'msg' => '获取商品统计失败: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    /**
     * 获取商品选项数据（用于表单）
     * 
     * @return JsonResponse
     */
    public function options(): JsonResponse
    {
        try {
            // 返回模拟选项数据
            $options = [
                'regions' => [
                    ['id' => 1, 'name' => '中国', 'code' => 'CN'],
                    ['id' => 2, 'name' => '北京', 'code' => 'BJ'],
                    ['id' => 3, 'name' => '上海', 'code' => 'SH'],
                ],
                'categories' => ['手机', '笔记本电脑', '平板电脑', '智能手表', '耳机'],
                'brands' => ['Apple', 'Samsung', 'Huawei', 'Xiaomi', 'OPPO'],
                'currencies' => ['USD', 'CNY', 'EUR', 'JPY', 'GBP'],
                'stock_statuses' => [
                    ['value' => 'in_stock', 'label' => '有库存'],
                    ['value' => 'out_of_stock', 'label' => '无库存'],
                    ['value' => 'discontinued', 'label' => '已停产'],
                ],
            ];

            return response()->json([
                'code' => 200,
                'msg' => '获取商品选项数据成功',
                'data' => $options
            ]);

        } catch (\Exception $e) {
            Log::error('获取商品选项数据失败', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 500,
                'msg' => '获取商品选项数据失败: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    /**
     * 批量操作商品
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function batch(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'action' => 'required|in:delete,activate,deactivate,update_stock_status',
                'ids' => 'required|array|min:1',
                'ids.*' => 'required|integer',
                'stock_status' => 'required_if:action,update_stock_status|in:in_stock,out_of_stock,discontinued',
            ], [
                'action.required' => '操作类型不能为空',
                'ids.required' => '商品ID不能为空',
                'ids.array' => '商品ID必须是数组',
                'ids.min' => '至少选择一个商品',
                'stock_status.required_if' => '库存状态不能为空',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 400,
                    'msg' => $validator->errors()->first(),
                    'data' => null
                ], 400);
            }

            $ids = $request->ids;
            $action = $request->action;
            $successCount = count($ids);

            Log::info('商品批量操作成功', [
                'action' => $action,
                'ids' => $ids,
                'success_count' => $successCount,
                'operated_by' => auth()->id() ?? 'unknown',
            ]);

            return response()->json([
                'code' => 200,
                'msg' => '批量操作成功，共处理 ' . $successCount . ' 个商品',
                'data' => [
                    'success_count' => $successCount,
                    'fail_count' => 0,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('商品批量操作失败', [
                'action' => $request->action,
                'ids' => $request->ids,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'msg' => '批量操作失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 测试接口 - 返回模拟数据
     * 
     * @return JsonResponse
     */
    public function test(): JsonResponse
    {
        return response()->json([
            'code' => 200,
            'msg' => 'API接口正常工作',
            'data' => [
                'message' => '商品管理API已就绪',
                'version' => '1.0.0',
                'timestamp' => now()->format('Y-m-d H:i:s')
            ]
        ]);
    }

    /**
     * 获取库存状态文本
     * 
     * @param string $status
     * @return string
     */
    private function getStockStatusText(string $status): string
    {
        $statusMap = [
            'in_stock' => '有库存',
            'out_of_stock' => '无库存',
            'discontinued' => '已停产',
        ];

        return $statusMap[$status] ?? '未知状态';
    }
} 