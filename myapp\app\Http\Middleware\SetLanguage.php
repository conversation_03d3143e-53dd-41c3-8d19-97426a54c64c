<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;

class SetLanguage
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            // 支持的语言列表
            $supportedLocales = ['zh_CN', 'en'];
            
            // 获取语言设置的优先级：
            // 1. Session中的语言设置（用户主动选择）
            // 2. 请求中的语言参数
            // 3. 默认使用中文（不再依赖浏览器语言偏好）
            
            $locale = null;
            
            // 1. 检查Session中的语言设置
            if (Session::has('locale')) {
                $sessionLocale = Session::get('locale');
                if (in_array($sessionLocale, $supportedLocales)) {
                    $locale = $sessionLocale;
                }
            }
            
            // 2. 检查请求参数中的语言设置
            if (!$locale && $request->has('lang')) {
                $requestLocale = $request->input('lang');
                if (in_array($requestLocale, $supportedLocales)) {
                    $locale = $requestLocale;
                    // 将请求中的语言设置保存到Session
                    Session::put('locale', $locale);
                }
            }
            
            // 3. 使用默认中文语言（不再检查浏览器偏好）
            if (!$locale) {
                $locale = 'zh_CN'; // 强制默认使用中文
            }
            
            // 确保语言代码在支持列表中
            if (!in_array($locale, $supportedLocales)) {
                $locale = 'zh_CN';
            }
            
            // 设置应用程序的语言环境
            App::setLocale($locale);
            
            // 将当前语言信息添加到视图变量中
            view()->share('currentLocale', $locale);
            view()->share('supportedLocales', $supportedLocales);
            
            // 记录语言设置日志（仅在调试模式下）
            if (config('app.debug')) {
                Log::info('当前语言环境设置为: ' . $locale);
            }
            
        } catch (\Exception $e) {
            // 如果出现错误，使用默认语言
            App::setLocale('zh_CN');
            
            // 记录错误日志
            Log::error('语言设置中间件错误: ' . $e->getMessage());
        }
        
        return $next($request);
    }
    
    /**
     * 从浏览器请求头中获取首选语言
     * 
     * @param Request $request
     * @return string|null
     */
    private function getBrowserLocale(Request $request)
    {
        $acceptLanguage = $request->header('Accept-Language');
        
        if (!$acceptLanguage) {
            return null;
        }
        
        // 解析Accept-Language头
        $languages = [];
        if (preg_match_all('/([a-z]{1,8}(-[a-z]{1,8})?)\s*(;\s*q\s*=\s*(1|0\.[0-9]+))?/i', $acceptLanguage, $matches)) {
            $languages = array_combine($matches[1], $matches[4]);
            
            // 如果没有质量值，默认为1.0
            foreach ($languages as $lang => $quality) {
                if ($quality === '') {
                    $languages[$lang] = 1.0;
                } else {
                    $languages[$lang] = floatval($quality);
                }
            }
            
            // 按质量值排序
            arsort($languages);
        }
        
        // 映射浏览器语言代码到应用程序语言代码
        $languageMap = [
            'zh-cn' => 'zh_CN',
            'zh-hans' => 'zh_CN',
            'zh' => 'zh_CN',
            'en-us' => 'en',
            'en-gb' => 'en',
            'en' => 'en',
        ];
        
        // 查找匹配的语言
        foreach ($languages as $lang => $quality) {
            $normalizedLang = strtolower($lang);
            
            if (isset($languageMap[$normalizedLang])) {
                return $languageMap[$normalizedLang];
            }
            
            // 尝试匹配主语言代码
            $primaryLang = explode('-', $normalizedLang)[0];
            if (isset($languageMap[$primaryLang])) {
                return $languageMap[$primaryLang];
            }
        }
        
        return null;
    }
} 