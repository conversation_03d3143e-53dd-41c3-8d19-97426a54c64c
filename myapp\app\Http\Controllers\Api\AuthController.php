<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use Laravel\Sanctum\PersonalAccessToken;

class AuthController extends Controller
{
    /**
     * 用户登录
     */
    public function login(Request $request)
    {
        // 兼容前端的userName字段，同时支持username
        $loginField = $request->input('userName') ?: $request->input('username');
        $password = $request->input('password');

        // 验证输入数据
        $validator = Validator::make([
            'loginField' => $loginField,
            'password' => $password,
        ], [
            'loginField' => 'required|string',
            'password' => 'required|string',
        ], [
            'loginField.required' => '用户名不能为空',
            'password.required' => '密码不能为空',
        ]);

        // 如果验证失败
        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'msg' => $validator->errors()->first(),
                'data' => null
            ], 400);
        }

        try {
            // 记录登录尝试
            Log::info('登录尝试', [
                'loginField' => $loginField,
                'request_data' => $request->all()
            ]);

            // 模拟用户验证 - 支持多个测试账号
            $validCredentials = [
                '<EMAIL>' => ['password' => '123456', 'role' => 'R_SUPER', 'name' => 'Super Admin'],
                '<EMAIL>' => ['password' => '123456', 'role' => 'R_ADMIN', 'name' => 'Admin'],
                '<EMAIL>' => ['password' => '123456', 'role' => 'R_EDITOR', 'name' => 'Editor'],
                '<EMAIL>' => ['password' => '123456', 'role' => 'R_USER', 'name' => 'User'],
                'admin' => ['password' => '123456', 'role' => 'R_SUPER', 'name' => 'Super Admin'],
            ];

            // 检查登录凭据
            if (!isset($validCredentials[$loginField]) || $validCredentials[$loginField]['password'] !== $password) {
                return response()->json([
                    'code' => 401,
                    'msg' => '用户名或密码错误',
                    'data' => null
                ], 401);
            }

            $userInfo = $validCredentials[$loginField];

            // 生成模拟token
            $token = 'mock_token_' . time() . '_' . rand(1000, 9999);
            $refreshToken = 'mock_refresh_token_' . time() . '_' . rand(1000, 9999);

            // 记录登录日志
            Log::info('用户登录成功', [
                'loginField' => $loginField,
                'role' => $userInfo['role'],
                'name' => $userInfo['name'],
                'login_time' => now(),
                'ip' => $request->ip()
            ]);

            return response()->json([
                'code' => 200,
                'msg' => '登录成功',
                'data' => [
                    'token' => $token,
                    'refreshToken' => $refreshToken,
                    'user' => [
                        'id' => 1,
                        'name' => $userInfo['name'],
                        'email' => $loginField,
                        'status' => 1,
                        'roles' => [$userInfo['role']],
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('登录过程发生错误', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => 500,
                'msg' => '服务器内部错误',
                'data' => null
            ], 500);
        }
    }

    /**
     * 用户登出
     */
    public function logout(Request $request)
    {
        try {
            // 删除当前用户的所有令牌
            $request->user()->tokens()->delete();

            return response()->json([
                'code' => 200,
                'msg' => '登出成功',
                'data' => null
            ]);

        } catch (\Exception $e) {
            Log::error('登出过程发生错误', [
                'error' => $e->getMessage(),
                'user_id' => $request->user()?->id
            ]);

            return response()->json([
                'code' => 500,
                'msg' => '登出失败',
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取用户信息
     */
    public function getUserInfo(Request $request)
    {
        try {
            // 返回模拟用户信息
            $mockUser = [
                'id' => 1,
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'status' => 1,
                'roles' => ['R_SUPER'],
                'permissions' => [
                    'product:view',
                    'product:create',
                    'product:edit',
                    'product:delete',
                    'banner:view',
                    'banner:create',
                    'banner:edit',
                    'banner:delete',
                    'user:view',
                    'user:create',
                    'user:edit',
                    'user:delete',
                ],
                'avatar' => null,
                'last_login_at' => now()->format('Y-m-d H:i:s'),
            ];

            return response()->json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $mockUser
            ]);

        } catch (\Exception $e) {
            Log::error('获取用户信息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'msg' => '获取用户信息失败',
                'data' => null
            ], 500);
        }
    }

    /**
     * 刷新访问令牌
     */
    public function refresh(Request $request)
    {
        try {
            $user = $request->user();
            
            // 删除当前令牌
            $request->user()->currentAccessToken()->delete();
            
            // 创建新的访问令牌
            $token = $user->createToken('auth-token')->plainTextToken;

            return response()->json([
                'code' => 200,
                'msg' => '刷新成功',
                'data' => [
                    'token' => $token,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('刷新令牌失败', [
                'error' => $e->getMessage(),
                'user_id' => $request->user()?->id
            ]);

            return response()->json([
                'code' => 401,
                'msg' => '刷新令牌失败',
                'data' => null
            ], 401);
        }
    }
} 