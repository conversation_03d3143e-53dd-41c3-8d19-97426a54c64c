<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>地区管理</span>
          <el-button type="primary" @click="handleAdd" :icon="Plus">
            添加地区
          </el-button>
        </div>
      </template>

      <el-table :data="regions" v-loading="loading" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="地区名称" />
        <el-table-column prop="code" label="地区代码" width="120" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">
              {{ getTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="parent_id" label="父级地区" width="120">
          <template #default="{ row }">
            <span v-if="row.parent_id">{{ getParentName(row.parent_id) }}</span>
            <span v-else class="text-gray-400">无</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.is_active"
              @change="handleToggleStatus(row)"
              :loading="row.statusLoading"
              active-text="启用"
              inactive-text="禁用"
            />
          </template>
        </el-table-column>
        <el-table-column prop="sort_order" label="排序" width="80" />
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)" :icon="Edit">
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
              :icon="Delete"
              :loading="row.deleteLoading"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑地区弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogMode === 'add' ? '添加地区' : '编辑地区'"
      width="600px"
      :before-close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="地区名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入地区名称" />
        </el-form-item>
        
        <el-form-item label="地区代码" prop="code">
          <el-input v-model="formData.code" placeholder="请输入地区代码（如：CN, US）" />
        </el-form-item>
        
        <el-form-item label="地区类型" prop="type">
          <el-select v-model="formData.type" style="width: 100%">
            <el-option label="国家" value="country" />
            <el-option label="省/州" value="state" />
            <el-option label="城市" value="city" />
            <el-option label="区县" value="district" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="父级地区" prop="parent_id">
          <el-select
            v-model="formData.parent_id"
            placeholder="选择父级地区（可选）"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="region in availableParents"
              :key="region.id"
              :label="region.name"
              :value="region.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="排序" prop="sort_order">
          <el-input-number v-model="formData.sort_order" :min="0" style="width: 100%" />
        </el-form-item>
        
        <el-form-item label="状态" prop="is_active">
          <el-switch
            v-model="formData.is_active"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入地区描述（可选）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ dialogMode === 'add' ? '添加' : '更新' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogMode = ref<'add' | 'edit'>('add')
const submitLoading = ref(false)
const formRef = ref()

// 地区列表数据
const regions = ref([])

// 地区表单
const formData = reactive({
  id: null,
  name: '',
  code: '',
  type: 'country',
  parent_id: null,
  is_active: true,
  sort_order: 0,
  description: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入地区名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入地区代码', trigger: 'blur' },
    { pattern: /^[A-Z0-9_]+$/, message: '地区代码只能包含大写字母、数字和下划线', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择地区类型', trigger: 'change' }
  ]
}

// 计算属性
const availableParents = computed(() => {
  // 排除当前编辑的地区，避免循环引用
  return regions.value.filter(region => region.id !== formData.id)
})

// 生命周期
onMounted(() => {
  loadRegions()
})

// 方法
const loadRegions = async () => {
  loading.value = true
  try {
    // 这里应该调用实际的API
    console.log('加载地区列表')
    // const response = await regionApi.getList()
    // regions.value = response.data
    
    // 模拟数据
    regions.value = [
      {
        id: 1,
        name: '中国',
        code: 'CN',
        type: 'country',
        parent_id: null,
        is_active: true,
        sort_order: 1,
        description: '中华人民共和国'
      },
      {
        id: 2,
        name: '北京',
        code: 'BJ',
        type: 'state',
        parent_id: 1,
        is_active: true,
        sort_order: 1,
        description: '北京市'
      },
      {
        id: 3,
        name: '上海',
        code: 'SH',
        type: 'state',
        parent_id: 1,
        is_active: true,
        sort_order: 2,
        description: '上海市'
      }
    ]
  } catch (error) {
    ElMessage.error('加载地区列表失败')
  } finally {
    loading.value = false
  }
}

const handleAdd = () => {
  dialogMode.value = 'add'
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row: any) => {
  dialogMode.value = 'edit'
  Object.assign(formData, { ...row })
  dialogVisible.value = true
}

const handleDelete = async (row: any) => {
  try {
    // 检查是否有子地区
    const hasChildren = regions.value.some(region => region.parent_id === row.id)
    if (hasChildren) {
      ElMessage.warning('该地区下有子地区，无法删除')
      return
    }

    await ElMessageBox.confirm(
      `确定要删除地区"${row.name}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    row.deleteLoading = true
    // 这里应该调用实际的API
    console.log('删除地区', row.id)
    // await regionApi.delete(row.id)
    
    ElMessage.success('删除成功')
    loadRegions()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  } finally {
    row.deleteLoading = false
  }
}

const handleToggleStatus = async (row: any) => {
  row.statusLoading = true
  try {
    // 这里应该调用实际的API
    console.log('切换地区状态', row.id, row.is_active)
    // await regionApi.toggle(row.id)
    
    ElMessage.success(`地区${row.is_active ? '启用' : '禁用'}成功`)
  } catch (error) {
    // 恢复状态
    row.is_active = !row.is_active
    ElMessage.error('操作失败')
  } finally {
    row.statusLoading = false
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
  } catch (error) {
    return
  }

  submitLoading.value = true
  try {
    if (dialogMode.value === 'add') {
      // 这里应该调用实际的API
      console.log('创建地区', formData)
      // await regionApi.create(formData)
      ElMessage.success('添加成功')
    } else {
      // 这里应该调用实际的API
      console.log('更新地区', formData)
      // await regionApi.update(formData.id, formData)
      ElMessage.success('更新成功')
    }
    
    dialogVisible.value = false
    loadRegions()
  } catch (error) {
    ElMessage.error(dialogMode.value === 'add' ? '添加失败' : '更新失败')
  } finally {
    submitLoading.value = false
  }
}

const handleDialogClose = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    id: null,
    name: '',
    code: '',
    type: 'country',
    parent_id: null,
    is_active: true,
    sort_order: 0,
    description: ''
  })
  formRef.value?.clearValidate()
}

const getTypeText = (type: string) => {
  const typeMap = {
    'country': '国家',
    'state': '省/州',
    'city': '城市',
    'district': '区县'
  }
  return typeMap[type] || type
}

const getTypeTagType = (type: string) => {
  const typeMap = {
    'country': 'success',
    'state': 'primary',
    'city': 'warning',
    'district': 'info'
  }
  return typeMap[type] || 'info'
}

const getParentName = (parentId: number) => {
  const parent = regions.value.find(region => region.id === parentId)
  return parent ? parent.name : ''
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.dialog-footer {
  text-align: right;
}

.text-gray-400 {
  color: #c0c4cc;
}
</style> 