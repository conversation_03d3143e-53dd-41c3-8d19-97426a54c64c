@echo off
echo ===========================================
echo 用户角色分离功能测试脚本
echo ===========================================
echo.

REM 设置PHP路径
set PHP_PATH=D:\phpstudy_pro\Extensions\php\php8.1.9nts
set PATH=%PHP_PATH%;%PATH%

REM 进入Laravel项目目录
cd /d myapp
echo 已进入Laravel项目目录: %CD%
echo.

echo 正在测试用户角色分离功能...
echo.

REM 测试1：检查路由
echo 1. 检查路由配置:
php artisan route:list --columns=method,uri,name,middleware
echo.

REM 测试2：检查数据库连接
echo 2. 检查数据库连接:
php artisan db:show
echo.

REM 测试3：清除缓存
echo 3. 清除缓存:
php artisan cache:clear
php artisan config:clear
php artisan route:clear
echo.

REM 测试4：检查用户表结构
echo 4. 检查用户表结构:
php artisan tinker --execute="Schema::getColumnListing('users')"
echo.

echo ===========================================
echo 测试完成！
echo ===========================================
echo.
echo 请运行以下命令来测试用户角色功能:
echo 1. php artisan tinker
echo 2. 在tinker中运行: User::create(['name'=>'Test','email'=>'<EMAIL>','password'=>bcrypt('123456'),'role'=>'R_USER'])
echo 3. 访问 http://localhost:8000/admin 测试后台管理
echo.

pause 