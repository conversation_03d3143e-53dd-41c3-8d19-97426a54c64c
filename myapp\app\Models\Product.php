<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'sku',
        'description',
        'brand',
        'category',
        'price',
        'original_price',
        'currency',
        'stock_quantity',
        'stock_status',
        'image_url',
        'product_url',
        'region_id',
        'attributes',
        'is_active',
        'last_updated_at',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'original_price' => 'decimal:2',
        'attributes' => 'array',
        'is_active' => 'boolean',
        'last_updated_at' => 'datetime',
    ];

    /**
     * 地区关联
     */
    public function region(): BelongsTo
    {
        return $this->belongsTo(Region::class);
    }

    /**
     * 关注用户关联
     */
    public function followers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_product_follows')
                    ->withPivot(['email_notifications', 'wechat_notifications', 'notification_settings'])
                    ->withTimestamps();
    }

    /**
     * 用户关注记录关联
     */
    public function userFollows(): HasMany
    {
        return $this->hasMany(UserProductFollow::class);
    }

    /**
     * 通知关联
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class);
    }

    /**
     * 获取激活的商品
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 按库存状态筛选
     */
    public function scopeByStockStatus($query, $status)
    {
        return $query->where('stock_status', $status);
    }

    /**
     * 有库存的商品
     */
    public function scopeInStock($query)
    {
        return $query->where('stock_status', 'in_stock');
    }

    /**
     * 按地区筛选
     */
    public function scopeByRegion($query, $regionId)
    {
        return $query->where('region_id', $regionId);
    }

    /**
     * 获取格式化价格
     */
    public function getFormattedPriceAttribute(): string
    {
        if ($this->price) {
            return $this->currency . ' ' . number_format($this->price, 2);
        }
        return 'N/A';
    }

    /**
     * 获取格式化原价
     */
    public function getFormattedOriginalPriceAttribute(): string
    {
        if ($this->original_price) {
            return $this->currency . ' ' . number_format($this->original_price, 2);
        }
        return 'N/A';
    }

    /**
     * 检查是否有折扣
     */
    public function getHasDiscountAttribute(): bool
    {
        return $this->price && $this->original_price && $this->price < $this->original_price;
    }

    /**
     * 获取折扣百分比
     */
    public function getDiscountPercentageAttribute(): float
    {
        if ($this->has_discount) {
            return round((($this->original_price - $this->price) / $this->original_price) * 100, 2);
        }
        return 0;
    }

    /**
     * 获取库存状态文本
     */
    public function getStockStatusTextAttribute(): string
    {
        return match($this->stock_status) {
            'in_stock' => 'In Stock',
            'out_of_stock' => 'Out of Stock',
            'discontinued' => 'Discontinued',
            default => ucfirst(str_replace('_', ' ', $this->stock_status)),
        };
    }
}
