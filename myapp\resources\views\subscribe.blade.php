<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ __('app.subscription.title', ['name' => __('app.nav.product_alert_system')]) }}</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body { 
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif; 
            background: linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e1b4b 100%);
            min-height: 100vh;
        }
        .gradient-bg { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
        }
        .summer-gradient {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffd93d 25%, #6bcf7f 50%, #4dabf7 75%, #ff6b6b 100%);
            background-size: 200% 200%;
            animation: gradientShift 3s ease infinite;
        }
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        .plan-card { 
            transition: all 0.3s ease; 
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .plan-card:hover { 
            transform: translateY(-5px); 
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
            border-color: rgba(59, 130, 246, 0.5);
        }
        .plan-selected { 
            border-color: #3B82F6; 
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
            box-shadow: 0 20px 40px -12px rgba(59, 130, 246, 0.3);
        }
        .step-indicator { 
            transition: all 0.3s ease; 
        }
        .countdown-timer {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        .payment-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        .payment-card:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(59, 130, 246, 0.5);
        }
        .original-price {
            position: relative;
        }
        .original-price::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: #ef4444;
            transform: rotate(-5deg);
        }
        .security-badge {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.2);
        }
    </style>
</head>
<body class="text-white">
    <!-- 导航栏 -->
    <nav class="bg-black/20 backdrop-blur-md border-b border-white/10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="/" class="text-xl font-bold text-white">{{ __('app.nav.product_alert_system') }}</a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="/" class="text-gray-300 hover:text-white transition-colors">{{ __('app.nav.back_to_home') }}</a>
                    @auth
                        <a href="/dashboard" class="text-gray-300 hover:text-white transition-colors">{{ __('app.nav.dashboard') }}</a>
                    @else
                        <a href="/login" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">{{ __('app.nav.login') }}</a>
                    @endauth
                    
                    <!-- 语言切换按钮 -->
                    @include('components.language-switcher')
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 错误消息 -->
        @if(session('error'))
            <div class="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-6 backdrop-blur-sm">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-red-200">{{ session('error') }}</p>
                    </div>
                </div>
            </div>
        @endif

        @if($errors->any())
            <div class="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-6 backdrop-blur-sm">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <ul class="text-sm text-red-200">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        @endif

        <!-- 促销标题 -->
        <div class="text-center mb-8">
            <div class="summer-gradient text-transparent bg-clip-text mb-4">
                <h1 class="text-4xl md:text-5xl font-black tracking-tight">
                    Summer Sale <span class="text-white">for New Users</span>
                </h1>
            </div>
            <p class="text-xl text-gray-300 mb-6">折扣即将结束。<span class="text-red-400 font-semibold">不要错过!</span></p>
            
            <!-- 倒计时 -->
            <div class="flex justify-center items-center space-x-4 mb-8">
                <div class="countdown-timer text-white px-4 py-2 rounded-lg font-bold text-lg">
                    <span id="hours">08</span>
                    <span class="text-sm block">Hrs</span>
                </div>
                <div class="countdown-timer text-white px-4 py-2 rounded-lg font-bold text-lg">
                    <span id="minutes">53</span>
                    <span class="text-sm block">Min</span>
                </div>
                <div class="countdown-timer text-white px-4 py-2 rounded-lg font-bold text-lg">
                    <span id="seconds">54</span>
                    <span class="text-sm block">Sec</span>
                </div>
            </div>
        </div>

        <!-- 订阅表单 -->
        <form action="{{ route('subscription.process') }}" method="POST" id="subscriptionForm">
            @csrf
            
            <!-- 地区选择（简化版） -->
            <div class="mb-6">
                <select name="region_id" id="region_select" class="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent backdrop-blur-sm" required>
                    <option value="" class="text-gray-900">请选择您的地区</option>
                    @foreach($regions as $region)
                        <option value="{{ $region->id }}" {{ old('region_id') == $region->id ? 'selected' : '' }} class="text-gray-900">
                            {{ $region->name }}
                        </option>
                        @if($region->children->count() > 0)
                            @foreach($region->children as $child)
                                <option value="{{ $child->id }}" {{ old('region_id') == $child->id ? 'selected' : '' }} class="text-gray-900">
                                    　└ {{ $child->name }}
                                </option>
                            @endforeach
                        @endif
                    @endforeach
                </select>
                @error('region_id')
                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                @enderror
            </div>

            <!-- 套餐选择 -->
            <div class="space-y-4 mb-8">
                @foreach($plans as $index => $plan)
                    @php
                        $isRecommended = $plan->name === 'Professional' || $index === 0;
                        $discountPercent = $index === 0 ? 70 : ($index === 1 ? 25 : 0);
                        $billingText = $index === 0 ? '12 months' : ($index === 1 ? '3 months' : '1 month');
                        $originalPrice = $plan->price * ($index === 0 ? 12 : ($index === 1 ? 3 : 1));
                        $finalPrice = $discountPercent > 0 ? $originalPrice * (1 - $discountPercent / 100) : $originalPrice;
                        $monthlyPrice = $finalPrice / ($index === 0 ? 12 : ($index === 1 ? 3 : 1));
                    @endphp
                    
                    <div class="plan-card bg-white/5 rounded-2xl p-6 cursor-pointer relative" 
                         data-plan-id="{{ $plan->id }}" 
                         data-plan-name="{{ $plan->name }}" 
                         data-plan-price="{{ $plan->price }}"
                         data-billing-months="{{ $index === 0 ? 12 : ($index === 1 ? 3 : 1) }}">
                        
                        @if($isRecommended)
                            <div class="absolute -top-3 left-6">
                                <span class="bg-gradient-to-r from-pink-500 to-red-500 text-white px-4 py-1 rounded-full text-sm font-bold">
                                    BEST CHOICE
                                </span>
                            </div>
                        @endif
                        
                        <div class="flex justify-between items-center">
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-white mb-1">{{ $billingText }}</h3>
                                @if($discountPercent > 0)
                                    <span class="bg-gradient-to-r from-red-500 to-pink-500 text-white px-2 py-1 rounded text-sm font-bold">
                                        {{ $discountPercent }}% OFF
                                    </span>
                                @endif
                            </div>
                            
                            <div class="text-right">
                                @if($discountPercent > 0)
                                    <div class="text-gray-400 text-lg original-price">
                                        ${{ number_format($originalPrice, 2) }}
                                    </div>
                                @endif
                                <div class="text-white font-bold text-2xl">
                                    ${{ number_format($monthlyPrice, 2) }}
                                    <span class="text-base text-gray-400 font-normal">/month</span>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
            
            <input type="hidden" name="plan_id" id="selected_plan_id" value="{{ old('plan_id') }}">
            <input type="hidden" name="billing_cycle" id="selected_billing_cycle" value="monthly">
            @error('plan_id')
                <p class="text-sm text-red-400 mb-4">{{ $message }}</p>
            @enderror

            <!-- 安全保障信息 -->
            <div class="space-y-3 mb-8">
                <div class="flex items-center text-green-400">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span class="text-sm">No adult transaction in your bank statement</span>
                </div>
                <div class="flex items-center text-green-400">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span class="text-sm">No hidden fees • Cancel subscription at any time</span>
                </div>
            </div>

            <!-- 支付方式 -->
            <div class="space-y-4 mb-8">
                <div class="payment-card rounded-2xl p-4">
                    <label class="flex items-center justify-between cursor-pointer">
                        <div class="flex items-center">
                            <input type="radio" name="payment_method" value="card" class="mr-3" required>
                            <span class="text-white font-semibold">Pay with Credit / Debit Card</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAzMiAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjIwIiByeD0iNCIgZmlsbD0iIzAwNTFBNSIvPgo8cGF0aCBkPSJNMTIuNSAxMi41SDguNVY3LjVIMTIuNVYxMi41WiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTIzLjUgMTIuNUgxOS41VjcuNUgyMy41VjEyLjVaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="VISA" class="w-8 h-5">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAzMiAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjIwIiByeD0iNCIgZmlsbD0iI0VCMDAxQiIvPgo8Y2lyY2xlIGN4PSIxMiIgY3k9IjEwIiByPSI0IiBmaWxsPSIjRkY1RjAwIi8+CjxjaXJjbGUgY3g9IjIwIiBjeT0iMTAiIHI9IjQiIGZpbGw9IiNGRkY1RjAiLz4KPC9zdmc+Cg==" alt="Mastercard" class="w-8 h-5">
                        </div>
                    </label>
                </div>
            </div>
            
            @error('payment_method')
                <p class="text-sm text-red-400 mb-4">{{ $message }}</p>
            @enderror

            <!-- 提交按钮 -->
            <button type="submit" id="submitOrder" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-bold py-4 px-8 rounded-2xl text-lg transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none" disabled>
                立即订阅
            </button>
        </form>
    </main>

    <!-- 获取PHP数据 -->
    <script type="text/javascript">
        window.subscribeData = {
            oldPlanId: @json(old('plan_id')),
            oldBillingCycle: @json(old('billing_cycle')),
            isAuthenticated: @json(auth()->check())
        };
    </script>

    <!-- JavaScript -->
    <script>
        let selectedRegionId = '';
        let selectedPlanId = '';
        let selectedBillingCycle = 'monthly';
        
        // 获取PHP数据
        const oldPlanId = window.subscribeData.oldPlanId;
        const oldBillingCycle = window.subscribeData.oldBillingCycle;
        const isAuthenticated = window.subscribeData.isAuthenticated;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            startCountdown();
            
            // 默认选中信用卡支付方式
            const cardPayment = document.querySelector('input[name="payment_method"][value="card"]');
            if (cardPayment) {
                cardPayment.checked = true;
            }
            
            // 如果有旧输入，恢复状态
            const oldRegion = document.getElementById('region_select').value;
            
            if (oldRegion) {
                selectedRegionId = oldRegion;
                updateSubmitButton();
            }
            
            if (oldPlanId) {
                const planCard = document.querySelector(`[data-plan-id="${oldPlanId}"]`);
                if (planCard) {
                    selectPlan(planCard);
                }
            }
            
            // 初始化按钮状态
            updateSubmitButton();
        });

        function setupEventListeners() {
            // 地区选择
            document.getElementById('region_select').addEventListener('change', function() {
                selectedRegionId = this.value;
                updateSubmitButton();
            });

            // 套餐选择
            document.querySelectorAll('.plan-card').forEach(card => {
                card.addEventListener('click', () => selectPlan(card));
            });

            // 支付方式
            document.querySelectorAll('input[name="payment_method"]').forEach(radio => {
                radio.addEventListener('change', updateSubmitButton);
            });

            // 表单提交前的处理
            document.getElementById('subscriptionForm').addEventListener('submit', function(e) {
                if (!isAuthenticated) {
                    e.preventDefault();
                    alert('请先登录后再订阅服务');
                    window.location.href = '/login';
                    return;
                }
                
                const submitBtn = document.getElementById('submitOrder');
                submitBtn.disabled = true;
                submitBtn.textContent = '正在处理...';
            });
        }

        function selectPlan(card) {
            // 移除其他选中状态
            document.querySelectorAll('.plan-card').forEach(c => {
                c.classList.remove('plan-selected');
            });
            
            // 添加选中状态
            card.classList.add('plan-selected');
            
            selectedPlanId = card.dataset.planId;
            const billingMonths = parseInt(card.dataset.billingMonths);
            
            // 根据选择的套餐设置计费周期
            if (billingMonths === 12) {
                selectedBillingCycle = 'yearly';
            } else if (billingMonths === 3) {
                selectedBillingCycle = 'quarterly';
            } else {
                selectedBillingCycle = 'monthly';
            }
            
            // 更新隐藏字段
            document.getElementById('selected_plan_id').value = selectedPlanId;
            document.getElementById('selected_billing_cycle').value = selectedBillingCycle;
            
            updateSubmitButton();
        }

        function updateSubmitButton() {
            const regionSelected = selectedRegionId !== '';
            const planSelected = selectedPlanId !== '';
            const paymentSelected = document.querySelector('input[name="payment_method"]:checked');
            
            const submitButton = document.getElementById('submitOrder');
            submitButton.disabled = !(regionSelected && planSelected && paymentSelected);
        }

        function startCountdown() {
            let hours = 8;
            let minutes = 53;
            let seconds = 54;
            
            const updateCountdown = () => {
                seconds--;
                
                if (seconds < 0) {
                    seconds = 59;
                    minutes--;
                    
                    if (minutes < 0) {
                        minutes = 59;
                        hours--;
                        
                        if (hours < 0) {
                            hours = 0;
                            minutes = 0;
                            seconds = 0;
                        }
                    }
                }
                
                document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
                document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
                document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
            };
            
            setInterval(updateCountdown, 1000);
        }
    </script>
</body>
</html> 