@use '@styles/variables.scss' as *;
@use '@styles/mixin.scss' as *;

// 设置抽屉模态框样式
.setting-modal {
  background: transparent !important;

  .el-drawer {
    // 背景滤镜效果
    background: rgba($color: #fff, $alpha: 50%) !important;
    box-shadow: 0 0 30px rgb(0 0 0 / 10%) !important;

    @include backdropBlur();

    .setting-box-wrap {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      width: calc(100% + 15px);
      margin-bottom: 10px;

      .setting-item {
        box-sizing: border-box;
        width: calc(33.333% - 15px);
        margin-right: 15px;
        text-align: center;

        .box {
          position: relative;
          box-sizing: border-box;
          display: flex;
          height: 52px;
          overflow: hidden;
          cursor: pointer;
          border: 2px solid var(--art-gray-100);
          border-radius: 8px;
          box-shadow: 0 2px 8px 0 rgb(0 0 0 / 20%);
          transition: box-shadow 0.1s;

          &.mt-16 {
            margin-top: 16px;
          }

          &.is-active {
            border: 2px solid var(--main-color);
          }

          img {
            width: 100%;
            height: 100%;
          }
        }

        .name {
          margin-top: 6px;
          font-size: 14px;
          text-align: center;
        }
      }
    }
  }

  // 去除滚动条
  .el-drawer__body::-webkit-scrollbar {
    width: 0 !important;
  }
}

.dark {
  .setting-modal {
    .el-drawer {
      background: rgba($color: #000, $alpha: 50%) !important;
    }
  }

  .drawer-con {
    .box-style {
      .button {
        &.is-active {
          color: #fff !important;
          background-color: rgba(var(--art-gray-400-rgb), 0.7);
        }

        &:hover:not(.is-active) {
          background-color: rgba($color: #000, $alpha: 20%);
        }
      }
    }
  }
}

// 去除火狐浏览器滚动条
:deep(.el-drawer__body) {
  scrollbar-width: none;
}

// 移动端隐藏
@media screen and (max-width: $device-ipad) {
  .mobile-hide {
    display: none !important;
  }

  .drawer-con {
    .style-item {
      width: calc(50% - 10px);
      margin-right: 10px;

      &:nth-child(2n) {
        margin-right: 0;
      }
    }

    .container-width {
      flex-direction: column;
      gap: 10px;

      .item {
        height: 50px;
      }
    }

    .basic-box {
      .item {
        padding: 6px 0;
        margin-top: 15px;

        span {
          font-size: 13px;
        }
      }
    }
  }
}

// 小屏幕适配
@media screen and (width <= 480px) {
  .drawer-con {
    padding: 0 8px 20px;

    .main-color-wrap {
      .offset {
        justify-content: center;

        > div {
          margin: 0 8px 8px 0;
        }
      }
    }
  }
}
