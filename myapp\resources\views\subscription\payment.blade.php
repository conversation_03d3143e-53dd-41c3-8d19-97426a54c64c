<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单支付 - 商品上新提醒系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .payment-method { transition: all 0.3s ease; }
        .payment-method:hover { transform: translateY(-2px); box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1); }
        .payment-method.selected { border-color: #3B82F6; background-color: #EBF8FF; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="/" class="text-xl font-bold text-gray-900">商品提醒系统</a>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-600">订单支付</span>
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900">返回中心</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 成功/错误消息 -->
        @if(session('success'))
            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                    </div>
                </div>
            </div>
        @endif

        @if(session('error'))
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-red-800">{{ session('error') }}</p>
                    </div>
                </div>
            </div>
        @endif

        <!-- 页面标题 -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900">完成支付</h1>
            <p class="mt-2 text-gray-600">请选择支付方式完成订单</p>
        </div>

        <div class="grid lg:grid-cols-3 gap-8">
            <!-- 订单详情 -->
            <div class="lg:col-span-2">
                <!-- 订单信息 -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">订单详情</h2>
                    
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-gray-600">订单号：</span>
                            <span class="font-medium text-gray-900">{{ $paymentOrder->order_number }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">套餐名称：</span>
                            <span class="font-medium text-gray-900">{{ $paymentOrder->plan->name }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">计费周期：</span>
                            <span class="font-medium text-gray-900">
                                @switch($paymentOrder->billing_cycle)
                                    @case('monthly')
                                        月付
                                        @break
                                    @case('yearly')
                                        年付
                                        @break
                                    @case('lifetime')
                                        终身
                                        @break
                                @endswitch
                            </span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">服务地区：</span>
                            <span class="font-medium text-gray-900">{{ $paymentOrder->region->name }}</span>
                        </div>
                        
                        @if($paymentOrder->discount_amount > 0)
                            <div class="flex justify-between">
                                <span class="text-gray-600">原价：</span>
                                <span class="text-gray-500 line-through">¥{{ number_format($paymentOrder->original_amount, 2) }}</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">优惠金额：</span>
                                <span class="text-green-600">-¥{{ number_format($paymentOrder->discount_amount, 2) }}</span>
                            </div>
                        @endif
                        
                        <div class="border-t pt-4">
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-semibold text-gray-900">应付金额：</span>
                                <span class="text-2xl font-bold text-blue-600">¥{{ number_format($paymentOrder->amount, 2) }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 支付方式选择 -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">选择支付方式</h2>
                    
                    <form action="{{ route('subscription.process-payment', $paymentOrder->id) }}" method="POST" id="paymentForm">
                        @csrf
                        
                        <div class="space-y-4 mb-6">
                            <!-- 支付宝 -->
                            <label class="payment-method cursor-pointer block">
                                <input type="radio" name="payment_method" value="alipay" class="sr-only" required>
                                <div class="border-2 border-gray-200 rounded-lg p-4 flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-8 h-8 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <h3 class="font-semibold text-gray-900">支付宝</h3>
                                        <p class="text-sm text-gray-600">使用支付宝安全快捷支付</p>
                                    </div>
                                    <div class="w-5 h-5 border-2 border-gray-300 rounded-full flex items-center justify-center">
                                        <div class="w-2 h-2 bg-blue-600 rounded-full hidden"></div>
                                    </div>
                                </div>
                            </label>

                            <!-- 微信支付 -->
                            <label class="payment-method cursor-pointer block">
                                <input type="radio" name="payment_method" value="wechat" class="sr-only">
                                <div class="border-2 border-gray-200 rounded-lg p-4 flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-8 h-8 text-green-600" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <h3 class="font-semibold text-gray-900">微信支付</h3>
                                        <p class="text-sm text-gray-600">使用微信支付便捷付款</p>
                                    </div>
                                    <div class="w-5 h-5 border-2 border-gray-300 rounded-full flex items-center justify-center">
                                        <div class="w-2 h-2 bg-blue-600 rounded-full hidden"></div>
                                    </div>
                                </div>
                            </label>

                            <!-- PayPal -->
                            <label class="payment-method cursor-pointer block">
                                <input type="radio" name="payment_method" value="paypal" class="sr-only">
                                <div class="border-2 border-gray-200 rounded-lg p-4 flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-8 h-8 text-yellow-600" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <h3 class="font-semibold text-gray-900">PayPal</h3>
                                        <p class="text-sm text-gray-600">使用PayPal国际支付</p>
                                    </div>
                                    <div class="w-5 h-5 border-2 border-gray-300 rounded-full flex items-center justify-center">
                                        <div class="w-2 h-2 bg-blue-600 rounded-full hidden"></div>
                                    </div>
                                </div>
                            </label>
                        </div>

                        @error('payment_method')
                            <p class="text-sm text-red-600 mb-4">{{ $message }}</p>
                        @enderror

                        <!-- 支付按钮 -->
                        <button 
                            type="submit" 
                            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-4 px-6 rounded-lg transition duration-300 flex items-center justify-center"
                            id="payButton"
                        >
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                            <span id="payButtonText">立即支付 ¥{{ number_format($paymentOrder->amount, 2) }}</span>
                        </button>
                    </form>
                </div>
            </div>

            <!-- 订单摘要 -->
            <div class="space-y-6">
                <!-- 倒计时 -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h3 class="font-semibold text-yellow-800">订单有效期</h3>
                            <p class="text-sm text-yellow-600">
                                订单将在 <span id="countdown" class="font-mono font-bold"></span> 后过期
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 安全提示 -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 class="font-semibold text-blue-800 mb-2">安全保障</h3>
                    <ul class="text-sm text-blue-600 space-y-1">
                        <li>• 256位SSL加密传输</li>
                        <li>• 支付信息安全保护</li>
                        <li>• 7天无理由退款</li>
                        <li>• 24小时客服支持</li>
                    </ul>
                </div>

                <!-- 客服联系 -->
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <h3 class="font-semibold text-gray-800 mb-2">需要帮助？</h3>
                    <div class="text-sm text-gray-600 space-y-1">
                        <p>客服热线：400-123-4567</p>
                        <p>在线客服：9:00-22:00</p>
                        <p>邮箱：<EMAIL></p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 获取PHP数据 -->
    <script type="text/javascript">
        window.paymentData = {
            expiresAt: @json($paymentOrder->expires_at->format('Y-m-d H:i:s'))
        };
    </script>

    <!-- JavaScript -->
    <script>
        // 获取过期时间数据
        const expiresAtData = window.paymentData.expiresAt;
        
        // 支付方式选择
        document.querySelectorAll('input[name="payment_method"]').forEach(radio => {
            radio.addEventListener('change', function() {
                // 移除所有选中状态
                document.querySelectorAll('.payment-method').forEach(method => {
                    method.classList.remove('selected');
                    method.querySelector('.w-2').classList.add('hidden');
                });
                
                // 添加选中状态
                const label = this.closest('.payment-method');
                label.classList.add('selected');
                label.querySelector('.w-2').classList.remove('hidden');
            });
        });

        // 默认选择第一个支付方式
        document.querySelector('input[name="payment_method"]').checked = true;
        document.querySelector('input[name="payment_method"]').dispatchEvent(new Event('change'));

        // 表单提交处理
        document.getElementById('paymentForm').addEventListener('submit', function(e) {
            const button = document.getElementById('payButton');
            const buttonText = document.getElementById('payButtonText');
            
            // 检查是否选择了支付方式
            const selectedPayment = document.querySelector('input[name="payment_method"]:checked');
            if (!selectedPayment) {
                e.preventDefault();
                alert('请选择支付方式');
                return;
            }
            
            // 显示加载状态
            button.disabled = true;
            buttonText.textContent = '正在处理支付...';
            button.classList.add('opacity-75');
        });

        // 倒计时
        function startCountdown() {
            const expiresAt = new Date(expiresAtData).getTime();
            const countdownElement = document.getElementById('countdown');
            
            function updateCountdown() {
                const now = new Date().getTime();
                const timeLeft = expiresAt - now;
                
                if (timeLeft > 0) {
                    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
                    
                    countdownElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                } else {
                    countdownElement.textContent = '已过期';
                    // 禁用支付按钮
                    document.getElementById('payButton').disabled = true;
                    document.getElementById('payButtonText').textContent = '订单已过期';
                }
            }
            
            updateCountdown();
            setInterval(updateCountdown, 1000);
        }
        
        startCountdown();

        // 自动关闭消息
        setTimeout(function() {
            const alerts = document.querySelectorAll('.bg-green-50, .bg-red-50');
            alerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 500);
            });
        }, 5000);
    </script>
</body>
</html> 
            const alerts = document.querySelectorAll('.bg-green-50, .bg-red-50');
            alerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 500);
            });
        }, 5000);
    </script>
</body>
</html> 