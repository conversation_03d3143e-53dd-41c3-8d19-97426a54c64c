<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BannerResource\Pages;
use App\Models\Banner;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;

class BannerResource extends Resource
{
    protected static ?string $model = Banner::class;

    protected static ?string $navigationIcon = 'heroicon-o-photograph';

    protected static ?string $navigationLabel = '轮播图管理';

    protected static ?string $modelLabel = '轮播图';

    protected static ?string $pluralModelLabel = '轮播图';

    protected static ?string $navigationGroup = '内容管理';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('基本信息')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label('标题')
                            ->required()
                            ->maxLength(255)
                            ->columnSpan(2),
                        
                        Forms\Components\FileUpload::make('image')
                            ->label('轮播图片')
                            ->required()
                            ->image()
                            ->disk('public')
                            ->directory('banners')
                            ->visibility('public')
                            ->maxSize(5120) // 5MB
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp'])
                            ->helperText('建议尺寸：1920x600px，支持JPG、PNG、GIF、WebP格式，最大5MB')
                            ->columnSpan(2),
                        
                        Forms\Components\TextInput::make('alt_text')
                            ->label('图片描述')
                            ->maxLength(255)
                            ->helperText('用于无障碍访问和SEO优化')
                            ->columnSpan(2),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('链接设置')
                    ->schema([
                        Forms\Components\TextInput::make('link')
                            ->label('跳转链接')
                            ->url()
                            ->maxLength(500)
                            ->placeholder('https://example.com')
                            ->helperText('点击轮播图时跳转的链接地址'),
                        
                        Forms\Components\Select::make('target')
                            ->label('打开方式')
                            ->options([
                                '_self' => '当前窗口',
                                '_blank' => '新窗口',
                            ])
                            ->default('_self')
                            ->required(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('描述信息')
                    ->schema([
                        Forms\Components\Textarea::make('description')
                            ->label('描述')
                            ->maxLength(1000)
                            ->rows(3)
                            ->placeholder('轮播图的详细描述...')
                            ->columnSpan(2),
                    ])
                    ->columns(1),

                Forms\Components\Section::make('显示设置')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->label('启用状态')
                            ->default(true)
                            ->helperText('关闭后轮播图将不会显示在前台'),
                        
                        Forms\Components\TextInput::make('sort_order')
                            ->label('排序')
                            ->numeric()
                            ->default(0)
                            ->helperText('数字越小越靠前显示'),
                        
                        Forms\Components\DateTimePicker::make('start_time')
                            ->label('开始时间')
                            ->placeholder('不限制')
                            ->helperText('留空表示立即生效'),
                        
                        Forms\Components\DateTimePicker::make('end_time')
                            ->label('结束时间')
                            ->placeholder('不限制')
                            ->helperText('留空表示永久有效'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image')
                    ->label('图片')
                    ->disk('public')
                    ->size(80)
                    ->square(),
                
                Tables\Columns\TextColumn::make('title')
                    ->label('标题')
                    ->searchable()
                    ->sortable()
                    ->limit(30),
                
                Tables\Columns\TextColumn::make('link')
                    ->label('链接')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    })
                    ->copyable()
                    ->copyMessage('链接已复制')
                    ->copyMessageDuration(1500),
                
                Tables\Columns\BadgeColumn::make('target_text')
                    ->label('打开方式')
                    ->colors([
                        'primary' => '当前窗口',
                        'success' => '新窗口',
                    ]),
                
                Tables\Columns\IconColumn::make('is_active')
                    ->label('状态')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                
                Tables\Columns\BadgeColumn::make('status_text')
                    ->label('显示状态')
                    ->colors([
                        'success' => '正常',
                        'warning' => '已过期',
                        'danger' => '已禁用',
                    ]),
                
                Tables\Columns\TextColumn::make('sort_order')
                    ->label('排序')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('start_time')
                    ->label('开始时间')
                    ->dateTime('Y-m-d H:i')
                    ->placeholder('立即生效')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('end_time')
                    ->label('结束时间')
                    ->dateTime('Y-m-d H:i')
                    ->placeholder('永久有效')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('启用状态')
                    ->placeholder('全部')
                    ->trueLabel('已启用')
                    ->falseLabel('已禁用'),
                
                Tables\Filters\SelectFilter::make('target')
                    ->label('打开方式')
                    ->options([
                        '_self' => '当前窗口',
                        '_blank' => '新窗口',
                    ])
                    ->placeholder('全部'),
                
                Tables\Filters\Filter::make('有效期')
                    ->form([
                        Forms\Components\DatePicker::make('start_date')
                            ->label('开始日期'),
                        Forms\Components\DatePicker::make('end_date')
                            ->label('结束日期'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['start_date'],
                                fn (Builder $query, $date): Builder => $query->whereDate('start_time', '>=', $date),
                            )
                            ->when(
                                $data['end_date'],
                                fn (Builder $query, $date): Builder => $query->whereDate('end_time', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['start_date'] ?? null) {
                            $indicators['start_date'] = '开始时间: ' . $data['start_date'];
                        }
                        if ($data['end_date'] ?? null) {
                            $indicators['end_date'] = '结束时间: ' . $data['end_date'];
                        }
                        return $indicators;
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()->label('查看'),
                Tables\Actions\EditAction::make()->label('编辑'),
                Tables\Actions\Action::make('toggle_status')
                    ->label(fn (Banner $record): string => $record->is_active ? '禁用' : '启用')
                    ->icon(fn (Banner $record): string => $record->is_active ? 'heroicon-s-eye-off' : 'heroicon-s-eye')
                    ->color(fn (Banner $record): string => $record->is_active ? 'danger' : 'success')
                    ->action(function (Banner $record) {
                        $record->update(['is_active' => !$record->is_active]);
                    })
                    ->requiresConfirmation()
                    ->modalHeading(fn (Banner $record): string => $record->is_active ? '禁用轮播图' : '启用轮播图')
                    ->modalSubheading(fn (Banner $record): string => $record->is_active ? '确定要禁用这个轮播图吗？' : '确定要启用这个轮播图吗？'),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make()->label('批量删除'),
                Tables\Actions\BulkAction::make('activate')
                    ->label('批量启用')
                    ->icon('heroicon-s-eye')
                    ->color('success')
                    ->action(function ($records) {
                        $records->each->update(['is_active' => true]);
                    })
                    ->requiresConfirmation()
                    ->modalHeading('批量启用轮播图')
                    ->modalSubheading('确定要启用选中的轮播图吗？'),
                Tables\Actions\BulkAction::make('deactivate')
                    ->label('批量禁用')
                    ->icon('heroicon-s-eye-off')
                    ->color('danger')
                    ->action(function ($records) {
                        $records->each->update(['is_active' => false]);
                    })
                    ->requiresConfirmation()
                    ->modalHeading('批量禁用轮播图')
                    ->modalSubheading('确定要禁用选中的轮播图吗？'),
            ])
            ->defaultSort('sort_order', 'asc');
    }
    
    public static function getRelations(): array
    {
        return [
            //
        ];
    }
    
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBanners::route('/'),
            'create' => Pages\CreateBanner::route('/create'),
            'view' => Pages\ViewBanner::route('/{record}'),
            'edit' => Pages\EditBanner::route('/{record}/edit'),
        ];
    }
} 