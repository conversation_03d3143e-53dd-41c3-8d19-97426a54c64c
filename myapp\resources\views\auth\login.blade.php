<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 商品提醒系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm">
        <div class="max-w-md mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <h1 class="text-xl font-bold text-gray-900">商品提醒系统</h1>
                <div class="space-x-2">
                    <a href="/" class="text-gray-600 hover:text-gray-900">首页</a>
                    <a href="/register" class="bg-blue-500 text-white px-3 py-1 rounded">注册</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 登录表单 -->
    <div class="min-h-screen flex items-center justify-center py-12 px-4">
        <div class="max-w-md w-full space-y-8">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-gray-900">用户登录</h2>
                <p class="mt-2 text-sm text-gray-600">
                    还没有账号？<a href="/register" class="text-blue-600 hover:text-blue-500">立即注册</a>
                </p>
            </div>

            <!-- 消息提示 -->
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    {{ session('error') }}
                </div>
            @endif

            <!-- 登录表单 -->
            <form class="mt-8 space-y-6" action="/login" method="POST">
                @csrf
                <div class="space-y-4">
                    <!-- 邮箱 -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">邮箱地址</label>
                        <input 
                            id="email" 
                            name="email" 
                            type="email" 
                            required 
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('email') border-red-500 @enderror"
                            placeholder="请输入您的邮箱"
                            value="{{ old('email') }}"
                        >
                        @error('email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- 密码 -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700">密码</label>
                        <input 
                            id="password" 
                            name="password" 
                            type="password" 
                            required 
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('password') border-red-500 @enderror"
                            placeholder="请输入您的密码"
                        >
                        @error('password')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- 记住我 -->
                    <div class="flex items-center">
                        <input 
                            id="remember" 
                            name="remember" 
                            type="checkbox" 
                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        >
                        <label for="remember" class="ml-2 block text-sm text-gray-900">记住我</label>
                    </div>
                </div>

                <!-- 登录按钮 -->
                <div>
                    <button 
                        type="submit" 
                        class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                        登录
                    </button>
                </div>
            </form>

            <!-- 底部链接 -->
            <div class="text-center">
                <p class="text-sm text-gray-600">
                    <a href="#" class="text-blue-600 hover:text-blue-500">忘记密码？</a>
                </p>
            </div>
        </div>
    </div>
</body>
</html> 