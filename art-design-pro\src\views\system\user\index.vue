<!-- 用户管理 -->
<!-- art-full-height 自动计算出页面剩余高度 -->
<!-- art-table-card 一个符合系统样式的 class，同时自动撑满剩余高度 -->
<!-- 如果你想使用 template 语法，请移步功能示例下面的高级表格示例 -->
<template>
  <div class="user-page art-full-height">
    <!-- 当前用户信息卡片 -->
    <ElCard class="current-user-card" shadow="never" style="margin-bottom: 16px;">
      <div class="current-user-info">
        <div class="user-avatar">
          <ElAvatar :size="50" :src="currentUser?.avatar">
            {{ currentUser?.name?.charAt(0) }}
          </ElAvatar>
        </div>
        <div class="user-details">
          <h3>{{ currentUser?.name }}</h3>
          <p class="user-email">{{ currentUser?.email }}</p>
          <ElTag :type="getRoleConfig(currentUser?.role || '').type" size="small">
            {{ getRoleConfig(currentUser?.role || '').text }}
          </ElTag>
        </div>
        <div class="user-actions">
          <ElButton size="small" @click="showDialog('edit', currentUser)">编辑资料</ElButton>
          <ElButton size="small" type="primary" @click="showPasswordDialog">修改密码</ElButton>
        </div>
      </div>
    </ElCard>

    <!-- 搜索栏 -->
    <UserSearch v-model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refresh">
        <template #left>
          <ElButton @click="showDialog('add')">新增用户</ElButton>
        </template>
      </ArtTableHeader>

      <!-- 表格 -->
      <ArtTable
        :loading="loading"
        :data="data"
        :columns="columns"
        :pagination="pagination"
        :table-config="{ rowKey: 'id' }"
        :layout="{ marginTop: 10 }"
        @row:selection-change="handleSelectionChange"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
      >
      </ArtTable>

      <!-- 用户弹窗 -->
      <UserDialog
        v-model:visible="dialogVisible"
        :type="dialogType"
        :user-data="currentUserData"
        @submit="handleDialogSubmit"
      />

      <!-- 密码修改弹窗 -->
      <ElDialog v-model="passwordDialogVisible" title="修改密码" width="400px" align-center>
        <ElForm ref="passwordFormRef" :model="passwordForm" :rules="passwordRules" label-width="80px">
          <ElFormItem label="新密码" prop="password">
            <ElInput 
              v-model="passwordForm.password" 
              type="password" 
              show-password 
              placeholder="请输入新密码"
            />
          </ElFormItem>
          <ElFormItem label="确认密码" prop="confirmPassword">
            <ElInput 
              v-model="passwordForm.confirmPassword" 
              type="password" 
              show-password 
              placeholder="请再次输入新密码"
            />
          </ElFormItem>
        </ElForm>
        <template #footer>
          <div class="dialog-footer">
            <ElButton @click="passwordDialogVisible = false">取消</ElButton>
            <ElButton type="primary" @click="handlePasswordSubmit">确定</ElButton>
          </div>
        </template>
      </ElDialog>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import { ElMessageBox, ElMessage, ElTag, ElAvatar, ElCard, ElButton, ElDialog, ElForm, ElFormItem, ElInput } from 'element-plus'
  import { useTable } from '@/composables/useTable'
  import { UserService, type User } from '@/api/userApi'
  import { useUserStore } from '@/store/modules/user'
  import UserSearch from './modules/user-search.vue'
  import UserDialog from './modules/user-dialog.vue'
  import type { FormInstance, FormRules } from 'element-plus'

  defineOptions({ name: 'User' })

  type UserListItem = User
  const { width } = useWindowSize()
  const userStore = useUserStore()

  // 当前用户信息
  const currentUser = computed(() => userStore.userInfo)

  // 弹窗相关
  const dialogType = ref<Form.DialogType>('add')
  const dialogVisible = ref(false)
  const currentUserData = ref<Partial<UserListItem>>({})

  // 密码修改弹窗
  const passwordDialogVisible = ref(false)
  const passwordFormRef = ref<FormInstance>()
  const passwordForm = reactive({
    password: '',
    confirmPassword: ''
  })
  const passwordRules: FormRules = {
    password: [
      { required: true, message: '请输入新密码', trigger: 'blur' },
      { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
    ],
    confirmPassword: [
      { required: true, message: '请再次输入新密码', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value !== passwordForm.password) {
            callback(new Error('两次输入的密码不一致'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]
  }

  // 选中行
  const selectedRows = ref<UserListItem[]>([])

  // 用户状态配置
  const USER_STATUS_CONFIG = {
    '1': { type: 'success' as const, text: '启用' },
    '0': { type: 'danger' as const, text: '禁用' }
  } as const

  // 角色配置
  const ROLE_CONFIG = {
    'R_SUPER': { type: 'danger' as const, text: '超级管理员' },
    'R_ADMIN': { type: 'warning' as const, text: '管理员' },
    'R_EDITOR': { type: 'info' as const, text: '编辑员' },
    'R_USER': { type: 'success' as const, text: '用户' }
  } as const

  /**
   * 获取用户状态配置
   */
  const getUserStatusConfig = (status: number) => {
    return (
      USER_STATUS_CONFIG[status.toString() as keyof typeof USER_STATUS_CONFIG] || {
        type: 'info' as const,
        text: '未知'
      }
    )
  }

  /**
   * 获取角色配置
   */
  const getRoleConfig = (role: string) => {
    return (
      ROLE_CONFIG[role as keyof typeof ROLE_CONFIG] || {
        type: 'info' as const,
        text: '未知'
      }
    )
  }

  // 包装API函数以匹配useTable的期望格式
  const getUserListWrapper = async (params: any) => {
    try {
      const apiParams = {
        page: params.current || 1,
        per_page: params.size || 20,
        search: params.name || '',
        role: params.role || ''
      }
      
      const response = await UserService.getUserList(apiParams)
      console.log('用户列表API响应:', response)
      
      // 处理响应数据结构
      let userData = []
      let totalCount = 0
      let currentPage = 1
      let pageSize = 20
      
      if (response && typeof response === 'object') {
        // 如果响应有data字段且包含用户列表
        if (response.data && typeof response.data === 'object') {
          const data = response.data
          
          // 处理嵌套的data结构: { data: { data: [...], total: 100 } }
          if (data.data && Array.isArray(data.data)) {
            userData = data.data
            totalCount = data.total || 0
            currentPage = data.current_page || 1
            pageSize = data.per_page || 20
          }
          // 处理直接的data结构: { data: [...], total: 100 }
          else if (Array.isArray(data)) {
            userData = data
            totalCount = response.total || data.length
            currentPage = response.current_page || 1
            pageSize = response.per_page || 20
          }
          // 处理其他可能的字段名
          else if (data.list && Array.isArray(data.list)) {
            userData = data.list
            totalCount = data.total || 0
            currentPage = data.current_page || 1
            pageSize = data.per_page || 20
          }
        }
        // 如果响应直接包含用户数据
        else if (Array.isArray(response.data)) {
          userData = response.data
          totalCount = response.total || 0
          currentPage = response.current_page || 1
          pageSize = response.per_page || 20
        }
        // 如果响应本身就是数组
        else if (Array.isArray(response)) {
          userData = response
          totalCount = response.length
          currentPage = 1
          pageSize = response.length
        }
        // 兜底处理：使用response的直接属性
        else {
          userData = response.data || []
          totalCount = response.total || 0
          currentPage = response.current_page || response.current || 1
          pageSize = response.per_page || response.size || 20
        }
      }
      
      const result = {
        records: userData, // 用户列表
        total: totalCount,  // 总数
        current: currentPage, // 当前页
        size: pageSize // 每页数量
      }
      
      console.log('用户列表处理结果:', result)
      return result
    } catch (error) {
      console.error('获取用户列表失败:', error)
      return {
        records: [],
        total: 0,
        current: 1,
        size: 20
      }
    }
  }

  const {
    columns,
    columnChecks,
    tableData: data,
    isLoading: loading,
    paginationState: pagination,
    searchState: searchParams,
    searchData: getDataByPage,
    resetSearch: resetSearchParams,
    onPageSizeChange: handleSizeChange,
    onCurrentPageChange: handleCurrentChange,
    refreshAll: refresh,
    refreshAfterCreate: refreshAfterAdd,
    refreshAfterUpdate: refreshAfterEdit,
    refreshAfterRemove: refreshAfterDelete
  } = useTable<UserListItem>({
    core: {
      apiFn: getUserListWrapper,
      apiParams: {
        current: 1,
        size: 20,
        name: '',
        role: ''
      },
      columnsFactory: () => [
        { type: 'selection' },
        { type: 'index', width: 60, label: '序号' },
        {
          prop: 'id',
          label: 'ID',
          width: 80
        },
        {
          prop: 'name',
          label: '用户名',
          minWidth: 120,
          formatter: (row) => {
            return h('div', { class: 'user-info' }, [
              h(ElAvatar, { size: 32, src: row.avatar }, () => row.name.charAt(0)),
              h('span', { style: 'margin-left: 8px' }, row.name)
            ])
          }
        },
        {
          prop: 'email',
          label: '邮箱',
          minWidth: 180
        },
        {
          prop: 'phone',
          label: '手机号',
          width: 120,
          formatter: (row) => row.phone || '-'
        },
        {
          prop: 'role',
          label: '角色',
          width: 120,
          formatter: (row) => {
            const roleConfig = getRoleConfig(row.role)
            return h(ElTag, { type: roleConfig.type }, () => roleConfig.text)
          }
        },
        {
          prop: 'status',
          label: '状态',
          width: 100,
          formatter: (row) => {
            const statusConfig = getUserStatusConfig(row.status)
            return h(ElTag, { type: statusConfig.type }, () => statusConfig.text)
          }
        },
        {
          prop: 'created_at',
          label: '创建时间',
          width: 160,
          formatter: (row) => {
            return new Date(row.created_at).toLocaleString('zh-CN')
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 220,
          fixed: 'right',
          formatter: (row) =>
            h('div', { style: 'display: flex; gap: 4px; flex-wrap: wrap;' }, [
              h(ArtButtonTable, {
                type: 'edit',
                onClick: () => showDialog('edit', row)
              }),
              h(ArtButtonTable, {
                type: 'password',
                text: '重置密码',
                onClick: () => resetPassword(row)
              }),
              h(ArtButtonTable, {
                type: row.status ? 'disable' : 'enable',
                text: row.status ? '禁用' : '启用',
                onClick: () => toggleUserStatus(row)
              }),
              h(ArtButtonTable, {
                type: 'delete',
                onClick: () => deleteUser(row)
              })
            ])
        }
      ]
    },
    transform: {
      dataTransformer: (records: any) => {
        if (!Array.isArray(records)) {
          console.warn('数据转换器: 期望数组类型，实际收到:', typeof records)
          return []
        }
        return records
      }
    },
    performance: {
      enableCache: false,
      cacheTime: 0
    },
    hooks: {
      onError: (error) => ElMessage.error(error.message || '获取数据失败')
    },
    debug: {
      enableLog: true
    }
  })

  const showDialog = (type: Form.DialogType, row?: UserListItem): void => {
    dialogType.value = type
    currentUserData.value = row || {}
    nextTick(() => {
      dialogVisible.value = true
    })
  }

  const showPasswordDialog = (): void => {
    passwordForm.password = ''
    passwordForm.confirmPassword = ''
    passwordDialogVisible.value = true
  }

  const resetPassword = async (row: UserListItem): Promise<void> => {
    try {
      await ElMessageBox.confirm(`确定要重置用户 "${row.name}" 的密码吗？新密码将为：123456`, '重置密码', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      
      const response = await UserService.updateUser(row.id, {
        name: row.name,
        email: row.email,
        role: row.role,
        password: '123456'
      })
      
      if (response.code === 200) {
        ElMessage.success('密码重置成功，新密码为：123456')
      } else {
        ElMessage.error(response.msg || '密码重置失败')
      }
    } catch (error: any) {
      if (error !== 'cancel') {
        ElMessage.error(error.message || '密码重置失败')
      }
    }
  }

  const handlePasswordSubmit = async (): Promise<void> => {
    if (!passwordFormRef.value) return

    await passwordFormRef.value.validate(async (valid) => {
      if (valid) {
        try {
          const response = await UserService.updateUser(currentUser.value.id, {
            name: currentUser.value.name,
            email: currentUser.value.email,
            role: currentUser.value.role,
            password: passwordForm.password
          })
          
          if (response.code === 200) {
            ElMessage.success('密码修改成功')
            passwordDialogVisible.value = false
            passwordForm.password = ''
            passwordForm.confirmPassword = ''
          } else {
            ElMessage.error(response.msg || '密码修改失败')
          }
        } catch (error: any) {
          ElMessage.error(error.message || '密码修改失败')
        }
      }
    })
  }

  const toggleUserStatus = async (row: UserListItem): Promise<void> => {
    try {
      const action = row.status ? '禁用' : '启用'
      await ElMessageBox.confirm(`确定要${action}该用户吗？`, `${action}用户`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      
      const response = await UserService.toggleUserStatus(row.id)
      
      if (response.code === 200) {
        ElMessage.success(response.msg || `${action}成功`)
        refresh()
      } else {
        ElMessage.error(response.msg || `${action}失败`)
      }
    } catch (error: any) {
      if (error !== 'cancel') {
        ElMessage.error(error.message || '操作失败')
      }
    }
  }

  const deleteUser = async (row: UserListItem): Promise<void> => {
    try {
      await ElMessageBox.confirm(`确定要删除用户 "${row.name}" 吗？此操作不可逆！`, '删除用户', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      })
      
      const response = await UserService.deleteUser(row.id)
      
      if (response.code === 200) {
        ElMessage.success(response.msg || '删除成功')
        refreshAfterDelete()
      } else {
        ElMessage.error(response.msg || '删除失败')
      }
    } catch (error: any) {
      if (error !== 'cancel') {
        ElMessage.error(error.message || '删除失败')
      }
    }
  }

  const handleDialogSubmit = async () => {
    try {
      dialogVisible.value = false
      await (dialogType.value === 'add' ? refreshAfterAdd() : refreshAfterEdit())
      currentUserData.value = {}
    } catch (error) {
      console.error('提交失败:', error)
    }
  }

  const handleSelectionChange = (selection: UserListItem[]): void => {
    selectedRows.value = selection
  }
</script>

<style lang="scss" scoped>
  .user-page {
    .current-user-card {
      .current-user-info {
        display: flex;
        align-items: center;
        gap: 16px;

        .user-avatar {
          flex-shrink: 0;
        }

        .user-details {
          flex: 1;

          h3 {
            margin: 0 0 4px 0;
            font-size: 16px;
            font-weight: 500;
          }

          .user-email {
            margin: 0 0 8px 0;
            color: var(--el-text-color-regular);
            font-size: 14px;
          }
        }

        .user-actions {
          flex-shrink: 0;
          display: flex;
          gap: 8px;
        }
      }
    }

    :deep(.user-info) {
      display: flex;
      align-items: center;
    }

    :deep(.user) {
      .avatar {
        width: 40px;
        height: 40px;
        border-radius: 6px;
      }

      > div {
        margin-left: 10px;

        .user-name {
          font-weight: 500;
          color: var(--art-text-gray-800);
        }
      }
    }
  }
</style>
