<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $product->name }} - 商品详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        body { 
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif; 
            background: linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e1b4b 100%);
            min-height: 100vh;
        }
        .image-zoom {
            transition: transform 0.3s ease;
            border-radius: 1rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .image-zoom:hover {
            transform: scale(1.05);
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .glass-card:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(59, 130, 246, 0.5);
        }
        .product-info-gradient {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
        }
        .follow-card {
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .similar-card {
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .similar-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
            border-color: rgba(59, 130, 246, 0.5);
        }
    </style>
</head>
<body class="text-white">
    <!-- 导航栏 -->
    <nav class="bg-black/20 backdrop-blur-md border-b border-white/10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-8">
                    <a href="/" class="text-xl font-bold text-white">商品提醒系统</a>
                    <nav class="hidden md:flex space-x-8">
                        <a href="/" class="text-gray-300 hover:text-white transition-colors">首页</a>
                        <a href="{{ route('products.index') }}" class="text-gray-300 hover:text-white transition-colors">商品浏览</a>
                        @auth
                            <a href="{{ route('history') }}" class="text-gray-300 hover:text-white transition-colors">我的关注</a>
                        @endauth
                    </nav>
                </div>
                <div class="flex items-center space-x-4">
                    @auth
                        <a href="/dashboard" class="text-gray-300 hover:text-white transition-colors">用户中心</a>
                        <form action="/logout" method="POST" class="inline">
                            @csrf
                            <button type="submit" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">退出登录</button>
                        </form>
                    @else
                        <a href="/subscribe" class="text-gray-300 hover:text-white transition-colors">订阅服务</a>
                        <a href="/login" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">登录</a>
                    @endauth
                </div>
            </div>
        </div>
    </nav>

    <!-- 面包屑导航 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-4">
                <li><a href="/" class="text-gray-400 hover:text-white transition-colors">首页</a></li>
                <li><span class="text-gray-500">></span></li>
                <li><a href="{{ route('products.index') }}" class="text-gray-400 hover:text-white transition-colors">商品浏览</a></li>
                <li><span class="text-gray-500">></span></li>
                <li><span class="text-white">{{ $product->name }}</span></li>
            </ol>
        </nav>
    </div>

    <!-- 商品详情 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid lg:grid-cols-2 gap-12">
            <!-- 左侧：商品图片 -->
            <div class="space-y-4">
                <div class="aspect-w-1 aspect-h-1 bg-gray-800 rounded-2xl overflow-hidden">
                    <img src="{{ $product->image_url ?: 'https://via.placeholder.com/600x600' }}" 
                         alt="{{ $product->name }}" 
                         class="w-full h-full object-cover image-zoom">
                </div>
                
                <!-- 商品标签 -->
                <div class="flex flex-wrap gap-2">
                    @if($product->has_discount)
                        <span class="bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm font-bold backdrop-blur-sm">
                            特价 -{{ $product->discount_percentage }}%
                        </span>
                    @endif
                    <span class="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full text-sm backdrop-blur-sm">
                        {{ $product->category }}
                    </span>
                    @if($product->region)
                        <span class="bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm backdrop-blur-sm">
                            {{ $product->region->name }}
                        </span>
                    @endif
                </div>
            </div>

            <!-- 右侧：商品信息 -->
            <div class="space-y-6">
                <!-- 商品标题 -->
                <div>
                    <h1 class="text-3xl font-bold text-white mb-2">{{ $product->name }}</h1>
                    <p class="text-lg text-gray-300">{{ $product->brand }}</p>
                </div>

                <!-- 价格 -->
                <div class="glass-card product-info-gradient rounded-2xl p-6">
                    <div class="flex items-center space-x-4">
                        <span class="text-4xl font-bold text-green-400">¥{{ number_format($product->price, 2) }}</span>
                        @if($product->original_price && $product->original_price > $product->price)
                            <span class="text-xl text-gray-500 line-through">¥{{ number_format($product->original_price, 2) }}</span>
                            <span class="bg-gradient-to-r from-red-500 to-pink-500 text-white px-2 py-1 rounded text-sm font-bold">
                                省 ¥{{ number_format($product->original_price - $product->price, 2) }}
                            </span>
                        @endif
                    </div>
                    <p class="text-sm text-gray-300 mt-2">
                        {{ $product->followers_count }} 人关注此商品
                    </p>
                </div>

                <!-- 商品信息 -->
                <div class="glass-card rounded-2xl p-6">
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="font-semibold text-gray-300">商品编号：</span>
                            <span class="text-gray-400">{{ $product->sku ?: 'N/A' }}</span>
                        </div>
                        <div>
                            <span class="font-semibold text-gray-300">库存状态：</span>
                            <span class="text-gray-400">{{ $product->stock_status_text }}</span>
                        </div>
                        <div>
                            <span class="font-semibold text-gray-300">分类：</span>
                            <span class="text-gray-400">{{ $product->category }}</span>
                        </div>
                        <div>
                            <span class="font-semibold text-gray-300">地区：</span>
                            <span class="text-gray-400">{{ $product->region->name ?? 'N/A' }}</span>
                        </div>
                    </div>
                </div>

                <!-- 商品描述 -->
                @if($product->description)
                    <div class="glass-card rounded-2xl p-6">
                        <h3 class="text-lg font-semibold text-white mb-2">商品描述</h3>
                        <p class="text-gray-300 leading-relaxed">{{ $product->description }}</p>
                    </div>
                @endif

                <!-- 关注设置 -->
                @auth
                    @if($isFollowed)
                        <div class="follow-card bg-green-500/10 rounded-2xl p-6">
                            <div class="flex items-center mb-3">
                                <svg class="w-5 h-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span class="font-semibold text-green-300">已关注此商品</span>
                            </div>
                            <div class="space-y-2 text-sm text-green-300">
                                @if($followSettings->email_notifications)
                                    <p>✓ 邮件通知已开启</p>
                                @endif
                                @if($followSettings->wechat_notifications)
                                    <p>✓ 微信通知已开启</p>
                                @endif
                                <p>关注时间：{{ $followSettings->created_at->format('Y-m-d H:i') }}</p>
                            </div>
                            <div class="mt-4 flex space-x-2">
                                <button data-product-id="{{ $product->id }}" onclick="updateNotificationSettings(this)" 
                                        class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300 transform hover:scale-105">
                                    修改通知设置
                                </button>
                                <button data-product-id="{{ $product->id }}" onclick="unfollowProduct(this)" 
                                        class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300 transform hover:scale-105">
                                    取消关注
                                </button>
                            </div>
                        </div>
                    @else
                        <div class="follow-card bg-blue-500/10 rounded-2xl p-6">
                            <h3 class="font-semibold text-blue-300 mb-3">关注此商品</h3>
                            <p class="text-blue-200 text-sm mb-4">关注后您将收到该商品的价格变动、库存更新等重要通知</p>
                            
                            <div class="space-y-3">
                                <label class="flex items-center">
                                    <input type="checkbox" id="emailNotifications" checked class="rounded border-gray-500 bg-white/10 text-blue-500 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-300">接收邮件通知</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" id="wechatNotifications" class="rounded border-gray-500 bg-white/10 text-blue-500 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-300">接收微信通知（需绑定微信）</span>
                                </label>
                            </div>
                            
                            <button data-product-id="{{ $product->id }}" onclick="followProduct(this)" 
                                    class="mt-4 w-full bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                                关注商品
                            </button>
                        </div>
                    @endif
                @else
                    <div class="follow-card bg-gray-500/10 rounded-2xl p-6">
                        <h3 class="font-semibold text-white mb-2">关注此商品</h3>
                        <p class="text-gray-300 text-sm mb-4">登录后可关注商品，获取价格变动和库存通知</p>
                        <a href="/login" class="w-full bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold text-center block transition-all duration-300 transform hover:scale-105">
                            登录后关注
                        </a>
                    </div>
                @endauth

                <!-- 操作按钮 -->
                <div class="flex space-x-4">
                    @if($product->product_url)
                        <a href="{{ $product->product_url }}" target="_blank" 
                           class="flex-1 bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold text-center transition-all duration-300 transform hover:scale-105">
                            查看原商品页面
                        </a>
                    @endif
                    <button onclick="shareProduct()" 
                            class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                        分享
                    </button>
                </div>
            </div>
        </div>

        <!-- 相似商品推荐 -->
        @if($similarProducts->count() > 0)
            <div class="mt-16">
                <h2 class="text-2xl font-bold text-white mb-8 text-center">相似商品推荐</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    @foreach($similarProducts as $similar)
                        <div class="similar-card glass-card rounded-2xl overflow-hidden">
                            <img src="{{ $similar->image_url ?: 'https://via.placeholder.com/300x200' }}" 
                                 alt="{{ $similar->name }}" 
                                 class="w-full h-40 object-cover">
                            <div class="p-4">
                                <h3 class="font-semibold text-white mb-1 line-clamp-2">{{ $similar->name }}</h3>
                                <p class="text-sm text-gray-400 mb-2">{{ $similar->brand }}</p>
                                <div class="flex items-center justify-between">
                                    <span class="text-lg font-bold text-green-400">¥{{ number_format($similar->price, 2) }}</span>
                                    <span class="text-sm text-gray-400">{{ $similar->followers_count }} 关注</span>
                                </div>
                                <a href="{{ route('products.show', $similar->id) }}" 
                                   class="mt-3 block w-full bg-blue-500 hover:bg-blue-600 text-white text-center py-2 rounded-lg text-sm font-semibold transition-all duration-300 transform hover:scale-105">
                                    查看详情
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif
    </main>

    <!-- JavaScript -->
    <script>
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        // 关注商品
        function followProduct(button) {
            const productId = button.getAttribute('data-product-id');
            const emailNotifications = document.getElementById('emailNotifications').checked;
            const wechatNotifications = document.getElementById('wechatNotifications').checked;

            fetch(`/products/${productId}/follow`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify({
                    email_notifications: emailNotifications,
                    wechat_notifications: wechatNotifications
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert(data.message || '关注失败，请稍后重试');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('关注失败，请稍后重试');
            });
        }

        // 取消关注商品
        function unfollowProduct(button) {
            if (!confirm('确定要取消关注这个商品吗？')) {
                return;
            }

            const productId = button.getAttribute('data-product-id');

            fetch(`/history/products/${productId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': csrfToken
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('已取消关注');
                    location.reload();
                } else {
                    alert(data.message || '取消关注失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('取消关注失败，请稍后重试');
            });
        }

        // 更新通知设置
        function updateNotificationSettings(button) {
            const productId = button.getAttribute('data-product-id');
            const emailNotifications = document.getElementById('emailNotifications').checked;
            const wechatNotifications = document.getElementById('wechatNotifications').checked;

            fetch(`/products/${productId}/update-notification-settings`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify({
                    email_notifications: emailNotifications,
                    wechat_notifications: wechatNotifications
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert(data.message || '更新通知设置失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('更新通知设置失败，请稍后重试');
            });
        }

        // 分享商品
        function shareProduct() {
            if (navigator.share) {
                navigator.share({
                    title: '{{ $product->name }}',
                    text: '发现一个好商品：{{ $product->name }} - ¥{{ number_format($product->price, 2) }}',
                    url: window.location.href
                });
            } else {
                // 复制链接到剪贴板
                navigator.clipboard.writeText(window.location.href).then(() => {
                    alert('商品链接已复制到剪贴板');
                });
            }
        }
    </script>
</body>
</html> 