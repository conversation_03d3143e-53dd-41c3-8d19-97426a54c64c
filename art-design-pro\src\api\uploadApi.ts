import request from '@/utils/http'

export interface UploadResponse {
  success: boolean
  message: string
  data?: {
    path: string
    url: string
    full_url: string
    filename: string
    size: number
    mime_type: string
  }
}

export class UploadService {
  // 上传图片
  static uploadImage(file: File): Promise<UploadResponse> {
    const formData = new FormData()
    formData.append('image', file)
    
    return request.post<UploadResponse>({
      url: '/api/upload/image',
      data: formData
    })
  }

  // 删除图片
  static deleteImage(path: string): Promise<UploadResponse> {
    return request.del<UploadResponse>({
      url: '/api/upload/image',
      data: { path }
    })
  }
} 