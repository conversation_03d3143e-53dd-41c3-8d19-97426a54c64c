@echo off
chcp 65001 >nul
echo.
echo =============================================
echo    安装FilamentPHP管理后台 (使用国内镜像)
echo =============================================
echo.

:: 检查是否在正确的目录
if not exist "artisan" (
    echo 错误: 请在Laravel项目根目录运行此脚本
    echo 当前目录: %cd%
    pause
    exit /b 1
)

echo 📁 当前目录: %cd%
echo.

:: 检查PHP是否可用
echo 🔍 检查PHP环境...
where php >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到PHP命令
    echo 请确保PHP已安装并添加到系统PATH中
    pause
    exit /b 1
)

php -v
echo ✅ PHP环境检查完成
echo.

:: 检查composer是否已安装
echo 🔍 检查Composer...
where composer >nul 2>&1
if errorlevel 1 (
    echo 📥 Composer未安装，开始安装...
    echo.
    
    :: 下载composer安装器
    echo 下载Composer安装器...
    php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
    
    if not exist "composer-setup.php" (
        echo ❌ 下载Composer安装器失败
        pause
        exit /b 1
    )
    
    :: 安装composer
    echo 安装Composer...
    php composer-setup.php --install-dir=. --filename=composer.phar
    
    if not exist "composer.phar" (
        echo ❌ Composer安装失败
        del composer-setup.php 2>nul
        pause
        exit /b 1
    )
    
    :: 清理安装器
    del composer-setup.php
    echo ✅ Composer安装完成
    set COMPOSER_CMD=php composer.phar
) else (
    echo ✅ Composer已安装
    set COMPOSER_CMD=composer
)
echo.

:: 配置国内镜像 (阿里云)
echo 🚀 配置Composer国内镜像 (阿里云)...
%COMPOSER_CMD% config -g repo.packagist composer https://mirrors.aliyun.com/composer/
echo ✅ 镜像配置完成
echo.

:: 更新composer
echo 📦 更新Composer依赖...
%COMPOSER_CMD% install --no-dev --optimize-autoloader
echo.

:: 安装FilamentPHP
echo 🎨 开始安装FilamentPHP管理后台...
echo.

echo 1️⃣ 安装Filament核心包...
%COMPOSER_CMD% require filament/filament:"^3.0"

if errorlevel 1 (
    echo ❌ FilamentPHP核心包安装失败
    pause
    exit /b 1
)

echo.
echo 2️⃣ 发布Filament资源...
php artisan filament:install --panels

if errorlevel 1 (
    echo ❌ Filament资源发布失败
    pause
    exit /b 1
)

echo.
echo 3️⃣ 创建管理员用户...
echo 请输入管理员信息:
php artisan make:filament-user

echo.
echo 4️⃣ 生成应用密钥 (如果需要)...
php artisan key:generate

echo.
echo 5️⃣ 运行数据库迁移...
php artisan migrate

echo.
echo 🎉 FilamentPHP安装完成！
echo.
echo ==========================================
echo        安装完成信息
echo ==========================================
echo.
echo 🌐 管理后台访问地址: http://localhost:8000/admin
echo 📱 启动开发服务器: php artisan serve
echo 📚 FilamentPHP文档: https://filamentphp.com/docs
echo.
echo 💡 下一步操作:
echo    1. 运行: php artisan serve
echo    2. 访问: http://localhost:8000/admin
echo    3. 使用刚创建的管理员账号登录
echo.
echo ==========================================
pause 