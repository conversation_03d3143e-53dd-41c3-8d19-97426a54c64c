<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Plan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'price',
        'currency',
        'billing_cycle',
        'duration_days',
        'max_products',
        'max_notifications',
        'features',
        'is_popular',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'features' => 'array',
        'is_popular' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * 订阅关联
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * 支付订单关联
     */
    public function paymentOrders(): HasMany
    {
        return $this->hasMany(PaymentOrder::class);
    }

    /**
     * 获取激活的套餐
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 获取热门套餐
     */
    public function scopePopular($query)
    {
        return $query->where('is_popular', true);
    }

    /**
     * 按计费周期筛选
     */
    public function scopeByBillingCycle($query, $cycle)
    {
        return $query->where('billing_cycle', $cycle);
    }

    /**
     * 获取格式化价格
     */
    public function getFormattedPriceAttribute(): string
    {
        return $this->currency . ' ' . number_format($this->price, 2);
    }

    /**
     * 检查是否为终身套餐
     */
    public function getIsLifetimeAttribute(): bool
    {
        return $this->billing_cycle === 'lifetime';
    }

    /**
     * 获取套餐周期显示文本
     */
    public function getBillingCycleTextAttribute(): string
    {
        return match($this->billing_cycle) {
            'monthly' => 'Monthly',
            'yearly' => 'Yearly',
            'lifetime' => 'Lifetime',
            default => ucfirst($this->billing_cycle),
        };
    }
}
