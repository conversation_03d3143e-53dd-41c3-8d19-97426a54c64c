<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PlanResource\Pages;
use App\Filament\Resources\PlanResource\RelationManagers;
use App\Models\Plan;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PlanResource extends Resource
{
    protected static ?string $model = Plan::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-list';

    protected static ?string $navigationLabel = '套餐管理';

    protected static ?string $modelLabel = '套餐';

    protected static ?string $pluralModelLabel = '套餐';

    protected static ?string $navigationGroup = '基础设置';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('基本信息')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('套餐名称')
                            ->required()
                            ->maxLength(255),
                        
                        Forms\Components\Textarea::make('description')
                            ->label('套餐描述')
                            ->maxLength(1000)
                            ->rows(3),
                        
                        Forms\Components\TextInput::make('price')
                            ->label('价格')
                            ->required()
                            ->numeric()
                            ->step(0.01)
                            ->prefix('¥'),
                        
                        Forms\Components\Toggle::make('is_active')
                            ->label('启用状态')
                            ->default(true),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('套餐功能')
                    ->schema([
                        Forms\Components\Textarea::make('features')
                            ->label('功能列表')
                            ->placeholder('每行输入一个功能，例如：
商品监控服务
价格提醒
库存通知
24小时客服支持')
                            ->rows(6)
                            ->helperText('每行输入一个功能特性')
                            ->dehydrateStateUsing(function ($state) {
                                if (empty($state)) {
                                    return json_encode([]);
                                }
                                $features = array_filter(array_map('trim', explode("\n", $state)));
                                return json_encode($features);
                            })
                            ->mutateDehydratedStateUsing(function ($state) {
                                if (empty($state)) {
                                    return '';
                                }
                                $features = json_decode($state, true);
                                return is_array($features) ? implode("\n", $features) : '';
                            }),
                    ])
                    ->columns(1),

                Forms\Components\Section::make('其他设置')
                    ->schema([
                        Forms\Components\TextInput::make('sort_order')
                            ->label('排序')
                            ->numeric()
                            ->default(0),
                        
                        Forms\Components\Toggle::make('is_popular')
                            ->label('推荐套餐')
                            ->default(false),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('套餐名称')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('description')
                    ->label('描述')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),
                
                Tables\Columns\TextColumn::make('price')
                    ->label('价格')
                    ->money('CNY')
                    ->sortable(),
                
                Tables\Columns\IconColumn::make('is_active')
                    ->label('状态')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                
                Tables\Columns\IconColumn::make('is_popular')
                    ->label('推荐')
                    ->boolean()
                    ->trueIcon('heroicon-o-star')
                    ->falseIcon('heroicon-o-star')
                    ->trueColor('warning')
                    ->falseColor('secondary'),
                
                Tables\Columns\TextColumn::make('sort_order')
                    ->label('排序')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('启用状态')
                    ->placeholder('全部')
                    ->trueLabel('已启用')
                    ->falseLabel('已禁用'),
                
                Tables\Filters\TernaryFilter::make('is_popular')
                    ->label('推荐套餐')
                    ->placeholder('全部')
                    ->trueLabel('推荐')
                    ->falseLabel('非推荐'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()->label('查看'),
                Tables\Actions\EditAction::make()->label('编辑'),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make()->label('批量删除'),
            ])
            ->defaultSort('sort_order', 'asc');
    }
    
    public static function getRelations(): array
    {
        return [
            //
        ];
    }
    
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPlans::route('/'),
            'create' => Pages\CreatePlan::route('/create'),
            'view' => Pages\ViewPlan::route('/{record}'),
            'edit' => Pages\EditPlan::route('/{record}/edit'),
        ];
    }    
}
