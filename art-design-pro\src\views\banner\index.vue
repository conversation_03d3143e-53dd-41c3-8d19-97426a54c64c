<template>
  <div class="app-container">
    <!-- 头部操作栏 -->
    <div class="header-actions">
      <el-row :gutter="16" align="middle">
        <el-col :span="12">
          <h2 class="page-title">轮播图管理</h2>
        </el-col>
        <el-col :span="12" class="text-right">
          <el-button type="primary" @click="handleAdd" :icon="Plus">
            添加轮播图
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="16" class="stats-cards">
      <el-col :span="6" v-for="stat in statsData" :key="stat.key">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon" :class="stat.iconClass">
              <el-icon :size="24">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stat.value }}</div>
              <div class="stats-label">{{ stat.label }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 轮播图列表 -->
    <el-card class="list-card">
      <template #header>
        <div class="card-header">
          <span>轮播图列表</span>
          <el-button @click="loadBannerList" :loading="loading" :icon="Refresh">
            刷新
          </el-button>
        </div>
      </template>

      <el-table :data="bannerList" v-loading="loading" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="title" label="标题" min-width="150" />
        <el-table-column label="图片" width="120">
          <template #default="{ row }">
            <el-image
              :src="row.image"
              :alt="row.alt_text || row.title"
              fit="cover"
              style="width: 80px; height: 50px; border-radius: 4px;"
              :preview-src-list="[row.image]"
            />
          </template>
        </el-table-column>
        <el-table-column label="链接" min-width="150">
          <template #default="{ row }">
            <el-link v-if="row.link" :href="row.link" :target="row.target" type="primary">
              {{ row.link }}
            </el-link>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="sort_order" label="排序" width="80" />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.is_active"
              @change="handleToggleStatus(row)"
              :loading="row.statusLoading"
              active-text="启用"
              inactive-text="禁用"
            />
          </template>
        </el-table-column>
        <el-table-column label="时间范围" width="180">
          <template #default="{ row }">
            <div class="time-range">
              <div v-if="row.start_time" class="time-item">
                <span class="time-label">开始:</span>
                <span class="time-value">{{ formatTime(row.start_time) }}</span>
              </div>
              <div v-if="row.end_time" class="time-item">
                <span class="time-label">结束:</span>
                <span class="time-value">{{ formatTime(row.end_time) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)" :icon="Edit">
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
              :icon="Delete"
              :loading="row.deleteLoading"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑轮播图弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogMode === 'add' ? '添加轮播图' : '编辑轮播图'"
      width="600px"
      :before-close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="80px"
        @submit.prevent
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="formData.title" placeholder="请输入轮播图标题" />
        </el-form-item>
        <el-form-item label="图片" prop="image">
          <div class="image-upload-section">
            <!-- 图片上传 -->
            <el-upload
              class="image-uploader"
              :auto-upload="false"
              :show-file-list="false"
              :on-change="handleFileChange"
              :disabled="uploadLoading"
              accept="image/*"
              drag
              :file-list="[]"
              :limit="1"
            >
              <div v-if="uploadLoading" class="upload-loading">
                <el-icon class="is-loading"><Loading /></el-icon>
                <div class="loading-text">上传中...</div>
              </div>
              <div v-else-if="formData.image" class="image-preview">
                <img :src="formData.image" alt="轮播图" />
                <div class="image-overlay">
                  <el-icon class="upload-icon"><Plus /></el-icon>
                  <span>重新上传</span>
                </div>
              </div>
              <div v-else class="upload-placeholder">
                <el-icon class="upload-icon"><Plus /></el-icon>
                <div class="upload-text">点击或拖拽上传图片</div>
                <div class="upload-tips">支持 JPG, PNG, GIF, WEBP 格式，大小不超过 5MB</div>
              </div>
            </el-upload>
            
            <!-- 手动输入URL -->
            <div class="url-input-section">
              <div class="section-divider">
                <span>或</span>
              </div>
              <el-input
                v-model="formData.image"
                placeholder="手动输入图片URL"
                clearable
              >
                <template #prepend>图片URL</template>
              </el-input>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="链接" prop="link">
          <el-input v-model="formData.link" placeholder="请输入跳转链接（可选）" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入轮播图描述（可选）"
          />
        </el-form-item>
        <el-form-item label="替代文本" prop="alt_text">
          <el-input v-model="formData.alt_text" placeholder="请输入图片替代文本（可选）" />
        </el-form-item>
        <el-form-item label="排序" prop="sort_order">
          <el-input-number v-model="formData.sort_order" :min="0" :max="999" />
        </el-form-item>
        <el-form-item label="打开方式" prop="target">
          <el-radio-group v-model="formData.target">
            <el-radio label="_self">当前窗口</el-radio>
            <el-radio label="_blank">新窗口</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="启用状态" prop="is_active">
          <el-switch v-model="formData.is_active" />
        </el-form-item>
        <el-form-item label="开始时间" prop="start_time">
          <el-date-picker
            v-model="formData.start_time"
            type="datetime"
            placeholder="选择开始时间（可选）"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="结束时间" prop="end_time">
          <el-date-picker
            v-model="formData.end_time"
            type="datetime"
            placeholder="选择结束时间（可选）"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ dialogMode === 'add' ? '添加' : '更新' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, markRaw } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete, Refresh, PictureRounded, DataLine, Warning, Check, Loading } from '@element-plus/icons-vue'
import { BannerService, type BannerItem, type BannerStats, type BannerCreateParams } from '@/api/bannerApi'
import { UploadService } from '@/api/uploadApi'
import { useUserStore } from '@/store/modules/user'

// 定义组件名称
defineOptions({
  name: 'BannerList'
})

// Store
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const bannerList = ref<BannerItem[]>([])
const statsData = ref([
  { key: 'total', label: '总数', value: 0, icon: markRaw(PictureRounded), iconClass: 'text-blue-500' },
  { key: 'active', label: '激活', value: 0, icon: markRaw(Check), iconClass: 'text-green-500' },
  { key: 'valid', label: '有效', value: 0, icon: markRaw(DataLine), iconClass: 'text-orange-500' },
  { key: 'expired', label: '过期', value: 0, icon: markRaw(Warning), iconClass: 'text-red-500' }
])

// 弹窗相关
const dialogVisible = ref(false)
const dialogMode = ref<'add' | 'edit'>('add')
const submitLoading = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive<BannerCreateParams & { id?: number }>({
  title: '',
  image: '',
  link: '',
  description: '',
  alt_text: '',
  sort_order: 0,
  target: '_self',
  is_active: true,
  start_time: '',
  end_time: ''
})

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入轮播图标题', trigger: 'blur' },
    { min: 2, max: 255, message: '标题长度在 2 到 255 个字符', trigger: 'blur' }
  ],
  image: [
    { required: true, message: '请上传图片或输入图片URL', trigger: 'blur' }
  ],
  end_time: [
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (value && formData.start_time && new Date(value) <= new Date(formData.start_time)) {
          callback(new Error('结束时间必须晚于开始时间'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 图片上传相关方法
const uploadLoading = ref(false)

// 简化的测试上传函数
const testDirectUpload = async (file: File) => {
  console.log('=== 直接测试上传 ===')
  console.log('文件:', file)
  
  const formData = new FormData()
  formData.append('image', file)
  
  console.log('FormData keys:', Array.from(formData.keys()))
  console.log('FormData values:', Array.from(formData.values()))
  
  try {
    const response = await fetch('http://127.0.0.1:8002/api/upload/image', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userStore.accessToken}`
      },
      body: formData
    })
    
    const result = await response.json()
    console.log('直接上传结果:', result)
    return result
  } catch (error) {
    console.error('直接上传错误:', error)
    throw error
  }
}

const handleFileChange = async (uploadFile: any) => {
  // 调试信息 - 检查uploadFile对象结构
  console.log('uploadFile对象:', uploadFile)
  console.log('uploadFile.raw:', uploadFile.raw)
  console.log('uploadFile.file:', uploadFile.file)
  
  // 尝试获取正确的文件对象
  const file = uploadFile.raw || uploadFile.file || uploadFile
  
  // 调试信息
  console.log('选择的文件:', file)
  console.log('文件名:', file?.name)
  console.log('文件大小:', file?.size)
  console.log('文件类型:', file?.type)
  console.log('文件是否为File对象:', file instanceof File)
  
  // 文件验证
  if (!file) {
    ElMessage.error('未选择文件')
    return
  }
  
  if (!(file instanceof File)) {
    ElMessage.error('文件对象无效')
    console.error('无效的文件对象:', file)
    return
  }
  
  if (!beforeImageUpload(file)) {
    return
  }
  
  // 开始上传
  uploadLoading.value = true
  try {
    console.log('开始上传文件:', file.name)
    
    // 首先尝试直接上传测试
    console.log('=== 尝试直接上传 ===')
    const directResult = await testDirectUpload(file)
    
    if (directResult.success) {
      formData.image = directResult.data.full_url
      ElMessage.success('图片上传成功')
      return
    }
    
    // 如果直接上传失败，尝试使用API服务
    console.log('=== 尝试API服务上传 ===')
    const response = await UploadService.uploadImage(file)
    console.log('API上传响应:', response)
    
    if (response.success) {
      formData.image = response.data!.full_url
      ElMessage.success('图片上传成功')
    } else {
      ElMessage.error(response.message || '图片上传失败')
    }
  } catch (error: any) {
    console.error('图片上传失败:', error)
    console.error('错误详情:', error.response?.data)
    const errorMessage = error.response?.data?.message || error.message || '图片上传失败，请重试'
    ElMessage.error(errorMessage)
  } finally {
    uploadLoading.value = false
  }
}

const beforeImageUpload = (file: File) => {
  const isImage = /^image\//.test(file.type)
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

// 方法
const loadBannerList = async () => {
  loading.value = true
  try {
    const response = await BannerService.getBannerList()
    console.log('轮播图列表响应:', response)
    
    // 处理响应数据结构
    let bannerData: BannerItem[] = []
    
    if (response && typeof response === 'object') {
      // 如果响应有data字段，使用data字段
      if (response.data && Array.isArray(response.data)) {
        bannerData = response.data
      } 
      // 如果响应本身就是数组
      else if (Array.isArray(response)) {
        bannerData = response
      }
      // 如果响应结构不正确，记录错误并使用空数组
      else {
        console.warn('轮播图列表响应格式不正确:', response)
        bannerData = []
      }
    }
    
    bannerList.value = bannerData.map(item => ({
      ...item,
      statusLoading: false,
      deleteLoading: false
    }))
  } catch (error) {
    ElMessage.error('获取轮播图列表失败')
    console.error('获取轮播图列表失败:', error)
    bannerList.value = []
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    const response = await BannerService.getBannerStats()
    console.log('统计数据响应:', response)
    
    // 处理响应数据结构
    let statsInfo: BannerStats
    
    if (response && typeof response === 'object') {
      // 如果响应有data字段，使用data字段
      if (response.data && typeof response.data === 'object') {
        statsInfo = response.data
      } 
      // 如果响应本身就是统计对象
      else if (response.total !== undefined) {
        statsInfo = response as BannerStats
      }
      // 如果响应结构不正确，使用默认值
      else {
        console.warn('统计数据响应格式不正确:', response)
        statsInfo = { total: 0, active: 0, valid: 0, expired: 0 }
      }
    } else {
      statsInfo = { total: 0, active: 0, valid: 0, expired: 0 }
    }
    
    statsData.value.forEach(item => {
      item.value = statsInfo[item.key as keyof BannerStats] || 0
    })
  } catch (error) {
    ElMessage.error('获取统计数据失败')
    console.error('获取统计数据失败:', error)
    // 设置默认值
    statsData.value.forEach(item => {
      item.value = 0
    })
  }
}

const handleAdd = () => {
  dialogMode.value = 'add'
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row: BannerItem) => {
  dialogMode.value = 'edit'
  Object.assign(formData, row)
  dialogVisible.value = true
}

const handleDelete = async (row: BannerItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除轮播图 "${row.title}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    row.deleteLoading = true
    await BannerService.deleteBanner(row.id)
    ElMessage.success('删除成功')
    loadBannerList()
    loadStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('删除失败:', error)
    }
  } finally {
    row.deleteLoading = false
  }
}

const handleToggleStatus = async (row: BannerItem) => {
  const originalStatus = !row.is_active
  row.statusLoading = true
  try {
    await BannerService.toggleBannerStatus(row.id)
    ElMessage.success(`${row.is_active ? '启用' : '禁用'}成功`)
    loadStats()
  } catch (error) {
    row.is_active = originalStatus
    ElMessage.error('状态切换失败')
    console.error('状态切换失败:', error)
  } finally {
    row.statusLoading = false
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    if (dialogMode.value === 'add') {
      await BannerService.createBanner(formData)
      ElMessage.success('添加成功')
    } else {
      await BannerService.updateBanner(formData.id!, formData)
      ElMessage.success('更新成功')
    }
    
    dialogVisible.value = false
    loadBannerList()
    loadStats()
  } catch (error) {
    ElMessage.error(`${dialogMode.value === 'add' ? '添加' : '更新'}失败`)
    console.error(`${dialogMode.value === 'add' ? '添加' : '更新'}失败:`, error)
  } finally {
    submitLoading.value = false
  }
}

const handleDialogClose = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    title: '',
    image: '',
    link: '',
    description: '',
    alt_text: '',
    sort_order: 0,
    target: '_self',
    is_active: true,
    start_time: '',
    end_time: ''
  })
  formRef.value?.resetFields()
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

// 生命周期
onMounted(() => {
  loadBannerList()
  loadStats()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.header-actions {
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.stats-card:hover {
  transform: translateY(-2px);
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.list-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.time-range {
  font-size: 12px;
}

.time-item {
  margin-bottom: 2px;
}

.time-label {
  color: #909399;
  margin-right: 4px;
}

.time-value {
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.text-right {
  text-align: right;
}

.text-gray-400 {
  color: #909399;
}

.image-upload-section {
  width: 100%;
}

.image-uploader {
  margin-bottom: 16px;
}

.image-uploader :deep(.el-upload) {
  width: 148px;
  height: 148px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.2s;
}

.image-uploader :deep(.el-upload:hover) {
  border-color: #409eff;
}

.image-preview {
  width: 100%;
  height: 100%;
  position: relative;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  opacity: 0;
  transition: opacity 0.2s;
  gap: 4px;
}

.image-preview:hover .image-overlay {
  opacity: 1;
}

.upload-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 8px;
  color: #8c939d;
}

.upload-icon {
  font-size: 28px;
}

.upload-text {
  font-size: 14px;
  font-weight: 600;
}

.upload-tips {
  font-size: 12px;
  color: #a8abb2;
  text-align: center;
  line-height: 1.4;
  padding: 0 8px;
}

.url-input-section {
  margin-top: 16px;
}

.section-divider {
  font-size: 14px;
  color: #909399;
  text-align: center;
  margin-bottom: 12px;
  position: relative;
}

.section-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e4e7ed;
  z-index: 1;
}

.section-divider span {
  background: white;
  padding: 0 16px;
  position: relative;
  z-index: 2;
}

.upload-loading {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 8px;
  color: #8c939d;
}

.loading-text {
  font-size: 14px;
  font-weight: 600;
}
</style> 