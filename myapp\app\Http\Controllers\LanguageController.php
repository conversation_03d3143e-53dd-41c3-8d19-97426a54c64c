<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Log;

class LanguageController extends Controller
{
    /**
     * 切换语言
     * 
     * @param Request $request
     * @param string $locale
     * @return \Illuminate\Http\RedirectResponse
     */
    public function switchLanguage(Request $request, $locale)
    {
        // 验证语言代码是否支持
        $supportedLocales = ['zh_CN', 'en'];
        
        if (!in_array($locale, $supportedLocales)) {
            // 如果语言不支持，默认使用中文
            $locale = 'zh_CN';
        }
        
        try {
            // 设置应用程序的语言环境
            App::setLocale($locale);
            
            // 将语言选择保存到session中
            Session::put('locale', $locale);
            
            // 记录语言切换日志
            Log::info('语言切换成功，切换到: ' . $locale);
            
            // 获取返回的URL，如果没有则返回首页
            $returnUrl = $request->input('return_url', url()->previous());
            
            // 确保返回URL是安全的（在同一域名下）
            if (!filter_var($returnUrl, FILTER_VALIDATE_URL) || 
                parse_url($returnUrl, PHP_URL_HOST) !== parse_url(url('/'), PHP_URL_HOST)) {
                $returnUrl = route('home');
            }
            
            return redirect($returnUrl)->with('success', __('app.common.success'));
            
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('语言切换失败: ' . $e->getMessage());
            
            // 返回错误信息
            return redirect()->back()->with('error', __('app.common.error'));
        }
    }
    
    /**
     * 获取当前语言
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCurrentLanguage()
    {
        $currentLocale = App::getLocale();
        
        $languages = [
            'zh_CN' => [
                'code' => 'zh_CN',
                'name' => '中文',
                'native_name' => '中文'
            ],
            'en' => [
                'code' => 'en',
                'name' => 'English',
                'native_name' => 'English'
            ]
        ];
        
        return response()->json([
            'current' => $currentLocale,
            'current_info' => $languages[$currentLocale] ?? $languages['zh_CN'],
            'available' => $languages
        ]);
    }
    
    /**
     * 获取支持的语言列表
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSupportedLanguages()
    {
        $languages = [
            'zh_CN' => [
                'code' => 'zh_CN',
                'name' => '中文',
                'native_name' => '中文',
                'flag' => '🇨🇳'
            ],
            'en' => [
                'code' => 'en',
                'name' => 'English',
                'native_name' => 'English',
                'flag' => '🇺🇸'
            ]
        ];
        
        return response()->json([
            'languages' => $languages,
            'current' => App::getLocale()
        ]);
    }

    /**
     * 重置语言为默认中文
     * 
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function resetLanguage(Request $request)
    {
        try {
            // 清除Session中的语言设置
            Session::forget('locale');
            
            // 设置为默认中文
            App::setLocale('zh_CN');
            
            // 记录重置日志
            Log::info('语言已重置为默认中文');
            
            // 获取返回的URL，如果没有则返回首页
            $returnUrl = $request->input('return_url', url()->previous());
            
            // 确保返回URL是安全的（在同一域名下）
            if (!filter_var($returnUrl, FILTER_VALIDATE_URL) || 
                parse_url($returnUrl, PHP_URL_HOST) !== parse_url(url('/'), PHP_URL_HOST)) {
                $returnUrl = route('home');
            }
            
            return redirect($returnUrl)->with('success', '语言已重置为中文');
            
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('语言重置失败: ' . $e->getMessage());
            
            // 返回错误信息
            return redirect()->back()->with('error', '语言重置失败');
        }
    }
} 