@echo off
chcp 65001 >nul
echo ===========================================
echo 用户账号检查和修复脚本
echo ===========================================
echo.

REM 设置PHP路径
set "PHP_PATH=D:\phpstudy_pro\Extensions\php\php8.1.9nts"
set "PATH=%PHP_PATH%;%PATH%"

REM 进入项目目录
cd /d "%~dp0myapp"
echo 已进入Laravel项目目录: %CD%
echo.

echo 1. 检查数据库连接...
"%PHP_PATH%\php.exe" artisan db:show
echo.

echo 2. 检查用户表结构...
"%PHP_PATH%\php.exe" artisan tinker --execute="use Illuminate\Support\Facades\Schema; $columns = Schema::getColumnListing('users'); echo '用户表字段: ' . implode(', ', $columns);"
echo.

echo 3. 检查现有用户...
"%PHP_PATH%\php.exe" artisan tinker --execute="use App\Models\User; $users = User::all(['id', 'name', 'email', 'role', 'status']); foreach($users as $user) { echo 'ID: ' . $user->id . ', 姓名: ' . $user->name . ', 邮箱: ' . $user->email . ', 角色: ' . ($user->role ?? '无') . ', 状态: ' . ($user->status ?? '无') . PHP_EOL; }"
echo.

echo 4. 重新创建测试用户（删除已存在的）...
"%PHP_PATH%\php.exe" artisan tinker --execute="use App\Models\User; User::where('email', '<EMAIL>')->delete(); User::where('email', '<EMAIL>')->delete(); User::where('email', '<EMAIL>')->delete(); echo '已删除现有测试用户';"
echo.

echo 5. 创建新的测试用户...
"%PHP_PATH%\php.exe" artisan tinker --execute="use App\Models\User; User::create(['name' => '超级管理员', 'email' => '<EMAIL>', 'password' => bcrypt('admin123'), 'role' => 'R_SUPER', 'status' => 'active', 'email_verified_at' => now()]); echo '超级管理员创建成功';"
echo.

"%PHP_PATH%\php.exe" artisan tinker --execute="use App\Models\User; User::create(['name' => '管理员', 'email' => '<EMAIL>', 'password' => bcrypt('manager123'), 'role' => 'R_ADMIN', 'status' => 'active', 'email_verified_at' => now()]); echo '管理员创建成功';"
echo.

"%PHP_PATH%\php.exe" artisan tinker --execute="use App\Models\User; User::create(['name' => '测试用户', 'email' => '<EMAIL>', 'password' => bcrypt('user123'), 'role' => 'R_USER', 'status' => 'active', 'email_verified_at' => now()]); echo '前端用户创建成功';"
echo.

echo 6. 验证用户创建结果...
"%PHP_PATH%\php.exe" artisan tinker --execute="use App\Models\User; $users = User::whereIn('email', ['<EMAIL>', '<EMAIL>', '<EMAIL>'])->get(['name', 'email', 'role', 'status']); foreach($users as $user) { echo '姓名: ' . $user->name . ', 邮箱: ' . $user->email . ', 角色: ' . $user->role . ', 状态: ' . $user->status . PHP_EOL; }"
echo.

echo 7. 测试密码验证...
"%PHP_PATH%\php.exe" artisan tinker --execute="use App\Models\User; use Illuminate\Support\Facades\Hash; $user = User::where('email', '<EMAIL>')->first(); if($user) { $passwordCheck = Hash::check('user123', $user->password); echo '前端用户密码验证: ' . ($passwordCheck ? '成功' : '失败'); } else { echo '前端用户不存在'; }"
echo.

echo ===========================================
echo 检查完成！
echo ===========================================
echo.
echo 现在请尝试使用以下账号登录：
echo.
echo 前端登录 (http://localhost:8000/login):
echo 邮箱: <EMAIL>
echo 密码: user123
echo.
echo 后台登录 (http://localhost:8000/admin):
echo 邮箱: <EMAIL>  
echo 密码: admin123
echo.

pause 