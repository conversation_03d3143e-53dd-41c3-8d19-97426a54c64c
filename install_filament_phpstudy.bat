@echo off
chcp 65001 >nul
echo.
echo =============================================
echo   FilamentPHP管理后台安装 (PHPStudy环境)
echo =============================================
echo.

:: 设置PHPStudy的PHP路径
set PHP_PATH=D:\phpstudy_pro\Extensions\php\php8.0.2nts\php.exe

:: 检查是否在正确的目录
if not exist "myapp\artisan" (
    echo ❌ 错误: 请在Laravel项目根目录运行此脚本
    echo 当前目录: %cd%
    echo 应该包含myapp文件夹
    pause
    exit /b 1
)

echo 📁 当前目录: %cd%
echo.

:: 检查PHPStudy的PHP是否可用
echo 🔍 检查PHPStudy PHP环境...
if not exist "%PHP_PATH%" (
    echo ❌ 错误: 未找到PHPStudy PHP
    echo 路径: %PHP_PATH%
    echo 💡 请确认PHPStudy已安装且PHP版本为8.0.2
    pause
    exit /b 1
)

%PHP_PATH% -v
echo ✅ PHPStudy PHP环境检查完成
echo.

:: 进入Laravel项目目录
echo 📂 进入Laravel项目目录...
cd myapp
echo ✅ 当前目录: %cd%
echo.

:: 检查composer是否已安装
echo 🔍 检查Composer...
where composer >nul 2>&1
if errorlevel 1 (
    echo 📥 Composer未安装，开始安装...
    echo.
    
    :: 下载composer安装器
    echo 下载Composer安装器...
    %PHP_PATH% -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
    
    if not exist "composer-setup.php" (
        echo ❌ 下载Composer安装器失败
        pause
        exit /b 1
    )
    
    :: 安装composer
    echo 安装Composer...
    %PHP_PATH% composer-setup.php --install-dir=. --filename=composer.phar
    
    if not exist "composer.phar" (
        echo ❌ Composer安装失败
        del composer-setup.php 2>nul
        pause
        exit /b 1
    )
    
    :: 清理安装器
    del composer-setup.php
    echo ✅ Composer安装完成
    set COMPOSER_CMD=%PHP_PATH% composer.phar
) else (
    echo ✅ Composer已安装
    set COMPOSER_CMD=composer
)
echo.

:: 配置国内镜像 (阿里云)
echo 🚀 配置Composer国内镜像 (阿里云)...
%COMPOSER_CMD% config -g repo.packagist composer https://mirrors.aliyun.com/composer/
echo ✅ 镜像配置完成
echo.

:: 更新composer依赖
echo 📦 更新Composer依赖...
%COMPOSER_CMD% install --no-dev --optimize-autoloader
echo.

:: 安装FilamentPHP
echo 🎨 开始安装FilamentPHP管理后台...
echo.

echo 1️⃣ 安装Filament核心包...
%COMPOSER_CMD% require filament/filament:"^3.0"

if errorlevel 1 (
    echo ❌ FilamentPHP核心包安装失败
    pause
    exit /b 1
)

echo.
echo 2️⃣ 发布Filament资源...
%PHP_PATH% artisan filament:install --panels

if errorlevel 1 (
    echo ❌ Filament资源发布失败
    pause
    exit /b 1
)

echo.
echo 3️⃣ 创建管理员用户...
echo 请输入管理员信息:
%PHP_PATH% artisan make:filament-user

echo.
echo 4️⃣ 生成应用密钥 (如果需要)...
%PHP_PATH% artisan key:generate

echo.
echo 5️⃣ 运行数据库迁移...
%PHP_PATH% artisan migrate

echo.
echo 🎉 FilamentPHP安装完成！
echo.
echo ==========================================
echo        安装完成信息
echo ==========================================
echo.
echo 🌐 管理后台访问地址: http://localhost:8000/admin
echo 📱 启动开发服务器命令: 
echo    %PHP_PATH% artisan serve --host=127.0.0.1 --port=8000
echo 📚 FilamentPHP文档: https://filamentphp.com/docs
echo.
echo 💡 下一步操作:
echo    1. 运行启动命令启动服务器
echo    2. 访问: http://localhost:8000/admin
echo    3. 使用刚创建的管理员账号登录
echo.
echo ==========================================

set /p start_server="是否现在启动开发服务器? (y/n): "
if /i "%start_server%"=="y" (
    echo.
    echo 🚀 启动Laravel开发服务器...
    echo 💡 按 Ctrl+C 停止服务器
    echo.
    %PHP_PATH% artisan serve --host=127.0.0.1 --port=8000
) else (
    echo.
    echo 💡 稍后可以运行以下命令启动服务器:
    echo    cd myapp
    echo    %PHP_PATH% artisan serve --host=127.0.0.1 --port=8000
)

echo.
echo 📴 安装程序结束
pause 