<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>包包订阅服务</title>
    <script src="https://cdn.tailwindcss.com/3.3.3"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css">
    <style>
        body {
            font-family: 'Comic Sans MS', cursive, sans-serif;
            background-color: #fff9f2;
        }
        .underline-orange {
            position: relative;
            display: inline-block;
        }
        .underline-orange::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: #ff8c42;
            border-radius: 3px;
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(255, 140, 66, 0.2);
        }
        .btn-hover {
            transition: all 0.3s ease;
        }
        .btn-hover:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(255, 140, 66, 0.3);
        }
        .carousel-slide {
            transition: opacity 0.5s ease-in-out;
        }
        .carousel-slide.active {
            opacity: 1;
        }
        .carousel-slide:not(.active) {
            opacity: 0;
            position: absolute;
            top: 0;
            left: 0;
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- 顶部轮播图 -->
    <div class="relative h-64 md:h-96 overflow-hidden rounded-b-3xl shadow-md">
        <div class="relative h-full w-full">
            <div class="carousel-slide active h-full w-full">
                <img src="https://space.coze.cn/s/cjbWHVDgkoc/?width_heigth=1080x1920" alt="房间场景" class="w-full h-full object-cover">
            </div>
            <div class="carousel-slide h-full w-full">
                <img src="https://space.coze.cn/s/eXBh-6amRO0/?width_heigth=1920x2208" alt="森林场景" class="w-full h-full object-cover">
            </div>
        </div>
        <button class="absolute top-4 right-4 bg-white text-orange-500 px-4 py-2 rounded-full shadow-md btn-hover">
            <i class="fas fa-headset mr-2"></i>Contact Customer Service
        </button>
    </div>

    <!-- 功能按钮区 -->
    <div class="container mx-auto px-4 py-8 flex flex-col md:flex-row justify-center gap-6">
        <button class="bg-orange-500 text-white px-6 py-3 rounded-xl shadow-md flex flex-col items-center btn-hover">
            <i class="fas fa-crown text-2xl mb-2"></i>
            <span>Subscription</span>
        </button>
        <button class="bg-orange-500 text-white px-6 py-3 rounded-xl shadow-md flex flex-col items-center btn-hover">
            <i class="fas fa-user text-2xl mb-2"></i>
            <span>Log In</span>
        </button>
        <button class="bg-orange-500 text-white px-6 py-3 rounded-xl shadow-md flex flex-col items-center btn-hover">
            <i class="fas fa-shopping-bag text-2xl mb-2"></i>
            <span>New Arrivals</span>
        </button>
    </div>

    <!-- About Us 部分 -->
    <div class="container mx-auto px-4 py-8 max-w-4xl">
        <h2 class="text-3xl font-bold text-center mb-6">
            <span class="underline-orange">About Us</span>
        </h2>
        <p class="text-gray-700 text-center mb-6">
            提供H-family包包的新品到货提醒，支持超过30种包包类型，包括Picotin、Lindy、Evelyne、Herbag、Garden Party等，以及KellytoGO、ConstantinoGO和Redoe等小型皮具的新品到货通知。
        </p>
        <p class="text-gray-700 text-center">
            支持的地区有16个国家和地区，包括美国、英国、法国、德国、日本等。提醒方式为电子邮件。
        </p>
    </div>

    <!-- 订阅套餐 -->
    <div class="container mx-auto px-4 py-8">
        <h2 class="text-3xl font-bold text-center mb-8">Subscription Plans</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto">
            <!-- 月卡 -->
            <div class="bg-white p-6 rounded-2xl shadow-md card-hover">
                <h3 class="text-xl font-bold text-center mb-4">Monthly</h3>
                <div class="text-orange-500 text-3xl font-bold text-center mb-4">$30 USD</div>
                <p class="text-gray-600 text-center mb-4">31 days</p>
                <button class="w-full bg-orange-500 text-white py-2 rounded-lg btn-hover">Subscribe</button>
            </div>
            
            <!-- 季卡 -->
            <div class="bg-white p-6 rounded-2xl shadow-md card-hover">
                <h3 class="text-xl font-bold text-center mb-4">Quarterly</h3>
                <div class="text-orange-500 text-3xl font-bold text-center mb-4">$81 USD</div>
                <p class="text-gray-600 text-center mb-4">93 days</p>
                <button class="w-full bg-orange-500 text-white py-2 rounded-lg btn-hover">Subscribe</button>
            </div>
            
            <!-- 半年卡 -->
            <div class="bg-white p-6 rounded-2xl shadow-md card-hover">
                <h3 class="text-xl font-bold text-center mb-4">Semi-Annual</h3>
                <div class="text-orange-500 text-3xl font-bold text-center mb-4">$142 USD</div>
                <p class="text-gray-600 text-center mb-4">186 days</p>
                <button class="w-full bg-orange-500 text-white py-2 rounded-lg btn-hover">Subscribe</button>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-white py-6 mt-8">
        <div class="container mx-auto px-4 text-center">
            <div class="flex justify-center gap-4 mb-4">
                <a href="#" class="text-orange-500 hover:text-orange-700">Terms of Service</a>
                <span>|</span>
                <a href="#" class="text-orange-500 hover:text-orange-700">Privacy Policy</a>
            </div>
            <p class="text-gray-500 text-sm">created by <a href="https://space.coze.cn" class="text-orange-500 hover:underline">coze space</a></p>
            <p class="text-gray-400 text-xs mt-2">页面内容均由 AI 生成，仅供参考</p>
        </div>
    </footer>

    <script>
        // 轮播图自动切换
        document.addEventListener('DOMContentLoaded', function() {
            const slides = document.querySelectorAll('.carousel-slide');
            let currentSlide = 0;
            
            function showSlide(index) {
                slides.forEach(slide => slide.classList.remove('active'));
                slides[index].classList.add('active');
            }
            
            function nextSlide() {
                currentSlide = (currentSlide + 1) % slides.length;
                showSlide(currentSlide);
            }
            
            setInterval(nextSlide, 5000);
            
            // 平滑滚动
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });
        });
    </script>
</body>
</html>