# Art Design Pro + Laravel 后端测试指南

## 🚀 服务器状态

确保两个服务器都在运行：
- **Art Design Pro前端**: http://localhost:3006
- **Laravel后端**: http://localhost:8000

## 📋 测试步骤

### 1. 创建测试用户

在myapp项目的数据库中，手动插入一个测试用户：

```sql
INSERT INTO `users` (
    `name`, 
    `email`, 
    `email_verified_at`, 
    `password`, 
    `status`, 
    `created_at`, 
    `updated_at`
) VALUES (
    'Admin', 
    '<EMAIL>', 
    NOW(), 
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password
    'active', 
    NOW(), 
    NOW()
);
```

默认密码是: `password`

### 2. 创建轮播图测试数据

```sql
INSERT INTO `banners` (
    `title`, 
    `image`, 
    `link`, 
    `description`, 
    `is_active`, 
    `sort_order`, 
    `target`, 
    `alt_text`, 
    `created_at`, 
    `updated_at`
) VALUES 
('欢迎使用Art Design Pro', 'https://picsum.photos/1200/400?random=1', 'https://www.lingchen.kim/art-design-pro', 'Art Design Pro - 一个美观实用的Vue3管理后台模板', 1, 1, '_blank', 'Art Design Pro Banner', NOW(), NOW()),
('功能丰富的管理系统', 'https://picsum.photos/1200/400?random=2', '#', '包含用户管理、权限控制、数据统计等完整功能', 1, 2, '_self', '功能介绍', NOW(), NOW()),
('响应式设计', 'https://picsum.photos/1200/400?random=3', '#', '完美适配各种设备尺寸，提供最佳用户体验', 1, 3, '_self', '响应式设计', NOW(), NOW());
```

### 3. 登录测试

1. 打开浏览器访问: http://localhost:3006
2. 在登录页面输入：
   - 用户名: `<EMAIL>`
   - 密码: `password`
3. 点击登录按钮
4. 如果登录成功，应该跳转到管理后台首页

### 4. 轮播图管理测试

登录成功后：

1. 在左侧菜单中找到"轮播图管理"菜单
2. 点击"轮播图列表"
3. 应该能看到轮播图列表页面，包含：
   - 统计卡片（总数、激活、有效、过期）
   - 轮播图列表表格
   - 添加轮播图按钮

#### 测试功能：

**查看轮播图列表:**
- 应该显示之前创建的测试数据
- 可以看到图片预览、标题、描述等信息

**添加轮播图:**
1. 点击"添加轮播图"按钮
2. 填写表单：
   - 标题: 测试轮播图
   - 图片URL: https://picsum.photos/1200/400?random=5
   - 链接: https://example.com
   - 描述: 这是一个测试轮播图
   - 排序: 5
   - 打开方式: 新窗口
   - 启用状态: 开启
3. 点击"添加"按钮
4. 应该显示成功消息并刷新列表

**编辑轮播图:**
1. 点击列表中的"编辑"按钮
2. 修改任意字段
3. 点击"更新"按钮
4. 应该显示成功消息并更新列表

**切换状态:**
1. 点击列表中的状态开关
2. 应该显示成功消息
3. 统计数据应该更新

**删除轮播图:**
1. 点击列表中的"删除"按钮
2. 确认删除操作
3. 应该显示成功消息并从列表中移除

### 5. API接口测试

您也可以直接测试API接口：

#### 登录接口
```bash
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "<EMAIL>",
    "password": "password"
  }'
```

#### 获取轮播图列表
```bash
curl -X GET http://localhost:8000/api/banners \
  -H "Content-Type: application/json"
```

#### 获取轮播图统计
```bash
curl -X GET http://localhost:8000/api/banners/stats \
  -H "Content-Type: application/json"
```

## 🔧 常见问题

### 1. 登录失败
- 检查用户是否存在且状态为 'active'
- 检查密码是否正确
- 查看浏览器开发者工具的网络请求

### 2. 轮播图数据不显示
- 检查Laravel服务器是否正常运行
- 检查API URL配置是否正确
- 查看浏览器控制台是否有错误

### 3. CORS错误
Laravel项目可能需要配置CORS，在 `config/cors.php` 中确保允许前端域名访问。

### 4. 数据库连接问题
检查 `myapp/.env` 文件中的数据库配置是否正确。

## 📝 预期结果

如果一切正常，您应该能够：
1. ✅ 成功登录管理后台
2. ✅ 查看轮播图统计数据
3. ✅ 查看轮播图列表
4. ✅ 添加新的轮播图
5. ✅ 编辑现有轮播图
6. ✅ 切换轮播图状态
7. ✅ 删除轮播图

恭喜！您已经成功完成了Art Design Pro与Laravel后端的对接！ 