import request from '@/utils/http'

export class UserService {
  // 登录
  static login(params: Api.Auth.LoginParams) {
    return request.post<Api.Auth.LoginResponse>({
      url: '/api/auth/login',
      data: params
    })
  }

  // 登出
  static logout() {
    return request.post<any>({
      url: '/api/auth/logout'
    })
  }

  // 获取用户信息
  static getUserInfo() {
    return request.get<Api.User.UserInfo>({
      url: '/api/user/info'
    })
  }

  // 获取用户列表
  static getUserList(params: Api.Common.PaginatingSearchParams) {
    return request.get<Api.User.UserListData>({
      url: '/api/user/list',
      params
    })
  }

  // 刷新Token
  static refreshToken() {
    return request.post<Api.Auth.RefreshTokenResponse>({
      url: '/api/auth/refresh'
    })
  }
}
