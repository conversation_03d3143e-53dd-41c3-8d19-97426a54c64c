# 🔐 系统登录账号信息

## 📋 账号概览

系统已经为您创建了3个测试账号，分别对应不同的用户角色：

---

## 🔴 【后台管理系统】
**访问地址**: http://localhost:8000/admin

### 1. 超级管理员账号
- **邮箱**: `<EMAIL>`
- **密码**: `admin123`
- **角色**: R_SUPER (超级管理员)
- **权限**: 最高权限，可以管理所有用户和系统设置

### 2. 普通管理员账号  
- **邮箱**: `<EMAIL>`
- **密码**: `manager123`
- **角色**: R_ADMIN (管理员)
- **权限**: 可以管理编辑者和普通用户

---

## 🔵 【前端用户系统】
**访问地址**: http://localhost:8000/login

### 3. 前端测试用户
- **邮箱**: `<EMAIL>`
- **密码**: `user123`
- **角色**: R_USER (前端用户)
- **权限**: 只能访问前端订阅功能

---

## 🎯 测试流程

### 测试后台管理系统
1. 访问 http://localhost:8000/admin
2. 使用超级管理员账号登录：
   - 邮箱：<EMAIL>
   - 密码：admin123
3. 在后台可以管理用户、商品、轮番图等

### 测试前端用户系统
1. 访问 http://localhost:8000/login
2. 使用前端用户账号登录：
   - 邮箱：<EMAIL>
   - 密码：user123
3. 在前端可以浏览商品、订阅服务等

### 测试角色分离功能
1. 用前端用户登录后尝试访问 `/admin` - 应该被重定向到首页
2. 用管理员账号登录后尝试访问订阅功能 - 应该被重定向到后台

---

## 🚀 快速启动

如果需要重新启动服务器，请运行：
```bash
.\setup_php_simple.bat
```

或者手动启动：
```bash
D:\phpstudy_pro\Extensions\php\php8.1.9nts\php.exe artisan serve
```

---

## 📊 角色权限对比

| 角色 | 代码 | 前端访问 | 后台访问 | 用户管理 |
|------|------|----------|----------|----------|
| 超级管理员 | R_SUPER | ❌ | ✅ | 全部用户 |
| 管理员 | R_ADMIN | ❌ | ✅ | 编辑者+用户 |
| 编辑者 | R_EDITOR | ❌ | ✅ | 仅普通用户 |
| 前端用户 | R_USER | ✅ | ❌ | 无 |

---

## 🔧 常见问题

### Q: 忘记密码怎么办？
A: 可以通过以下命令重置密码：
```bash
php artisan tinker
User::where('email', '<EMAIL>')->update(['password' => bcrypt('新密码')]);
```

### Q: 如何创建新用户？
A: 在后台管理系统 `/admin` 中的"用户管理"模块创建

### Q: 如何修改用户角色？
A: 在后台管理系统中编辑用户，选择对应的角色

---

## 📝 重要提示

⚠️ **生产环境注意事项**：
- 这些是测试账号，生产环境请修改密码
- 建议使用强密码和双因素认证
- 定期审查用户权限和角色设置 