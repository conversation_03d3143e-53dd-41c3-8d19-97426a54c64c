<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id'); // 用户ID
            $table->unsignedBigInteger('plan_id'); // 套餐ID
            $table->unsignedBigInteger('region_id'); // 地区ID
            $table->string('subscription_id')->unique(); // 订阅唯一标识
            $table->enum('status', ['active', 'expired', 'cancelled', 'pending'])->default('pending'); // 订阅状态
            $table->decimal('amount_paid', 10, 2); // 支付金额
            $table->string('currency', 3)->default('USD'); // 货币
            $table->timestamp('starts_at'); // 开始时间
            $table->timestamp('expires_at')->nullable(); // 到期时间
            $table->timestamp('cancelled_at')->nullable(); // 取消时间
            $table->string('paypal_subscription_id')->nullable(); // PayPal订阅ID
            $table->json('metadata')->nullable(); // 额外数据
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('plan_id')->references('id')->on('plans');
            $table->foreign('region_id')->references('id')->on('regions');
            $table->index(['user_id', 'status']);
            $table->index(['status', 'expires_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('subscriptions');
    }
};
