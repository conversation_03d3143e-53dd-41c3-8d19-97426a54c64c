# 商品管理API测试指南

## 概述

已成功为后台管理系统实现完整的商品管理功能，包括前端Vue组件和后端Laravel API接口。

## 项目结构

### 后端 (Laravel)
- **主控制器**: `myapp/app/Http/Controllers/Api/ProductController.php`
- **认证控制器**: `myapp/app/Http/Controllers/Api/AuthController.php`
- **路由配置**: `myapp/routes/api.php`

### 前端 (Vue3 + TypeScript)
- **主页面**: `art-design-pro/src/views/product/index.vue`
- **API服务**: `art-design-pro/src/api/productApi.ts`
- **路由配置**: `art-design-pro/src/router/routes/asyncRoutes.ts`
- **路由别名**: `art-design-pro/src/router/routesAlias.ts`

## API接口列表

### 认证接口

#### 1. 用户登录
```
POST http://127.0.0.1:8002/api/auth/login
Content-Type: application/json

{
    "userName": "<EMAIL>",
    "password": "123456"
}
```

**测试账号**:
- `<EMAIL>` / `123456` (超级管理员)
- `<EMAIL>` / `123456` (管理员)
- `<EMAIL>` / `123456` (编辑员)
- `<EMAIL>` / `123456` (普通用户)
- `admin` / `123456` (管理员)

#### 2. 获取用户信息
```
GET http://127.0.0.1:8002/api/user/info
Authorization: Bearer {token}
```

### 商品管理接口

#### 1. 获取商品统计
```
GET http://127.0.0.1:8002/api/products/stats
```

#### 2. 获取商品选项数据
```
GET http://127.0.0.1:8002/api/products/options
```

#### 3. 获取商品列表 (需要认证)
```
GET http://127.0.0.1:8002/api/admin/products
Authorization: Bearer {token}

查询参数:
- page: 页码 (默认1)
- per_page: 每页数量 (默认15)
- search: 搜索关键词
- category: 分类过滤
- brand: 品牌过滤
- region_id: 地区过滤
- is_active: 状态过滤 (1=上架, 0=下架)
- stock_status: 库存状态过滤
- sort_by: 排序字段
- sort_direction: 排序方向 (asc/desc)
```

#### 4. 创建商品 (需要认证)
```
POST http://127.0.0.1:8002/api/admin/products
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "新商品名称",
    "sku": "SKU001",
    "description": "商品描述",
    "brand": "品牌名称",
    "category": "分类",
    "price": 999.00,
    "original_price": 1299.00,
    "currency": "CNY",
    "stock_quantity": 100,
    "stock_status": "in_stock",
    "image_url": "https://example.com/image.jpg",
    "product_url": "https://example.com/product",
    "region_id": 1,
    "is_active": true
}
```

#### 5. 更新商品 (需要认证)
```
PUT http://127.0.0.1:8002/api/admin/products/{id}
Authorization: Bearer {token}
Content-Type: application/json

{同创建商品的参数}
```

#### 6. 删除商品 (需要认证)
```
DELETE http://127.0.0.1:8002/api/admin/products/{id}
Authorization: Bearer {token}
```

#### 7. 切换商品状态 (需要认证)
```
PUT http://127.0.0.1:8002/api/admin/products/{id}/toggle
Authorization: Bearer {token}
```

#### 8. 批量操作商品 (需要认证)
```
POST http://127.0.0.1:8002/api/admin/products/batch
Authorization: Bearer {token}
Content-Type: application/json

{
    "action": "delete|activate|deactivate|update_stock_status",
    "ids": [1, 2, 3],
    "stock_status": "in_stock" // 仅当action为update_stock_status时需要
}
```

## 前端功能特性

### 商品列表页面
- ✅ 统计面板显示 (总数、上架、下架、缺货)
- ✅ 高级搜索和过滤
- ✅ 分页显示
- ✅ 商品状态切换 (上架/下架)
- ✅ 批量操作 (删除、上架、下架、更新库存状态)
- ✅ 添加新商品
- ✅ 编辑商品信息
- ✅ 删除商品

### 表单功能
- ✅ 完整的商品信息表单
- ✅ 数据验证
- ✅ 图片预览
- ✅ 地区选择
- ✅ 库存状态管理
- ✅ 商品属性JSON编辑

### 用户体验
- ✅ 响应式设计
- ✅ 加载状态提示
- ✅ 错误处理
- ✅ 成功反馈
- ✅ 确认对话框

## 数据结构

### 商品数据示例
```json
{
    "id": 1,
    "name": "iPhone 15 Pro",
    "sku": "IPHONE15PRO001",
    "description": "苹果iPhone 15 Pro 256GB 深空黑色",
    "brand": "Apple",
    "category": "手机",
    "price": 8999.00,
    "original_price": 9999.00,
    "currency": "CNY",
    "stock_quantity": 50,
    "stock_status": "in_stock",
    "stock_status_text": "有库存",
    "image_url": "https://example.com/iphone15pro.jpg",
    "product_url": "https://www.apple.com.cn/iphone-15-pro/",
    "region": {
        "id": 1,
        "name": "中国",
        "code": "CN"
    },
    "is_active": true,
    "status_text": "上架",
    "followers_count": 0,
    "last_updated_at": "2025-06-21 14:25:00",
    "created_at": "2025-06-21 14:25:00",
    "updated_at": "2025-06-21 14:25:00"
}
```

## 权限控制

### 角色权限
- **R_SUPER**: 超级管理员，拥有所有权限
- **R_ADMIN**: 管理员，可以管理商品和用户
- **R_EDITOR**: 编辑员，可以编辑商品信息
- **R_USER**: 普通用户，只能查看

### 菜单权限
商品管理菜单仅对 `R_SUPER` 和 `R_ADMIN` 角色可见。

## 技术特点

### 后端特性
- 模拟数据响应，不依赖数据库连接
- 完整的数据验证
- 详细的操作日志
- RESTful API设计
- 统一的响应格式

### 前端特性
- Vue 3 + TypeScript + Element Plus
- 响应式设计，适配不同屏幕
- 组件化开发
- 统一的API服务层
- 完善的错误处理

## 使用说明

1. **启动后端服务**:
   ```bash
   cd myapp
   php artisan serve --host=127.0.0.1 --port=8002
   ```

2. **启动前端服务**:
   ```bash
   cd art-design-pro
   pnpm dev
   ```

3. **访问系统**:
   - 前端地址: http://localhost:3006
   - 使用测试账号登录
   - 导航到商品管理页面

## 注意事项

- 当前使用模拟数据，生产环境需要连接真实数据库
- 需要配置正确的数据库连接信息
- 建议安装PHP的pdo_mysql扩展以支持MySQL数据库
- 前端需要确保API接口地址正确配置

## 后续开发建议

1. **数据库集成**: 连接真实MySQL数据库
2. **文件上传**: 实现商品图片上传功能
3. **数据导入**: 支持Excel批量导入商品
4. **数据导出**: 支持商品数据导出
5. **审核流程**: 添加商品审核机制
6. **版本控制**: 商品信息变更历史记录 