<template>
  <div class="app-container">
    <!-- 头部操作栏 -->
    <div class="header-actions">
      <el-row :gutter="16" align="middle">
        <el-col :span="12">
          <h2 class="page-title">用户设置</h2>
        </el-col>
        <el-col :span="12" class="text-right">
          <el-button type="primary" @click="handleSaveAll" :loading="saveLoading" :icon="Check">
            保存所有设置
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 设置选项卡 -->
    <el-tabs v-model="activeTab" class="settings-tabs">
      <!-- 基础设置 -->
      <el-tab-pane label="基础设置" name="basic">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <span>基础设置</span>
            </div>
          </template>
          <el-form :model="basicSettings" :rules="basicRules" ref="basicFormRef" label-width="120px">
            <el-form-item label="默认分页大小" prop="default_page_size">
              <el-input-number 
                v-model="basicSettings.default_page_size" 
                :min="10" 
                :max="100" 
                :step="10"
                placeholder="默认分页大小"
              />
              <div class="form-help">设置系统列表页面的默认分页大小</div>
            </el-form-item>
            <el-form-item label="会话超时时间" prop="session_timeout">
              <el-select v-model="basicSettings.session_timeout" placeholder="请选择会话超时时间">
                <el-option label="30分钟" value="30" />
                <el-option label="1小时" value="60" />
                <el-option label="2小时" value="120" />
                <el-option label="4小时" value="240" />
                <el-option label="8小时" value="480" />
              </el-select>
              <div class="form-help">用户无操作后自动退出的时间（分钟）</div>
            </el-form-item>
            <el-form-item label="密码复杂度" prop="password_complexity">
              <el-checkbox-group v-model="basicSettings.password_complexity">
                <el-checkbox label="uppercase">至少包含一个大写字母</el-checkbox>
                <el-checkbox label="lowercase">至少包含一个小写字母</el-checkbox>
                <el-checkbox label="number">至少包含一个数字</el-checkbox>
                <el-checkbox label="special">至少包含一个特殊字符</el-checkbox>
              </el-checkbox-group>
              <div class="form-help">设置新用户密码的复杂度要求</div>
            </el-form-item>
            <el-form-item label="密码最小长度" prop="password_min_length">
              <el-input-number 
                v-model="basicSettings.password_min_length" 
                :min="6" 
                :max="20"
                placeholder="密码最小长度"
              />
              <div class="form-help">用户密码的最小长度要求</div>
            </el-form-item>
            <el-form-item label="允许重复登录" prop="allow_multiple_login">
              <el-switch v-model="basicSettings.allow_multiple_login" />
              <div class="form-help">是否允许同一用户在多个设备同时登录</div>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSaveBasic" :loading="basicLoading">
                保存基础设置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 安全设置 -->
      <el-tab-pane label="安全设置" name="security">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <span>安全设置</span>
            </div>
          </template>
          <el-form :model="securitySettings" :rules="securityRules" ref="securityFormRef" label-width="120px">
            <el-form-item label="登录失败限制" prop="login_attempts">
              <el-input-number 
                v-model="securitySettings.login_attempts" 
                :min="3" 
                :max="10"
                placeholder="登录失败次数限制"
              />
              <div class="form-help">连续登录失败次数达到限制后锁定账户</div>
            </el-form-item>
            <el-form-item label="账户锁定时间" prop="lockout_duration">
              <el-select v-model="securitySettings.lockout_duration" placeholder="请选择锁定时间">
                <el-option label="15分钟" value="15" />
                <el-option label="30分钟" value="30" />
                <el-option label="1小时" value="60" />
                <el-option label="2小时" value="120" />
                <el-option label="24小时" value="1440" />
              </el-select>
              <div class="form-help">账户被锁定后的解锁时间（分钟）</div>
            </el-form-item>
            <el-form-item label="IP白名单" prop="ip_whitelist">
              <el-input 
                v-model="securitySettings.ip_whitelist" 
                type="textarea" 
                :rows="3"
                placeholder="每行一个IP地址或IP段，如：***********/24"
              />
              <div class="form-help">只允许白名单中的IP地址访问系统，留空则不限制</div>
            </el-form-item>
            <el-form-item label="启用两步验证" prop="enable_2fa">
              <el-switch v-model="securitySettings.enable_2fa" />
              <div class="form-help">强制用户启用两步验证</div>
            </el-form-item>
            <el-form-item label="API访问限制" prop="api_rate_limit">
              <el-input-number 
                v-model="securitySettings.api_rate_limit" 
                :min="100" 
                :max="10000"
                placeholder="每分钟API调用次数限制"
              />
              <div class="form-help">每个用户每分钟的API调用次数限制</div>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSaveSecurity" :loading="securityLoading">
                保存安全设置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 通知设置 -->
      <el-tab-pane label="通知设置" name="notification">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <span>通知设置</span>
            </div>
          </template>
          <el-form :model="notificationSettings" ref="notificationFormRef" label-width="120px">
            <el-form-item label="邮件通知" prop="email_notifications">
              <el-checkbox-group v-model="notificationSettings.email_notifications">
                <el-checkbox label="user_login">用户登录通知</el-checkbox>
                <el-checkbox label="password_change">密码修改通知</el-checkbox>
                <el-checkbox label="account_locked">账户锁定通知</el-checkbox>
                <el-checkbox label="system_error">系统错误通知</el-checkbox>
                <el-checkbox label="security_alert">安全警报通知</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="短信通知" prop="sms_notifications">
              <el-checkbox-group v-model="notificationSettings.sms_notifications">
                <el-checkbox label="critical_error">严重错误通知</el-checkbox>
                <el-checkbox label="security_breach">安全漏洞通知</el-checkbox>
                <el-checkbox label="system_maintenance">系统维护通知</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="通知邮箱" prop="notification_email">
              <el-input v-model="notificationSettings.notification_email" placeholder="接收通知的邮箱地址" />
              <div class="form-help">系统通知将发送到此邮箱</div>
            </el-form-item>
            <el-form-item label="通知手机" prop="notification_phone">
              <el-input v-model="notificationSettings.notification_phone" placeholder="接收短信通知的手机号码" />
              <div class="form-help">紧急通知将发送到此手机</div>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSaveNotification" :loading="notificationLoading">
                保存通知设置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 系统维护 -->
      <el-tab-pane label="系统维护" name="maintenance">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <span>系统维护</span>
            </div>
          </template>
          <div class="maintenance-actions">
            <el-row :gutter="16">
              <el-col :span="12">
                <el-card class="action-card">
                  <h4>缓存管理</h4>
                  <p>清除系统缓存，提升系统性能</p>
                  <el-button type="warning" @click="handleClearCache" :loading="cacheLoading">
                    清除缓存
                  </el-button>
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-card class="action-card">
                  <h4>日志清理</h4>
                  <p>清理30天前的系统日志</p>
                  <el-button type="danger" @click="handleCleanLogs" :loading="logLoading">
                    清理日志
                  </el-button>
                </el-card>
              </el-col>
            </el-row>
            <el-row :gutter="16" style="margin-top: 16px;">
              <el-col :span="12">
                <el-card class="action-card">
                  <h4>数据备份</h4>
                  <p>备份重要的系统数据</p>
                  <el-button type="primary" @click="handleBackupData" :loading="backupLoading">
                    立即备份
                  </el-button>
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-card class="action-card">
                  <h4>系统检查</h4>
                  <p>检查系统状态和性能</p>
                  <el-button type="info" @click="handleSystemCheck" :loading="checkLoading">
                    系统检查
                  </el-button>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check } from '@element-plus/icons-vue'

// 定义组件名称
defineOptions({
  name: 'UserSettings'
})

// 响应式数据
const activeTab = ref('basic')
const saveLoading = ref(false)
const basicLoading = ref(false)
const securityLoading = ref(false)
const notificationLoading = ref(false)
const cacheLoading = ref(false)
const logLoading = ref(false)
const backupLoading = ref(false)
const checkLoading = ref(false)

// 表单引用
const basicFormRef = ref()
const securityFormRef = ref()
const notificationFormRef = ref()

// 基础设置
const basicSettings = reactive({
  default_page_size: 20,
  session_timeout: '60',
  password_complexity: ['lowercase', 'number'],
  password_min_length: 8,
  allow_multiple_login: false
})

const basicRules = {
  default_page_size: [
    { required: true, message: '请输入默认分页大小', trigger: 'blur' }
  ],
  session_timeout: [
    { required: true, message: '请选择会话超时时间', trigger: 'change' }
  ],
  password_min_length: [
    { required: true, message: '请输入密码最小长度', trigger: 'blur' }
  ]
}

// 安全设置
const securitySettings = reactive({
  login_attempts: 5,
  lockout_duration: '30',
  ip_whitelist: '',
  enable_2fa: false,
  api_rate_limit: 1000
})

const securityRules = {
  login_attempts: [
    { required: true, message: '请输入登录失败次数限制', trigger: 'blur' }
  ],
  lockout_duration: [
    { required: true, message: '请选择锁定时间', trigger: 'change' }
  ],
  api_rate_limit: [
    { required: true, message: '请输入API调用次数限制', trigger: 'blur' }
  ]
}

// 通知设置
const notificationSettings = reactive({
  email_notifications: ['password_change', 'account_locked', 'system_error'],
  sms_notifications: ['critical_error', 'security_breach'],
  notification_email: '<EMAIL>',
  notification_phone: ''
})

// 方法
const loadSettings = async () => {
  try {
    // 模拟加载设置数据
    console.log('加载设置数据...')
  } catch (error) {
    ElMessage.error('加载设置失败')
  }
}

const handleSaveBasic = async () => {
  try {
    await basicFormRef.value.validate()
    basicLoading.value = true
    
    // 模拟保存
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('基础设置保存成功')
  } catch (error) {
    ElMessage.error('基础设置保存失败')
  } finally {
    basicLoading.value = false
  }
}

const handleSaveSecurity = async () => {
  try {
    await securityFormRef.value.validate()
    securityLoading.value = true
    
    // 模拟保存
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('安全设置保存成功')
  } catch (error) {
    ElMessage.error('安全设置保存失败')
  } finally {
    securityLoading.value = false
  }
}

const handleSaveNotification = async () => {
  try {
    notificationLoading.value = true
    
    // 模拟保存
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('通知设置保存成功')
  } catch (error) {
    ElMessage.error('通知设置保存失败')
  } finally {
    notificationLoading.value = false
  }
}

const handleSaveAll = async () => {
  try {
    saveLoading.value = true
    
    // 保存所有设置
    await Promise.all([
      handleSaveBasic(),
      handleSaveSecurity(),
      handleSaveNotification()
    ])
    
    ElMessage.success('所有设置保存成功')
  } catch (error) {
    ElMessage.error('保存设置失败')
  } finally {
    saveLoading.value = false
  }
}

// 系统维护操作
const handleClearCache = async () => {
  try {
    await ElMessageBox.confirm('确定要清除系统缓存吗？', '确认清除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    cacheLoading.value = true
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('缓存清除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('缓存清除失败')
    }
  } finally {
    cacheLoading.value = false
  }
}

const handleCleanLogs = async () => {
  try {
    await ElMessageBox.confirm('确定要清理30天前的日志吗？此操作不可逆！', '确认清理', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    logLoading.value = true
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    ElMessage.success('日志清理成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('日志清理失败')
    }
  } finally {
    logLoading.value = false
  }
}

const handleBackupData = async () => {
  try {
    backupLoading.value = true
    await new Promise(resolve => setTimeout(resolve, 5000))
    
    ElMessage.success('数据备份成功')
  } catch (error) {
    ElMessage.error('数据备份失败')
  } finally {
    backupLoading.value = false
  }
}

const handleSystemCheck = async () => {
  try {
    checkLoading.value = true
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    ElMessage.success('系统检查完成，一切正常')
  } catch (error) {
    ElMessage.error('系统检查失败')
  } finally {
    checkLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.header-actions {
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.text-right {
  text-align: right;
}

.settings-tabs {
  margin-bottom: 20px;
}

.settings-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-help {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.maintenance-actions {
  padding: 20px;
}

.action-card {
  text-align: center;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  transition: all 0.3s;
}

.action-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.action-card h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.action-card p {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 14px;
}
</style> 