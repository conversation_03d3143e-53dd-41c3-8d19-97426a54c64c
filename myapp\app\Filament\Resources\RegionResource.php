<?php

namespace App\Filament\Resources;

use App\Filament\Resources\RegionResource\Pages;
use App\Filament\Resources\RegionResource\RelationManagers;
use App\Models\Region;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class RegionResource extends Resource
{
    protected static ?string $model = Region::class;

    protected static ?string $navigationIcon = 'heroicon-o-globe';

    protected static ?string $navigationLabel = '地区管理';

    protected static ?string $modelLabel = '地区';

    protected static ?string $pluralModelLabel = '地区';

    protected static ?string $navigationGroup = '基础设置';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('基本信息')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('地区名称')
                            ->required()
                            ->maxLength(255),
                        
                        Forms\Components\TextInput::make('code')
                            ->label('地区代码')
                            ->required()
                            ->maxLength(50)
                            ->unique(ignoreRecord: true),
                        
                        Forms\Components\Select::make('type')
                            ->label('地区类型')
                            ->options([
                                'country' => '国家',
                                'state' => '省/州',
                                'city' => '城市',
                                'district' => '区/县',
                            ])
                            ->required(),
                        
                        Forms\Components\Select::make('parent_id')
                            ->label('上级地区')
                            ->relationship('parent', 'name')
                            ->placeholder('选择上级地区（留空为顶级）'),
                        
                        Forms\Components\Toggle::make('is_active')
                            ->label('启用状态')
                            ->default(true),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('其他设置')
                    ->schema([
                        Forms\Components\TextInput::make('sort_order')
                            ->label('排序')
                            ->numeric()
                            ->default(0),
                        
                        Forms\Components\Textarea::make('description')
                            ->label('描述')
                            ->maxLength(500)
                            ->rows(3),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('地区名称')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('code')
                    ->label('地区代码')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('parent.name')
                    ->label('上级地区')
                    ->placeholder('顶级地区')
                    ->sortable(),
                
                Tables\Columns\IconColumn::make('is_active')
                    ->label('状态')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                
                Tables\Columns\TextColumn::make('sort_order')
                    ->label('排序')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('children_count')
                    ->label('子地区数')
                    ->counts('children')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('启用状态')
                    ->placeholder('全部')
                    ->trueLabel('已启用')
                    ->falseLabel('已禁用'),
                
                Tables\Filters\SelectFilter::make('parent_id')
                    ->label('上级地区')
                    ->relationship('parent', 'name')
                    ->placeholder('全部'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()->label('查看'),
                Tables\Actions\EditAction::make()->label('编辑'),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make()->label('批量删除'),
            ])
            ->defaultSort('sort_order', 'asc');
    }
    
    public static function getRelations(): array
    {
        return [
            //
        ];
    }
    
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRegions::route('/'),
            'create' => Pages\CreateRegion::route('/create'),
            'view' => Pages\ViewRegion::route('/{record}'),
            'edit' => Pages\EditRegion::route('/{record}/edit'),
        ];
    }    
}
