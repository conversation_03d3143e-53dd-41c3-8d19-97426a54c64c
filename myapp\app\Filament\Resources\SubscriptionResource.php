<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SubscriptionResource\Pages;
use App\Filament\Resources\SubscriptionResource\RelationManagers;
use App\Models\Subscription;
use App\Models\Plan;
use App\Models\Region;
use App\Models\User;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SubscriptionResource extends Resource
{
    protected static ?string $model = Subscription::class;

    protected static ?string $navigationIcon = 'heroicon-o-star';

    protected static ?string $navigationLabel = '订阅管理';

    protected static ?string $modelLabel = '订阅';

    protected static ?string $pluralModelLabel = '订阅';

    protected static ?string $navigationGroup = '订单管理';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('订阅信息')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->label('用户')
                            ->relationship('user', 'name')
                            ->required()
                            ->searchable(),
                        
                        Forms\Components\Select::make('plan_id')
                            ->label('套餐')
                            ->relationship('plan', 'name')
                            ->required(),
                        
                        Forms\Components\Select::make('region_id')
                            ->label('地区')
                            ->relationship('region', 'name')
                            ->required(),
                        
                        Forms\Components\Select::make('status')
                            ->label('状态')
                            ->options([
                                'pending' => '待激活',
                                'active' => '已激活',
                                'expired' => '已过期',
                                'cancelled' => '已取消',
                            ])
                            ->required(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('计费信息')
                    ->schema([
                        Forms\Components\Select::make('billing_cycle')
                            ->label('计费周期')
                            ->options([
                                'monthly' => '月付',
                                'yearly' => '年付',
                                'lifetime' => '终身',
                            ])
                            ->required(),
                        
                        Forms\Components\TextInput::make('amount')
                            ->label('订阅金额')
                            ->required()
                            ->numeric()
                            ->step(0.01)
                            ->prefix('¥'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('时间信息')
                    ->schema([
                        Forms\Components\DateTimePicker::make('starts_at')
                            ->label('开始时间')
                            ->required(),
                        
                        Forms\Components\DateTimePicker::make('expires_at')
                            ->label('到期时间')
                            ->required(),
                        
                        Forms\Components\DateTimePicker::make('activated_at')
                            ->label('激活时间'),
                        
                        Forms\Components\DateTimePicker::make('cancelled_at')
                            ->label('取消时间'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('其他信息')
                    ->schema([
                        Forms\Components\Textarea::make('cancel_reason')
                            ->label('取消原因')
                            ->maxLength(500),
                    ])
                    ->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('用户')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('plan.name')
                    ->label('套餐')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('region.name')
                    ->label('地区')
                    ->sortable(),
                
                Tables\Columns\BadgeColumn::make('status')
                    ->label('状态')
                    ->colors([
                        'warning' => 'pending',
                        'success' => 'active',
                        'danger' => 'expired',
                        'secondary' => 'cancelled',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'pending' => '待激活',
                        'active' => '已激活',
                        'expired' => '已过期',
                        'cancelled' => '已取消',
                        default => $state,
                    }),
                
                Tables\Columns\TextColumn::make('billing_cycle')
                    ->label('计费周期')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'monthly' => '月付',
                        'yearly' => '年付',
                        'lifetime' => '终身',
                        default => $state,
                    }),
                
                Tables\Columns\TextColumn::make('amount')
                    ->label('金额')
                    ->money('CNY')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('starts_at')
                    ->label('开始时间')
                    ->dateTime('Y-m-d H:i')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('expires_at')
                    ->label('到期时间')
                    ->dateTime('Y-m-d H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('状态')
                    ->options([
                        'pending' => '待激活',
                        'active' => '已激活',
                        'expired' => '已过期',
                        'cancelled' => '已取消',
                    ]),
                
                Tables\Filters\SelectFilter::make('billing_cycle')
                    ->label('计费周期')
                    ->options([
                        'monthly' => '月付',
                        'yearly' => '年付',
                        'lifetime' => '终身',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()->label('查看'),
                Tables\Actions\EditAction::make()->label('编辑'),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make()->label('批量删除'),
            ])
            ->defaultSort('created_at', 'desc');
    }
    
    public static function getRelations(): array
    {
        return [
            //
        ];
    }
    
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSubscriptions::route('/'),
            'create' => Pages\CreateSubscription::route('/create'),
            'view' => Pages\ViewSubscription::route('/{record}'),
            'edit' => Pages\EditSubscription::route('/{record}/edit'),
        ];
    }    
}
