<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Models\Plan;
use App\Models\Region;
use App\Models\Subscription;
use App\Models\PaymentOrder;
use App\Models\User;
use Carbon\Carbon;

class SubscriptionController extends Controller
{
    /**
     * 显示订阅页面
     */
    public function showSubscriptionForm()
    {
        // 获取所有套餐 - 使用is_active字段而不是status字段
        $plans = Plan::where('is_active', true)->orderBy('price')->get();
        
        // 获取地区数据
        $regions = Region::whereNull('parent_id')->with('children.children')->get();
        
        return view('subscribe', compact('plans', 'regions'));
    }

    /**
     * 处理订阅提交
     */
    public function processSubscription(Request $request)
    {
        // 验证输入数据
        $validator = Validator::make($request->all(), [
            'region_id' => 'required|exists:regions,id',
            'plan_id' => 'required|exists:plans,id',
            'billing_cycle' => 'required|in:monthly,quarterly,yearly,lifetime',
            'payment_method' => 'required|in:card',
        ], [
            'region_id.required' => '请选择您的地区',
            'region_id.exists' => '选择的地区无效',
            'plan_id.required' => '请选择订阅套餐',
            'plan_id.exists' => '选择的套餐无效',
            'billing_cycle.required' => '请选择计费周期',
            'billing_cycle.in' => '计费周期无效',
            'payment_method.required' => '请选择支付方式',
            'payment_method.in' => '支付方式无效',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();

            $user = Auth::user();
            $plan = Plan::findOrFail($request->plan_id);
            $region = Region::findOrFail($request->region_id);

            // 检查用户是否已有活跃订阅
            $existingSubscription = $user->subscriptions()
                ->where('status', 'active')
                ->where('expires_at', '>', now())
                ->first();

            if ($existingSubscription) {
                return redirect()->back()
                    ->with('error', '您已经有一个活跃的订阅，请先取消当前订阅再购买新套餐。');
            }

            // 计算价格和到期时间
            $priceData = $this->calculatePrice($plan, $request->billing_cycle);
            
            // 创建支付订单
            $paymentOrder = PaymentOrder::create([
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'region_id' => $region->id,
                'amount' => $priceData['final_price'],
                'original_amount' => $priceData['original_price'],
                'discount_amount' => $priceData['discount_amount'],
                'currency' => 'USD',
                'payment_method' => $request->payment_method,
                'billing_cycle' => $request->billing_cycle,
                'status' => 'pending',
                'order_number' => $this->generateOrderNumber(),
                'expires_at' => now()->addMinutes(30), // 订单30分钟后过期
            ]);

            // 创建订阅记录（待支付状态）
            $subscription = Subscription::create([
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'region_id' => $region->id,
                'payment_order_id' => $paymentOrder->id,
                'status' => 'pending',
                'billing_cycle' => $request->billing_cycle,
                'amount' => $priceData['final_price'],
                'starts_at' => now(),
                'expires_at' => $priceData['expires_at'],
            ]);

            // 记录操作日志
            Log::info('用户创建订阅订单', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'payment_order_id' => $paymentOrder->id,
                'plan_id' => $plan->id,
                'amount' => $priceData['final_price'],
                'billing_cycle' => $request->billing_cycle,
                'payment_method' => $request->payment_method,
            ]);

            DB::commit();

            // 重定向到支付页面
            return redirect()->route('subscription.payment', $paymentOrder->id)
                ->with('success', '订单创建成功，请完成支付。');

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('订阅处理失败', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return redirect()->back()
                ->with('error', '订阅处理失败，请稍后重试。')
                ->withInput();
        }
    }

    /**
     * 显示支付页面
     */
    public function showPaymentPage($orderId)
    {
        $paymentOrder = PaymentOrder::with(['user', 'plan', 'region'])
            ->where('id', $orderId)
            ->where('user_id', Auth::id())
            ->where('status', 'pending')
            ->firstOrFail();

        // 检查订单是否过期
        if ($paymentOrder->expires_at < now()) {
            $paymentOrder->update(['status' => 'expired']);
            return redirect()->route('subscription.form')
                ->with('error', '订单已过期，请重新创建订单。');
        }

        return view('subscription.payment', compact('paymentOrder'));
    }

    /**
     * 处理支付（模拟）
     */
    public function processPayment(Request $request, $orderId)
    {
        $validator = Validator::make($request->all(), [
            'payment_method' => 'required|in:card',
        ], [
            'payment_method.required' => '请选择支付方式',
            'payment_method.in' => '支付方式无效',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator);
        }

        try {
            DB::beginTransaction();

            $paymentOrder = PaymentOrder::with('subscription')
                ->where('id', $orderId)
                ->where('user_id', Auth::id())
                ->where('status', 'pending')
                ->firstOrFail();

            // 模拟支付处理（实际项目中这里会调用支付API）
            $paymentResult = $this->simulatePayment($paymentOrder, $request->payment_method);

            if ($paymentResult['success']) {
                // 更新支付订单状态
                $paymentOrder->update([
                    'status' => 'paid',
                    'paid_at' => now(),
                    'transaction_id' => $paymentResult['transaction_id'],
                    'payment_method' => $request->payment_method,
                ]);

                // 激活订阅
                $subscription = $paymentOrder->subscription;
                $subscription->update([
                    'status' => 'active',
                    'activated_at' => now(),
                ]);

                // 更新用户地区（如果有变化）
                if ($paymentOrder->region_id != $paymentOrder->user->region_id) {
                    $paymentOrder->user->update([
                        'region_id' => $paymentOrder->region_id,
                    ]);
                }

                // 记录成功日志
                Log::info('订阅支付成功', [
                    'user_id' => $paymentOrder->user_id,
                    'payment_order_id' => $paymentOrder->id,
                    'subscription_id' => $subscription->id,
                    'transaction_id' => $paymentResult['transaction_id'],
                    'amount' => $paymentOrder->amount,
                    'payment_method' => $request->payment_method,
                ]);

                DB::commit();

                return redirect()->route('subscription.success', $subscription->id)
                    ->with('success', '支付成功！您的订阅已激活。');

            } else {
                // 支付失败
                $paymentOrder->update([
                    'status' => 'failed',
                    'failed_reason' => $paymentResult['error_message'],
                ]);

                Log::warning('订阅支付失败', [
                    'user_id' => $paymentOrder->user_id,
                    'payment_order_id' => $paymentOrder->id,
                    'error' => $paymentResult['error_message'],
                ]);

                DB::commit();

                return redirect()->back()
                    ->with('error', '支付失败：' . $paymentResult['error_message']);
            }

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('支付处理异常', [
                'order_id' => $orderId,
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
            ]);

            return redirect()->back()
                ->with('error', '支付处理失败，请稍后重试。');
        }
    }

    /**
     * 订阅成功页面
     */
    public function subscriptionSuccess($subscriptionId)
    {
        $subscription = Subscription::with(['plan', 'region', 'paymentOrder'])
            ->where('id', $subscriptionId)
            ->where('user_id', Auth::id())
            ->where('status', 'active')
            ->firstOrFail();

        return view('subscription.success', compact('subscription'));
    }

    /**
     * 获取套餐价格（AJAX）
     */
    public function getPlanPrice(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'plan_id' => 'required|exists:plans,id',
            'billing_cycle' => 'required|in:monthly,quarterly,yearly,lifetime',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => '参数无效'], 400);
        }

        $plan = Plan::findOrFail($request->plan_id);
        $priceData = $this->calculatePrice($plan, $request->billing_cycle);

        return response()->json([
            'success' => true,
            'data' => $priceData,
        ]);
    }

    /**
     * 计算价格
     */
    private function calculatePrice($plan, $billingCycle)
    {
        $originalPrice = $plan->price;
        $finalPrice = $originalPrice;
        $discountAmount = 0;
        $discountPercent = 0;

        // 根据计费周期计算价格
        switch ($billingCycle) {
            case 'monthly':
                // 月付价格不变
                $expiresAt = now()->addMonth();
                break;
                
            case 'quarterly':
                // 季付 - 3个月，25%折扣
                $originalPrice = $plan->price * 3;
                $discountPercent = 25;
                $discountAmount = $originalPrice * 0.25;
                $finalPrice = $originalPrice - $discountAmount;
                $expiresAt = now()->addMonths(3);
                break;
                
            case 'yearly':
                // 年付 - 12个月，70%折扣
                $originalPrice = $plan->price * 12;
                $discountPercent = 70;
                $discountAmount = $originalPrice * 0.7;
                $finalPrice = $originalPrice - $discountAmount;
                $expiresAt = now()->addYear();
                break;
                
            case 'lifetime':
                // 终身价格（如果套餐支持）
                if ($plan->name === 'Lifetime') {
                    $originalPrice = $plan->price;
                    $finalPrice = $originalPrice;
                    $expiresAt = now()->addYears(99); // 99年后过期
                } else {
                    // 其他套餐的终身价格为年付价格的10倍
                    $originalPrice = $plan->price * 12 * 10;
                    $discountPercent = 50;
                    $discountAmount = $originalPrice * 0.5;
                    $finalPrice = $originalPrice - $discountAmount;
                    $expiresAt = now()->addYears(99);
                }
                break;
        }

        return [
            'original_price' => $originalPrice,
            'final_price' => $finalPrice,
            'discount_amount' => $discountAmount,
            'discount_percent' => $discountPercent,
            'expires_at' => $expiresAt,
            'billing_cycle' => $billingCycle,
        ];
    }

    /**
     * 生成订单号
     */
    private function generateOrderNumber()
    {
        return 'SUB' . date('YmdHis') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }

    /**
     * 模拟支付处理
     */
    private function simulatePayment($paymentOrder, $paymentMethod)
    {
        // 模拟支付成功率（90%成功率）
        $success = mt_rand(1, 100) <= 90;
        
        if ($success) {
            return [
                'success' => true,
                'transaction_id' => $paymentMethod . '_' . time() . '_' . mt_rand(1000, 9999),
                'message' => '支付成功',
            ];
        } else {
            $errorMessages = [
                '余额不足',
                '银行卡已过期',
                '支付密码错误',
                '网络超时',
                '银行系统维护中',
            ];
            
            return [
                'success' => false,
                'error_message' => $errorMessages[array_rand($errorMessages)],
            ];
        }
    }

    /**
     * 取消订阅
     */
    public function cancelSubscription(Request $request, $subscriptionId)
    {
        try {
            $subscription = Subscription::where('id', $subscriptionId)
                ->where('user_id', Auth::id())
                ->where('status', 'active')
                ->firstOrFail();

            $subscription->update([
                'status' => 'cancelled',
                'cancelled_at' => now(),
                'cancel_reason' => $request->input('reason', '用户主动取消'),
            ]);

            Log::info('用户取消订阅', [
                'user_id' => Auth::id(),
                'subscription_id' => $subscription->id,
                'reason' => $request->input('reason', '用户主动取消'),
            ]);

            return redirect()->route('dashboard')
                ->with('success', '订阅已成功取消。');

        } catch (\Exception $e) {
            Log::error('取消订阅失败', [
                'subscription_id' => $subscriptionId,
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
            ]);

            return redirect()->back()
                ->with('error', '取消订阅失败，请稍后重试。');
        }
    }
} 