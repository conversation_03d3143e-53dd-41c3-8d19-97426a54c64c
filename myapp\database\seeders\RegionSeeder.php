<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Region;

class RegionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // 创建美国作为主要国家
        $usa = Region::create([
            'name' => 'United States',
            'code' => 'US',
            'type' => 'country',
            'is_active' => true,
            'sort_order' => 1,
            'description' => 'United States of America'
        ]);

        // 创建主要州
        $states = [
            ['name' => 'California', 'code' => 'CA'],
            ['name' => 'New York', 'code' => 'NY'],
            ['name' => 'Texas', 'code' => 'TX'],
            ['name' => 'Florida', 'code' => 'FL'],
            ['name' => 'Illinois', 'code' => 'IL'],
            ['name' => 'Pennsylvania', 'code' => 'PA'],
            ['name' => 'Ohio', 'code' => 'OH'],
            ['name' => 'Georgia', 'code' => 'GA'],
            ['name' => 'North Carolina', 'code' => 'NC'],
            ['name' => 'Michigan', 'code' => 'MI'],
        ];

        foreach ($states as $index => $state) {
            Region::create([
                'name' => $state['name'],
                'code' => $state['code'],
                'type' => 'state',
                'parent_id' => $usa->id,
                'is_active' => true,
                'sort_order' => $index + 1,
                'description' => $state['name'] . ' State'
            ]);
        }

        // 创建加州的主要城市
        $california = Region::where('code', 'CA')->first();
        $cities = [
            ['name' => 'Los Angeles', 'code' => 'LA'],
            ['name' => 'San Francisco', 'code' => 'SF'],
            ['name' => 'San Diego', 'code' => 'SD'],
            ['name' => 'Sacramento', 'code' => 'SAC'],
            ['name' => 'San Jose', 'code' => 'SJ'],
        ];

        foreach ($cities as $index => $city) {
            Region::create([
                'name' => $city['name'],
                'code' => $city['code'],
                'type' => 'city',
                'parent_id' => $california->id,
                'is_active' => true,
                'sort_order' => $index + 1,
                'description' => $city['name'] . ', California'
            ]);
        }
    }
}
