<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use App\Models\Region;

class RegisterController extends Controller
{
    /**
     * 显示注册表单
     */
    public function showRegistrationForm()
    {
        return view('auth.register');
    }

    /**
     * 处理用户注册
     */
    public function register(Request $request)
    {
        try {
            // 验证输入数据
            $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:6|confirmed',
                'region_id' => 'nullable|exists:regions,id',
        ], [
            'name.required' => '姓名不能为空',
            'name.max' => '姓名长度不能超过255个字符',
            'email.required' => '邮箱地址不能为空',
            'email.email' => '请输入有效的邮箱地址',
            'email.unique' => '该邮箱地址已被注册',
            'password.required' => '密码不能为空',
            'password.min' => '密码长度至少为6位',
            'password.confirmed' => '两次输入的密码不一致',
            'region_id.exists' => '选择的地区无效',
        ]);

            // 构建通知设置
            $notificationPreferences = [
                'email_notifications' => $request->has('email_notifications'),
                'wechat_notifications' => $request->has('wechat_notifications'),
                'price_alerts' => $request->has('price_alerts'),
                'stock_alerts' => $request->has('stock_alerts'),
            ];

            // 创建用户
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'region_id' => $request->region_id,
                'notification_preferences' => json_encode($notificationPreferences),
                'email_notifications' => $request->has('email_notifications'),
                'wechat_notifications' => $request->has('wechat_notifications'),
                'status' => 'active',
                'role' => 'R_USER',
                'email_verified_at' => now(),
            ]);

            // 记录注册日志
            Log::info('用户注册成功', [
                'user_id' => $user->id,
                'email' => $user->email,
                'name' => $user->name,
                'role' => $user->role,
                'ip' => $request->ip(),
            ]);

            // 注册成功后的处理
            return redirect()->route('register.success')->with([
                'success' => '注册成功！欢迎使用商品提醒系统！',
                'user_name' => $user->name,
                'user_email' => $user->email,
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            // 验证失败，返回错误信息
            return redirect()->back()
                ->withErrors($e->validator)
                ->withInput();

        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('用户注册失败', [
                'request' => $request->all(),
                'error' => $e->getMessage(),
                'ip' => $request->ip(),
            ]);

            return redirect()->back()
                ->with('error', '注册失败，请稍后重试。如果问题持续存在，请联系客服。')
                ->withInput();
        }
    }

    /**
     * 注册成功页面
     */
    public function registrationSuccess()
    {
        return view('auth.register-success');
    }

    /**
     * 检查邮箱是否已存在（AJAX接口）
     */
    public function checkEmail(Request $request)
    {
        try {
        $email = $request->input('email');
            
            if (!$email) {
                return response()->json([
                    'exists' => false,
                    'message' => '请输入邮箱地址'
                ]);
            }

        $exists = User::where('email', $email)->exists();
        
        return response()->json([
            'exists' => $exists,
            'message' => $exists ? '该邮箱地址已被注册' : '邮箱地址可用'
        ]);

        } catch (\Exception $e) {
            Log::error('检查邮箱失败', [
                'email' => $request->input('email'),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'exists' => false,
                'message' => '检查邮箱时发生错误'
            ]);
        }
    }
} 