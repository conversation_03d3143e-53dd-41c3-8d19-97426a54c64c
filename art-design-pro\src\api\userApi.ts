import request from '@/utils/http'

// 用户数据类型定义
export interface User {
  id: number
  name: string
  email: string
  phone?: string
  role: string
  status: number
  avatar?: string
  created_at: string
  updated_at: string
}

export interface UserGroup {
  id: number
  name: string
  description: string
  member_count: number
  status: number
  created_at: string
}

export interface UserPermission {
  id: number
  username: string
  email: string
  role: string
  permissions: string[]
  last_login: string
}

export interface UserLog {
  id: number
  username: string
  action: string
  description: string
  ip_address: string
  user_agent: string
  method: string
  url: string
  created_at: string
  extra_data?: any
}

export interface SystemSettings {
  basic: {
    default_page_size: number
    session_timeout: string
    password_complexity: string[]
    password_min_length: number
    allow_multiple_login: boolean
  }
  security: {
    login_attempts: number
    lockout_duration: string
    ip_whitelist: string
    enable_2fa: boolean
    api_rate_limit: number
  }
  notification: {
    email_notifications: string[]
    sms_notifications: string[]
    notification_email: string
    notification_phone: string
  }
}

export interface UserCreateParams {
  name: string
  email: string
  phone?: string
  password: string
  role: string
  status?: number
}

export interface UserUpdateParams {
  name: string
  email: string
  phone?: string
  role: string
  status?: number
  password?: string
}

export interface UserListParams {
  page?: number
  per_page?: number
  search?: string
  role?: string
}

export interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
  errors?: any
}

export interface PaginatedResponse<T> extends ApiResponse<T> {
  data: {
    data: T[]
    current_page: number
    last_page: number
    per_page: number
    total: number
    from: number
    to: number
  }
}

// 用户管理API服务
export class UserService {
  
  /**
   * 获取用户列表
   */
  static getUserList(params?: UserListParams) {
    return request.get<{
      data: User[]
      total: number
      current_page: number
      per_page: number
      last_page: number
      from: number
      to: number
    }>({
      url: '/api/user/list',
      params
    })
  }

  /**
   * 创建用户
   */
  static createUser(data: UserCreateParams) {
    return request.post<User>({
      url: '/api/user/list',
      data
    })
  }

  /**
   * 更新用户
   */
  static updateUser(id: number, data: UserUpdateParams) {
    return request.put<User>({
      url: `/api/user/list/${id}`,
      data
    })
  }

  /**
   * 删除用户
   */
  static deleteUser(id: number) {
    return request.del<any>({
      url: `/api/user/list/${id}`
    })
  }

  /**
   * 切换用户状态
   */
  static toggleUserStatus(id: number) {
    return request.put<{ status: number }>({
      url: `/api/user/list/${id}/toggle`
    })
  }

  /**
   * 获取用户组列表
   */
  static getUserGroups() {
    return request.get<UserGroup[]>({
      url: '/api/user/groups'
    })
  }

  /**
   * 获取用户权限列表
   */
  static getUserPermissions() {
    return request.get<UserPermission[]>({
      url: '/api/user/permissions'
    })
  }

  /**
   * 获取用户日志
   */
  static getUserLogs(params?: {
    page?: number
    per_page?: number
    username?: string
    action?: string
    date_range?: string[]
  }) {
    return request.get<UserLog[]>({
      url: '/api/user/logs',
      params
    })
  }

  /**
   * 获取系统设置
   */
  static getSystemSettings() {
    return request.get<SystemSettings>({
      url: '/api/user/settings'
    })
  }

  /**
   * 更新系统设置
   */
  static updateSystemSettings(data: Partial<SystemSettings>) {
    return request.post<any>({
      url: '/api/user/settings',
      data
    })
  }
}

// 导出默认服务
export default UserService 