<template>
  <div class="app-container">
    <!-- 头部操作栏 -->
    <div class="header-actions">
      <el-row :gutter="16" align="middle">
        <el-col :span="12">
          <h2 class="page-title">商品管理</h2>
        </el-col>
        <el-col :span="12" class="text-right">
          <el-button type="primary" @click="handleAdd" :icon="Plus">
            添加商品
          </el-button>
          <el-button @click="loadProductList" :loading="loading" :icon="Refresh">
            刷新
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="16" class="stats-cards">
      <el-col :span="4" v-for="stat in statsData" :key="stat.key">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon" :class="stat.iconClass">
              <el-icon :size="24">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stat.value }}</div>
              <div class="stats-label">{{ stat.label }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索过滤栏 -->
    <el-card class="filter-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="搜索">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索商品名称、SKU、品牌"
            style="width: 200px"
            clearable
          />
        </el-form-item>
        <el-form-item label="分类">
          <el-select v-model="searchForm.category" placeholder="选择分类" clearable style="width: 150px">
            <el-option
              v-for="category in options.categories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="品牌">
          <el-select v-model="searchForm.brand" placeholder="选择品牌" clearable style="width: 150px">
            <el-option
              v-for="brand in options.brands"
              :key="brand"
              :label="brand"
              :value="brand"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="地区">
          <el-select v-model="searchForm.region_id" placeholder="选择地区" clearable style="width: 150px">
            <el-option
              v-for="region in options.regions"
              :key="region.id"
              :label="region.name"
              :value="region.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.is_active" placeholder="选择状态" clearable style="width: 120px">
            <el-option label="上架" :value="1" />
            <el-option label="下架" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="库存状态">
          <el-select v-model="searchForm.stock_status" placeholder="库存状态" clearable style="width: 120px">
            <el-option
              v-for="status in options.stock_statuses"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">
            搜索
          </el-button>
          <el-button @click="handleResetSearch" :icon="RefreshLeft">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 商品列表 -->
    <el-card class="list-card">
      <template #header>
        <div class="card-header">
          <span>商品列表</span>
          <div>
            <el-button
              type="warning"
              @click="handleBatchOperation"
              :disabled="!selectedProducts.length"
              :icon="Operation"
            >
              批量操作
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="productList"
        v-loading="loading"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="商品名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="sku" label="SKU" width="120" />
        <el-table-column label="图片" width="120">
          <template #default="{ row }">
            <el-image
              v-if="row.image_url"
              :src="row.image_url"
              :alt="row.name"
              fit="cover"
              style="width: 80px; height: 50px; border-radius: 4px;"
              :preview-src-list="[row.image_url]"
            />
            <span v-else class="text-gray-400">无图片</span>
          </template>
        </el-table-column>
        <el-table-column prop="brand" label="品牌" width="100" />
        <el-table-column prop="category" label="分类" width="100" />
        <el-table-column label="价格" width="120">
          <template #default="{ row }">
            <div class="price-info">
              <div class="current-price">{{ row.currency }} {{ row.price }}</div>
              <div v-if="row.original_price && row.original_price != row.price" class="original-price">
                原价: {{ row.currency }} {{ row.original_price }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="库存" width="120">
          <template #default="{ row }">
            <div class="stock-info">
              <div class="stock-quantity">{{ row.stock_quantity }}</div>
              <el-tag :type="getStockStatusType(row.stock_status)" size="small">
                {{ row.stock_status_text }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="followers_count" label="关注数" width="80" />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.is_active"
              @change="handleToggleStatus(row)"
              :loading="row.statusLoading"
              active-text="上架"
              inactive-text="下架"
            />
          </template>
        </el-table-column>
        <el-table-column prop="region.name" label="地区" width="100" />
        <el-table-column prop="last_updated_at" label="更新时间" width="160" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)" :icon="Edit">
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
              :icon="Delete"
              :loading="row.deleteLoading"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current_page"
          v-model:page-size="pagination.per_page"
          :page-sizes="[10, 15, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handleCurrentPageChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑商品弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogMode === 'add' ? '添加商品' : '编辑商品'"
      width="800px"
      :before-close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        @submit.prevent
      >
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="商品名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入商品名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品SKU" prop="sku">
              <el-input v-model="formData.sku" placeholder="请输入商品SKU" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="品牌" prop="brand">
              <el-input v-model="formData.brand" placeholder="请输入品牌" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分类" prop="category">
              <el-input v-model="formData.category" placeholder="请输入分类" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="价格" prop="price">
              <el-input-number v-model="formData.price" :min="0" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="原价" prop="original_price">
              <el-input-number v-model="formData.original_price" :min="0" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="货币" prop="currency">
              <el-select v-model="formData.currency" style="width: 100%">
                <el-option
                  v-for="currency in options.currencies"
                  :key="currency"
                  :label="currency"
                  :value="currency"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="库存数量" prop="stock_quantity">
              <el-input-number v-model="formData.stock_quantity" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="库存状态" prop="stock_status">
              <el-select v-model="formData.stock_status" style="width: 100%">
                <el-option
                  v-for="status in options.stock_statuses"
                  :key="status.value"
                  :label="status.label"
                  :value="status.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="所属地区" prop="region_id">
              <el-select v-model="formData.region_id" style="width: 100%">
                <el-option
                  v-for="region in options.regions"
                  :key="region.id"
                  :label="region.name"
                  :value="region.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="is_active">
              <el-switch
                v-model="formData.is_active"
                active-text="上架"
                inactive-text="下架"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="商品图片" prop="image_url">
          <el-input v-model="formData.image_url" placeholder="请输入商品图片URL" />
        </el-form-item>

        <el-form-item label="商品链接" prop="product_url">
          <el-input v-model="formData.product_url" placeholder="请输入商品链接" />
        </el-form-item>

        <el-form-item label="商品描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="4"
            placeholder="请输入商品描述"
          />
        </el-form-item>

        <el-form-item label="商品属性" prop="attributes">
          <el-input
            v-model="attributesText"
            type="textarea"
            :rows="3"
            placeholder='请输入JSON格式的商品属性，如：{"color":"红色","size":"L"}'
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ dialogMode === 'add' ? '添加' : '更新' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量操作弹窗 -->
    <el-dialog
      v-model="batchDialogVisible"
      title="批量操作"
      width="500px"
    >
      <el-form :model="batchForm" label-width="100px">
        <el-form-item label="操作类型">
          <el-select v-model="batchForm.action" placeholder="选择操作类型" style="width: 100%">
            <el-option label="批量上架" value="activate" />
            <el-option label="批量下架" value="deactivate" />
            <el-option label="批量删除" value="delete" />
            <el-option label="批量更新库存状态" value="update_stock_status" />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="batchForm.action === 'update_stock_status'"
          label="库存状态"
        >
          <el-select v-model="batchForm.stock_status" style="width: 100%">
            <el-option
              v-for="status in options.stock_statuses"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <div class="batch-info">
            已选择 <strong>{{ selectedProducts.length }}</strong> 个商品
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleBatchSubmit" :loading="batchLoading">
            确认操作
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Edit,
  Delete,
  Refresh,
  Search,
  RefreshLeft,
  Operation,
  Box,
  TrendCharts,
  Warning,
  Check
} from '@element-plus/icons-vue'
import { ProductService, ProductItem, ProductListParams } from '@/api/productApi'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const batchDialogVisible = ref(false)
const dialogMode = ref<'add' | 'edit'>('add')
const submitLoading = ref(false)
const batchLoading = ref(false)
const formRef = ref()

// 商品列表数据
const productList = ref<ProductItem[]>([])
const selectedProducts = ref<ProductItem[]>([])

// 分页数据
const pagination = reactive({
  current_page: 1,
  per_page: 15,
  total: 0,
  last_page: 1,
  from: 0,
  to: 0
})

// 搜索表单
const searchForm = reactive<ProductListParams>({
  search: '',
  category: '',
  brand: '',
  region_id: undefined,
  is_active: undefined,
  stock_status: '',
  sort_by: 'created_at',
  sort_direction: 'desc'
})

// 商品表单
const formData = reactive({
  id: null as number | null,
  name: '',
  sku: '',
  description: '',
  brand: '',
  category: '',
  price: null as number | null,
  original_price: null as number | null,
  currency: 'CNY',
  stock_quantity: 0,
  stock_status: 'in_stock',
  image_url: '',
  product_url: '',
  region_id: null as number | null,
  attributes: null as any,
  is_active: true
})

// 批量操作表单
const batchForm = reactive({
  action: '',
  stock_status: '',
  ids: [] as number[]
})

// 商品属性文本（用于JSON编辑）
const attributesText = ref('')

// 选项数据
const options = reactive({
  regions: [] as Array<{ id: number; name: string; code: string }>,
  categories: [] as string[],
  brands: [] as string[],
  currencies: ['USD', 'CNY', 'EUR', 'JPY', 'GBP'],
  stock_statuses: [
    { value: 'in_stock', label: '有库存' },
    { value: 'out_of_stock', label: '无库存' },
    { value: 'discontinued', label: '已停产' }
  ]
})

// 统计数据
const statsData = ref([
  {
    key: 'total',
    label: '总商品数',
    value: 0,
    icon: Box,
    iconClass: 'stats-icon-primary'
  },
  {
    key: 'active',
    label: '已上架',
    value: 0,
    icon: Check,
    iconClass: 'stats-icon-success'
  },
  {
    key: 'inactive',
    label: '已下架',
    value: 0,
    icon: Warning,
    iconClass: 'stats-icon-warning'
  },
  {
    key: 'out_of_stock',
    label: '缺货商品',
    value: 0,
    icon: TrendCharts,
    iconClass: 'stats-icon-danger'
  }
])

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入商品名称', trigger: 'blur' }
  ],
  sku: [
    { required: true, message: '请输入商品SKU', trigger: 'blur' }
  ],
  currency: [
    { required: true, message: '请选择货币类型', trigger: 'change' }
  ],
  stock_quantity: [
    { required: true, message: '请输入库存数量', trigger: 'blur' }
  ],
  stock_status: [
    { required: true, message: '请选择库存状态', trigger: 'change' }
  ],
  region_id: [
    { required: true, message: '请选择所属地区', trigger: 'change' }
  ]
}

// 生命周期
onMounted(() => {
  loadProductList()
  loadOptions()
  loadStats()
})

// 方法
const loadProductList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.current_page,
      per_page: pagination.per_page
    }
    
    const response = await ProductService.getProductList(params)
    console.log('商品列表响应:', response)
    
    // 处理响应数据结构
    if (response && response.data) {
      const { list, pagination: paginationData } = response.data
      
      productList.value = list.map(item => ({
        ...item,
        statusLoading: false,
        deleteLoading: false
      }))
      
      Object.assign(pagination, paginationData)
    }
  } catch (error) {
    ElMessage.error('加载商品列表失败')
    console.error('加载商品列表失败:', error)
    productList.value = []
  } finally {
    loading.value = false
  }
}

const loadOptions = async () => {
  try {
    const response = await ProductService.getProductOptions()
    console.log('选项数据响应:', response)
    
    if (response && response.data) {
      Object.assign(options, response.data)
    }
  } catch (error) {
    ElMessage.error('加载选项数据失败')
    console.error('加载选项数据失败:', error)
  }
}

const loadStats = async () => {
  try {
    const response = await ProductService.getProductStats()
    console.log('统计数据响应:', response)
    
    if (response && response.data) {
      statsData.value.forEach(item => {
        item.value = response.data[item.key as keyof typeof response.data] || 0
      })
    }
  } catch (error) {
    ElMessage.error('加载统计数据失败')
    console.error('加载统计数据失败:', error)
    // 设置默认值
    statsData.value.forEach(item => {
      item.value = 0
    })
  }
}

const handleAdd = () => {
  dialogMode.value = 'add'
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row: ProductItem) => {
  dialogMode.value = 'edit'
  Object.assign(formData, { ...row })
  attributesText.value = row.attributes ? JSON.stringify(row.attributes, null, 2) : ''
  dialogVisible.value = true
}

const handleDelete = async (row: ProductItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除商品"${row.name}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    row.deleteLoading = true
    await ProductService.deleteProduct(row.id)
    ElMessage.success('删除成功')
    loadProductList()
    loadStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('删除失败:', error)
    }
  } finally {
    row.deleteLoading = false
  }
}

const handleToggleStatus = async (row: ProductItem) => {
  const originalStatus = !row.is_active
  row.statusLoading = true
  try {
    await ProductService.toggleProductStatus(row.id)
    ElMessage.success(`商品${row.is_active ? '上架' : '下架'}成功`)
    loadStats()
  } catch (error) {
    // 恢复状态
    row.is_active = originalStatus
    ElMessage.error('操作失败')
    console.error('状态切换失败:', error)
  } finally {
    row.statusLoading = false
  }
}

const handleSearch = () => {
  pagination.current_page = 1
  loadProductList()
}

const handleResetSearch = () => {
  Object.assign(searchForm, {
    search: '',
    category: '',
    brand: '',
    region_id: undefined,
    is_active: undefined,
    stock_status: '',
    sort_by: 'created_at',
    sort_direction: 'desc'
  })
  handleSearch()
}

const handleSelectionChange = (selection: ProductItem[]) => {
  selectedProducts.value = selection
}

const handleBatchOperation = () => {
  if (!selectedProducts.value.length) {
    ElMessage.warning('请先选择要操作的商品')
    return
  }
  batchForm.action = ''
  batchForm.stock_status = ''
  batchDialogVisible.value = true
}

const handleBatchSubmit = async () => {
  if (!batchForm.action) {
    ElMessage.warning('请选择操作类型')
    return
  }
  
  if (batchForm.action === 'update_stock_status' && !batchForm.stock_status) {
    ElMessage.warning('请选择库存状态')
    return
  }

  batchLoading.value = true
  try {
    const ids = selectedProducts.value.map((item: ProductItem) => item.id)
    await ProductService.batchOperation({
      action: batchForm.action as any,
      ids: ids,
      stock_status: batchForm.stock_status
    })
    
    ElMessage.success('批量操作成功')
    batchDialogVisible.value = false
    loadProductList()
    loadStats()
  } catch (error) {
    ElMessage.error('批量操作失败')
    console.error('批量操作失败:', error)
  } finally {
    batchLoading.value = false
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
  } catch (error) {
    return
  }

  // 处理商品属性
  if (attributesText.value.trim()) {
    try {
      formData.attributes = JSON.parse(attributesText.value)
    } catch (error) {
      ElMessage.error('商品属性JSON格式错误')
      return
    }
  } else {
    formData.attributes = null
  }

  submitLoading.value = true
  try {
    if (dialogMode.value === 'add') {
      await ProductService.createProduct(formData as any)
      ElMessage.success('添加成功')
    } else {
      await ProductService.updateProduct(formData.id!, formData as any)
      ElMessage.success('更新成功')
    }
    
    dialogVisible.value = false
    loadProductList()
    loadStats()
  } catch (error) {
    ElMessage.error(dialogMode.value === 'add' ? '添加失败' : '更新失败')
    console.error(`${dialogMode.value === 'add' ? '添加' : '更新'}失败:`, error)
  } finally {
    submitLoading.value = false
  }
}

const handleDialogClose = () => {
  dialogVisible.value = false
  resetForm()
}

const handlePageSizeChange = (size: number) => {
  pagination.per_page = size
  pagination.current_page = 1
  loadProductList()
}

const handleCurrentPageChange = (page: number) => {
  pagination.current_page = page
  loadProductList()
}

const resetForm = () => {
  Object.assign(formData, {
    id: null,
    name: '',
    sku: '',
    description: '',
    brand: '',
    category: '',
    price: null,
    original_price: null,
    currency: 'CNY',
    stock_quantity: 0,
    stock_status: 'in_stock',
    image_url: '',
    product_url: '',
    region_id: null,
    attributes: null,
    is_active: true
  })
  attributesText.value = ''
  formRef.value?.clearValidate()
}

const getStockStatusType = (status: string) => {
  const typeMap = {
    'in_stock': 'success',
    'out_of_stock': 'danger',
    'discontinued': 'warning'
  }
  return typeMap[status] || 'info'
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.header-actions {
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stats-icon-primary {
  background: linear-gradient(135deg, #409eff 0%, #6bb6ff 100%);
}

.stats-icon-success {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
}

.stats-icon-warning {
  background: linear-gradient(135deg, #e6a23c 0%, #ebb563 100%);
}

.stats-icon-danger {
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.filter-card {
  margin-bottom: 20px;
}

.list-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.price-info .current-price {
  font-weight: 600;
  color: #e6a23c;
}

.price-info .original-price {
  font-size: 12px;
  color: #909399;
  text-decoration: line-through;
}

.stock-info {
  text-align: center;
}

.stock-quantity {
  font-weight: 600;
  margin-bottom: 4px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

.dialog-footer {
  text-align: right;
}

.batch-info {
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
  color: #606266;
}

.text-right {
  text-align: right;
}

.text-gray-400 {
  color: #c0c4cc;
}
</style> 