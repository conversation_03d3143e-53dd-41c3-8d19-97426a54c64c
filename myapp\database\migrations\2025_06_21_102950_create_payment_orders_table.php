<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payment_orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_number')->unique(); // 订单号
            $table->unsignedBigInteger('user_id'); // 用户ID
            $table->unsignedBigInteger('plan_id'); // 套餐ID
            $table->unsignedBigInteger('subscription_id')->nullable(); // 关联订阅ID
            $table->decimal('amount', 10, 2); // 金额
            $table->string('currency', 3)->default('USD'); // 货币
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled', 'refunded'])->default('pending'); // 支付状态
            $table->string('payment_method')->default('paypal'); // 支付方式
            $table->string('paypal_order_id')->nullable(); // PayPal订单ID
            $table->string('paypal_payment_id')->nullable(); // PayPal支付ID
            $table->string('paypal_payer_id')->nullable(); // PayPal付款人ID
            $table->json('paypal_response')->nullable(); // PayPal响应数据
            $table->timestamp('paid_at')->nullable(); // 支付时间
            $table->text('notes')->nullable(); // 备注
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('plan_id')->references('id')->on('plans');
            $table->foreign('subscription_id')->references('id')->on('subscriptions');
            $table->index(['user_id', 'status']);
            $table->index(['status', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payment_orders');
    }
};
