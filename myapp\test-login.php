<?php

require_once 'vendor/autoload.php';

// 初始化Laravel应用
$app = require_once 'bootstrap/app.php';

// 创建HTTP内核
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// 创建请求
$request = Illuminate\Http\Request::capture();

// 处理请求
$response = $kernel->handle($request);

// 测试用户创建
try {
    echo "测试用户创建功能...\n";
    
    // 创建测试用户
    $user = new App\Models\User();
    $user->name = 'Test User';
    $user->email = '<EMAIL>';
    $user->password = bcrypt('password123');
    $user->role = 'R_USER';
    $user->status = 'active';
    $user->email_verified_at = now();
    
    // 检查用户是否已存在
    $existingUser = App\Models\User::where('email', '<EMAIL>')->first();
    if ($existingUser) {
        echo "测试用户已存在：" . $existingUser->name . " (" . $existingUser->email . ")\n";
        echo "用户角色：" . $existingUser->role . "\n";
        echo "用户状态：" . $existingUser->status . "\n";
    } else {
        $user->save();
        echo "测试用户创建成功：" . $user->name . " (" . $user->email . ")\n";
        echo "用户ID：" . $user->id . "\n";
        echo "用户角色：" . $user->role . "\n";
    }
    
    echo "\n登录注册系统配置正常！\n";
    
} catch (Exception $e) {
    echo "错误：" . $e->getMessage() . "\n";
    echo "可能的问题：\n";
    echo "1. 数据库连接问题\n";
    echo "2. 用户表不存在或结构不正确\n";
    echo "3. 必要的字段缺失\n";
}

// 清理内核
$kernel->terminate($request, $response); 