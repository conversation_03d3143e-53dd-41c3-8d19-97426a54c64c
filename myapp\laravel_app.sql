/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 50726
 Source Host           : localhost:3306
 Source Schema         : laravel_app

 Target Server Type    : MySQL
 Target Server Version : 50726
 File Encoding         : 65001

 Date: 11/07/2025 14:10:46
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for banners
-- ----------------------------
DROP TABLE IF EXISTS `banners`;
CREATE TABLE `banners`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `title` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `link` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `target` enum('_self','_blank') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '_self',
  `start_time` timestamp(0) NULL DEFAULT NULL,
  `end_time` timestamp(0) NULL DEFAULT NULL,
  `alt_text` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `created_at` timestamp(0) NULL DEFAULT NULL,
  `updated_at` timestamp(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `banners_is_active_sort_order_index`(`is_active`, `sort_order`) USING BTREE,
  INDEX `banners_start_time_end_time_index`(`start_time`, `end_time`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of banners
-- ----------------------------
INSERT INTO `banners` VALUES (1, '首页轮播图 1', 'http://127.0.0.1:8002/storage/uploads/banners/banner_1751884632_phJR3pxAfZ.png', NULL, '这是第一张轮播图的描述', 1, 1, '_self', NULL, NULL, '首页轮播图 1', '2025-06-22 01:13:30', '2025-07-07 10:37:22');
INSERT INTO `banners` VALUES (3, '首页轮播图 3', 'banners/sample3.jpg', '#', '这是第三张轮播图的描述', 0, 3, '_blank', NULL, NULL, '首页轮播图 3', '2025-06-22 01:13:30', '2025-07-07 07:01:48');
INSERT INTO `banners` VALUES (7, '即将推出新功能', 'https://picsum.photos/1200/400?random=4', '#', '更多精彩功能正在开发中，敬请期待', 0, 4, '_self', '2025-08-13 08:21:46', '2025-08-13 08:21:46', '新功能预告', '2025-08-13 08:21:46', '2025-08-13 08:21:46');

-- ----------------------------
-- Table structure for failed_jobs
-- ----------------------------
DROP TABLE IF EXISTS `failed_jobs`;
CREATE TABLE `failed_jobs`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `uuid` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `failed_jobs_uuid_unique`(`uuid`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of failed_jobs
-- ----------------------------

-- ----------------------------
-- Table structure for migrations
-- ----------------------------
DROP TABLE IF EXISTS `migrations`;
CREATE TABLE `migrations`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `migration` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of migrations
-- ----------------------------
INSERT INTO `migrations` VALUES (1, '2014_10_12_000000_create_users_table', 1);
INSERT INTO `migrations` VALUES (2, '2014_10_12_100000_create_password_resets_table', 1);
INSERT INTO `migrations` VALUES (3, '2019_08_19_000000_create_failed_jobs_table', 1);
INSERT INTO `migrations` VALUES (4, '2019_12_14_000001_create_personal_access_tokens_table', 1);
INSERT INTO `migrations` VALUES (5, '2025_06_21_102944_create_regions_table', 1);
INSERT INTO `migrations` VALUES (6, '2025_06_21_102945_create_plans_table', 1);
INSERT INTO `migrations` VALUES (7, '2025_06_21_102947_create_products_table', 1);
INSERT INTO `migrations` VALUES (8, '2025_06_21_102949_create_subscriptions_table', 1);
INSERT INTO `migrations` VALUES (9, '2025_06_21_102950_create_payment_orders_table', 1);
INSERT INTO `migrations` VALUES (10, '2025_06_21_102958_create_user_product_follows_table', 1);
INSERT INTO `migrations` VALUES (11, '2025_06_21_102959_create_notifications_table', 1);
INSERT INTO `migrations` VALUES (12, '2025_06_21_103000_add_fields_to_users_table', 1);
INSERT INTO `migrations` VALUES (13, '2025_06_21_143156_add_role_to_users_table', 2);
INSERT INTO `migrations` VALUES (14, '2025_06_21_154500_create_banners_table', 3);
INSERT INTO `migrations` VALUES (15, '2025_06_22_011806_modify_banners_table_increase_image_length', 4);
INSERT INTO `migrations` VALUES (16, '2025_07_07_145720_recreate_role_column', 5);

-- ----------------------------
-- Table structure for notifications
-- ----------------------------
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `product_id` bigint(20) UNSIGNED NULL DEFAULT NULL,
  `type` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `channel` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `data` json NULL,
  `status` enum('pending','sent','failed','cancelled') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `sent_at` timestamp(0) NULL DEFAULT NULL,
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `retry_count` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp(0) NULL DEFAULT NULL,
  `updated_at` timestamp(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `notifications_product_id_foreign`(`product_id`) USING BTREE,
  INDEX `notifications_user_id_status_index`(`user_id`, `status`) USING BTREE,
  INDEX `notifications_type_status_index`(`type`, `status`) USING BTREE,
  INDEX `notifications_status_created_at_index`(`status`, `created_at`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of notifications
-- ----------------------------

-- ----------------------------
-- Table structure for password_resets
-- ----------------------------
DROP TABLE IF EXISTS `password_resets`;
CREATE TABLE `password_resets`  (
  `email` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp(0) NULL DEFAULT NULL,
  PRIMARY KEY (`email`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of password_resets
-- ----------------------------

-- ----------------------------
-- Table structure for payment_orders
-- ----------------------------
DROP TABLE IF EXISTS `payment_orders`;
CREATE TABLE `payment_orders`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `order_number` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `plan_id` bigint(20) UNSIGNED NOT NULL,
  `subscription_id` bigint(20) UNSIGNED NULL DEFAULT NULL,
  `amount` decimal(10, 2) NOT NULL,
  `currency` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'USD',
  `status` enum('pending','completed','failed','cancelled','refunded') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `payment_method` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'paypal',
  `paypal_order_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `paypal_payment_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `paypal_payer_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `paypal_response` json NULL,
  `paid_at` timestamp(0) NULL DEFAULT NULL,
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `created_at` timestamp(0) NULL DEFAULT NULL,
  `updated_at` timestamp(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `payment_orders_order_number_unique`(`order_number`) USING BTREE,
  INDEX `payment_orders_plan_id_foreign`(`plan_id`) USING BTREE,
  INDEX `payment_orders_subscription_id_foreign`(`subscription_id`) USING BTREE,
  INDEX `payment_orders_user_id_status_index`(`user_id`, `status`) USING BTREE,
  INDEX `payment_orders_status_created_at_index`(`status`, `created_at`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of payment_orders
-- ----------------------------

-- ----------------------------
-- Table structure for personal_access_tokens
-- ----------------------------
DROP TABLE IF EXISTS `personal_access_tokens`;
CREATE TABLE `personal_access_tokens`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `last_used_at` timestamp(0) NULL DEFAULT NULL,
  `expires_at` timestamp(0) NULL DEFAULT NULL,
  `created_at` timestamp(0) NULL DEFAULT NULL,
  `updated_at` timestamp(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `personal_access_tokens_token_unique`(`token`) USING BTREE,
  INDEX `personal_access_tokens_tokenable_type_tokenable_id_index`(`tokenable_type`, `tokenable_id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of personal_access_tokens
-- ----------------------------
INSERT INTO `personal_access_tokens` VALUES (12, 'App\\Models\\User', 1, 'refresh-token', 'b5c44014c82bf29a38cc00d10ed2036b6619cd18939dc21d21bbd17cf879483c', '[\"refresh\"]', NULL, NULL, '2025-07-08 14:04:53', '2025-07-08 14:04:53');
INSERT INTO `personal_access_tokens` VALUES (11, 'App\\Models\\User', 1, 'auth-token', '37d9d410bf58be27406668163aae69ffc80adf48697b7e6643478b8dccdf9449', '[\"*\"]', '2025-07-09 02:14:08', NULL, '2025-07-08 14:04:53', '2025-07-09 02:14:08');
INSERT INTO `personal_access_tokens` VALUES (13, 'App\\Models\\User', 2, 'auth-token', '0839be1b897ee20f06ea3bf1bce1dd0b7cddc7bb1d7fc91edb20112de4e9b73b', '[\"*\"]', '2025-07-09 02:33:33', NULL, '2025-07-09 02:15:04', '2025-07-09 02:33:33');
INSERT INTO `personal_access_tokens` VALUES (14, 'App\\Models\\User', 2, 'refresh-token', 'ad855b197fcc7965603a5be44d8eec347249faf94ffc515415380fad8c64a863', '[\"refresh\"]', NULL, NULL, '2025-07-09 02:15:04', '2025-07-09 02:15:04');

-- ----------------------------
-- Table structure for plans
-- ----------------------------
DROP TABLE IF EXISTS `plans`;
CREATE TABLE `plans`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `price` decimal(10, 2) NOT NULL,
  `currency` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'USD',
  `billing_cycle` enum('monthly','yearly','lifetime') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `duration_days` int(11) NULL DEFAULT NULL,
  `max_products` int(11) NOT NULL DEFAULT 0,
  `max_notifications` int(11) NOT NULL DEFAULT 0,
  `features` json NULL,
  `is_popular` tinyint(1) NOT NULL DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp(0) NULL DEFAULT NULL,
  `updated_at` timestamp(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `plans_slug_unique`(`slug`) USING BTREE,
  INDEX `plans_is_active_sort_order_index`(`is_active`, `sort_order`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of plans
-- ----------------------------
INSERT INTO `plans` VALUES (1, 'Basic', 'basic', '基础套餐，适合个人用户', 9.99, 'CNY', 'monthly', 30, 10, 100, '\"[\\\"\\\\u5546\\\\u54c1\\\\u76d1\\\\u63a7\\\",\\\"\\\\u4ef7\\\\u683c\\\\u63d0\\\\u9192\\\",\\\"\\\\u90ae\\\\u4ef6\\\\u901a\\\\u77e5\\\"]\"', 0, 1, 1, '2025-06-21 14:24:59', '2025-06-21 14:24:59');
INSERT INTO `plans` VALUES (2, 'Professional', 'professional', '专业套餐，适合商业用户', 19.99, 'CNY', 'monthly', 30, 50, 500, '\"[\\\"\\\\u5546\\\\u54c1\\\\u76d1\\\\u63a7\\\",\\\"\\\\u4ef7\\\\u683c\\\\u63d0\\\\u9192\\\",\\\"\\\\u5e93\\\\u5b58\\\\u901a\\\\u77e5\\\",\\\"\\\\u90ae\\\\u4ef6\\\\u901a\\\\u77e5\\\",\\\"\\\\u5fae\\\\u4fe1\\\\u901a\\\\u77e5\\\",\\\"24\\\\u5c0f\\\\u65f6\\\\u5ba2\\\\u670d\\\"]\"', 1, 1, 2, '2025-06-21 14:24:59', '2025-06-21 14:24:59');
INSERT INTO `plans` VALUES (3, 'Enterprise', 'enterprise', '企业套餐，适合大型企业', 49.99, 'CNY', 'monthly', 30, 0, 0, '\"[\\\"\\\\u65e0\\\\u9650\\\\u5546\\\\u54c1\\\\u76d1\\\\u63a7\\\",\\\"\\\\u4ef7\\\\u683c\\\\u63d0\\\\u9192\\\",\\\"\\\\u5e93\\\\u5b58\\\\u901a\\\\u77e5\\\",\\\"\\\\u90ae\\\\u4ef6\\\\u901a\\\\u77e5\\\",\\\"\\\\u5fae\\\\u4fe1\\\\u901a\\\\u77e5\\\",\\\"API\\\\u63a5\\\\u53e3\\\",\\\"\\\\u4e13\\\\u5c5e\\\\u5ba2\\\\u670d\\\",\\\"\\\\u6570\\\\u636e\\\\u62a5\\\\u8868\\\"]\"', 0, 1, 3, '2025-06-21 14:24:59', '2025-06-21 14:24:59');

-- ----------------------------
-- Table structure for products
-- ----------------------------
DROP TABLE IF EXISTS `products`;
CREATE TABLE `products`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `sku` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `brand` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `category` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `price` decimal(10, 2) NULL DEFAULT NULL,
  `original_price` decimal(10, 2) NULL DEFAULT NULL,
  `currency` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'USD',
  `stock_quantity` int(11) NOT NULL DEFAULT 0,
  `stock_status` enum('in_stock','out_of_stock','discontinued') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'in_stock',
  `image_url` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `product_url` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `region_id` bigint(20) UNSIGNED NOT NULL,
  `attributes` json NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `last_updated_at` timestamp(0) NULL DEFAULT NULL,
  `created_at` timestamp(0) NULL DEFAULT NULL,
  `updated_at` timestamp(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `products_sku_unique`(`sku`) USING BTREE,
  INDEX `products_region_id_is_active_index`(`region_id`, `is_active`) USING BTREE,
  INDEX `products_stock_status_is_active_index`(`stock_status`, `is_active`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of products
-- ----------------------------
INSERT INTO `products` VALUES (1, 'iPhone 15 Pro', 'IPHONE15PRO001', '苹果iPhone 15 Pro 256GB 深空黑色', 'Apple', '手机', 8999.00, 9999.00, 'CNY', 50, 'in_stock', 'https://example.com/iphone15pro.jpg', 'https://www.apple.com.cn/iphone-15-pro/', 1, NULL, 1, '2025-06-21 14:25:00', '2025-06-21 14:25:00', '2025-06-21 14:25:00');
INSERT INTO `products` VALUES (2, 'MacBook Pro 14', 'MBP14M3001', 'MacBook Pro 14英寸 M3芯片 512GB SSD', 'Apple', '笔记本电脑', 15999.00, 16999.00, 'CNY', 0, 'out_of_stock', 'https://example.com/macbookpro14.jpg', 'https://www.apple.com.cn/macbook-pro/', 1, NULL, 1, '2025-06-21 14:25:00', '2025-06-21 14:25:00', '2025-06-21 14:25:00');
INSERT INTO `products` VALUES (3, 'iPad Air', 'IPADAIR2024001', 'iPad Air 11英寸 M2芯片 256GB WiFi版', 'Apple', '平板电脑', 4999.00, 4999.00, 'CNY', 100, 'in_stock', 'https://example.com/ipadair.jpg', 'https://www.apple.com.cn/ipad-air/', 1, NULL, 1, '2025-06-21 14:25:00', '2025-06-21 14:25:00', '2025-06-21 14:25:00');

-- ----------------------------
-- Table structure for regions
-- ----------------------------
DROP TABLE IF EXISTS `regions`;
CREATE TABLE `regions`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'country',
  `parent_id` bigint(20) UNSIGNED NULL DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `created_at` timestamp(0) NULL DEFAULT NULL,
  `updated_at` timestamp(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `regions_code_unique`(`code`) USING BTREE,
  INDEX `regions_type_is_active_index`(`type`, `is_active`) USING BTREE,
  INDEX `regions_parent_id_foreign`(`parent_id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of regions
-- ----------------------------
INSERT INTO `regions` VALUES (1, '中国', 'CN', 'country', NULL, 1, 1, NULL, '2025-06-21 14:24:59', '2025-06-21 14:24:59');
INSERT INTO `regions` VALUES (2, '北京', 'BJ', 'state', 1, 1, 1, NULL, '2025-06-21 14:24:59', '2025-06-21 14:24:59');
INSERT INTO `regions` VALUES (3, '上海', 'SH', 'state', 1, 1, 2, NULL, '2025-06-21 14:24:59', '2025-06-21 14:24:59');

-- ----------------------------
-- Table structure for subscriptions
-- ----------------------------
DROP TABLE IF EXISTS `subscriptions`;
CREATE TABLE `subscriptions`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `plan_id` bigint(20) UNSIGNED NOT NULL,
  `region_id` bigint(20) UNSIGNED NOT NULL,
  `subscription_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('active','expired','cancelled','pending') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `amount_paid` decimal(10, 2) NOT NULL,
  `currency` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'USD',
  `starts_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  `expires_at` timestamp(0) NULL DEFAULT NULL,
  `cancelled_at` timestamp(0) NULL DEFAULT NULL,
  `paypal_subscription_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `metadata` json NULL,
  `created_at` timestamp(0) NULL DEFAULT NULL,
  `updated_at` timestamp(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `subscriptions_subscription_id_unique`(`subscription_id`) USING BTREE,
  INDEX `subscriptions_plan_id_foreign`(`plan_id`) USING BTREE,
  INDEX `subscriptions_region_id_foreign`(`region_id`) USING BTREE,
  INDEX `subscriptions_user_id_status_index`(`user_id`, `status`) USING BTREE,
  INDEX `subscriptions_status_expires_at_index`(`status`, `expires_at`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of subscriptions
-- ----------------------------

-- ----------------------------
-- Table structure for user_product_follows
-- ----------------------------
DROP TABLE IF EXISTS `user_product_follows`;
CREATE TABLE `user_product_follows`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `product_id` bigint(20) UNSIGNED NOT NULL,
  `email_notifications` tinyint(1) NOT NULL DEFAULT 1,
  `wechat_notifications` tinyint(1) NOT NULL DEFAULT 0,
  `notification_settings` json NULL,
  `created_at` timestamp(0) NULL DEFAULT NULL,
  `updated_at` timestamp(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `user_product_follows_user_id_product_id_unique`(`user_id`, `product_id`) USING BTREE,
  INDEX `user_product_follows_product_id_foreign`(`product_id`) USING BTREE,
  INDEX `user_product_follows_user_id_created_at_index`(`user_id`, `created_at`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_product_follows
-- ----------------------------
INSERT INTO `user_product_follows` VALUES (1, 1, 2, 1, 0, '{\"price_drop\": true, \"back_in_stock\": true, \"new_similar_products\": false}', '2025-06-21 15:51:10', '2025-06-21 15:51:10');
INSERT INTO `user_product_follows` VALUES (2, 1, 3, 1, 0, '{\"price_drop\": true, \"back_in_stock\": true, \"new_similar_products\": false}', '2025-07-07 06:20:58', '2025-07-07 06:20:58');

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `region_id` bigint(20) UNSIGNED NULL DEFAULT NULL,
  `timezone` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'America/New_York',
  `email_notifications` tinyint(1) NOT NULL DEFAULT 1,
  `wechat_notifications` tinyint(1) NOT NULL DEFAULT 0,
  `wechat_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `notification_preferences` json NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '用户状态: 1-启用, 0-禁用',
  `last_login_at` timestamp(0) NULL DEFAULT NULL,
  `avatar` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `email_verified_at` timestamp(0) NULL DEFAULT NULL,
  `password` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'R_USER' COMMENT '用户角色: R_SUPER, R_ADMIN, R_EDITOR, R_USER',
  `remember_token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `created_at` timestamp(0) NULL DEFAULT NULL,
  `updated_at` timestamp(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `users_email_unique`(`email`) USING BTREE,
  INDEX `users_region_id_status_index`(`region_id`, `status`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES (1, 'admin', '<EMAIL>', NULL, NULL, 'America/New_York', 1, 0, NULL, NULL, 1, '2025-07-08 14:04:53', NULL, '2025-07-08 02:58:25', '$2y$10$x3.bhk9tWkfdeytfgPC7ie0xjRcSpsn2AWW0LIN8o7ZzCwzPDwLm2', 'R_SUPER', NULL, '2025-07-08 02:58:25', '2025-07-08 14:37:57');
INSERT INTO `users` VALUES (2, 'Admin', '<EMAIL>', '***********', NULL, 'America/New_York', 1, 0, NULL, NULL, 1, '2025-07-09 02:15:04', NULL, '2025-07-08 02:58:25', '$2y$10$cyV4SXNLjjuj4STl95uF8uom05H2psCMAR7TCJ1g1dQuqbuNC5dE.', 'R_ADMIN', NULL, '2025-07-08 02:58:25', '2025-07-09 02:15:04');
INSERT INTO `users` VALUES (3, 'Editor', '<EMAIL>', '***********', NULL, 'America/New_York', 1, 0, NULL, NULL, 1, NULL, NULL, '2025-07-08 02:58:25', '$2y$10$MXCyuy.Op0ikys4W9rAF/uuABnjDJ/S40KvknlMKByXos0qhumKYG', 'R_EDITOR', NULL, '2025-07-08 02:58:25', '2025-07-08 02:58:25');
INSERT INTO `users` VALUES (4, 'Normal User', '<EMAIL>', '***********', NULL, 'America/New_York', 1, 0, NULL, NULL, 1, NULL, NULL, '2025-07-08 02:58:25', '$2y$10$bQxf/RHYwxNBrjh99ITI1eGKXQZWbKxfQgthaWmtEvrnyMX79Uabi', 'R_USER', NULL, '2025-07-08 02:58:25', '2025-07-08 02:58:25');
INSERT INTO `users` VALUES (5, 'Test User', '<EMAIL>', '***********', NULL, 'America/New_York', 1, 0, NULL, NULL, 0, NULL, NULL, '2025-07-08 02:58:25', '$2y$10$PJx90QzVBJHAd0Qxr5dwQOjNQxej84Uqujse.fUf5e9K5pt4PrpcS', 'R_USER', NULL, '2025-07-08 02:58:25', '2025-07-08 02:58:25');

SET FOREIGN_KEY_CHECKS = 1;
