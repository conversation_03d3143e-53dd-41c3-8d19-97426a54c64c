import { RoutesAlias } from '../routesAlias'
import { AppRouteRecord } from '@/types/router'

/**
 * 菜单列表、异步路由
 * 只保留核心业务功能，去掉演示样例
 */
export const asyncRoutes: AppRouteRecord[] = [
  {
    name: 'Dashboard',
    path: '/dashboard',
    component: RoutesAlias.Layout,
    meta: {
      title: '仪表板',
      icon: '&#xe721;',
      roles: ['R_SUPER', 'R_ADMIN']
    },
    children: [
      {
        path: 'console',
        name: 'Console',
        component: RoutesAlias.Dashboard,
        meta: {
          title: '工作台',
          keepAlive: false,
          fixedTab: true
        }
      },
      {
        path: 'analysis',
        name: 'Analysis',
        component: RoutesAlias.Analysis,
        meta: {
          title: '数据分析',
          keepAlive: false
        }
      }
    ]
  },
  {
    path: '/banner',
    name: 'Banner',
    component: RoutesAlias.Layout,
    meta: {
      title: '轮播图管理',
      icon: '&#xe7c9;',
      roles: ['R_SUPER', 'R_ADMIN']
    },
    children: [
      {
        path: 'list',
        name: 'BannerList',
        component: '/banner/index',
        meta: {
          title: '轮播图列表',
          keepAlive: true,
          authList: [
            {
              title: '新增',
              authMark: 'add'
      },
      {
              title: '编辑',
              authMark: 'edit'
      },
      {
              title: '删除',
              authMark: 'delete'
      }
    ]
        }
      }
    ]
  },
  {
    path: '/product',
    name: 'Product',
    component: RoutesAlias.Layout,
    meta: {
      title: '商品管理',
      icon: '&#xe7a2;',
      roles: ['R_SUPER', 'R_ADMIN']
    },
    children: [
      {
        path: 'list',
        name: 'ProductList',
        component: RoutesAlias.ProductList,
        meta: {
          title: '商品列表',
          keepAlive: true,
          authList: [
            {
              title: '新增',
              authMark: 'add'
            },
            {
              title: '编辑',
              authMark: 'edit'
            },
            {
              title: '删除',
              authMark: 'delete'
            },
            {
              title: '上架',
              authMark: 'publish'
            },
            {
              title: '下架',
              authMark: 'unpublish'
            },
            {
              title: '批量操作',
              authMark: 'batch'
            },
            {
              title: '导出',
              authMark: 'export'
            }
          ]
        }
      },
      {
        path: 'categories',
        name: 'ProductCategories',
        component: RoutesAlias.ProductCategories,
        meta: {
          title: '分类管理',
          keepAlive: true,
          authList: [
            {
              title: '新增',
              authMark: 'add'
            },
            {
              title: '编辑',
              authMark: 'edit'
            },
            {
              title: '删除',
              authMark: 'delete'
            }
          ]
        }
      },
      {
        path: 'brands',
        name: 'ProductBrands',
        component: RoutesAlias.ProductBrands,
        meta: {
          title: '品牌管理',
          keepAlive: true,
          authList: [
            {
              title: '新增',
              authMark: 'add'
            },
            {
              title: '编辑',
              authMark: 'edit'
            },
            {
              title: '删除',
              authMark: 'delete'
            }
          ]
        }
      },
      {
        path: 'regions',
        name: 'ProductRegions',
        component: RoutesAlias.ProductRegions,
        meta: {
          title: '地区管理',
          keepAlive: true,
          roles: ['R_SUPER'],
          authList: [
            {
              title: '新增',
              authMark: 'add'
            },
            {
              title: '编辑',
              authMark: 'edit'
            },
            {
              title: '删除',
              authMark: 'delete'
            }
          ]
        }
      },
      {
        path: 'settings',
        name: 'ProductSettings',
        component: RoutesAlias.ProductSettings,
        meta: {
          title: '商品设置',
          keepAlive: true,
          roles: ['R_SUPER'],
          authList: [
            {
              title: '修改',
              authMark: 'edit'
            }
          ]
        }
      }
    ]
  },
  {
    path: '/system',
    name: 'System',
    component: RoutesAlias.Layout,
    meta: {
      title: '系统管理',
      icon: '&#xe7b9;',
      roles: ['R_SUPER', 'R_ADMIN']
    },
    children: [
      {
        path: 'user',
        name: 'User',
        component: RoutesAlias.User,
        meta: {
          title: '用户管理',
          keepAlive: true,
          roles: ['R_SUPER', 'R_ADMIN']
        }
      },
      {
        path: 'role',
        name: 'Role',
        component: RoutesAlias.Role,
        meta: {
          title: '角色管理',
          keepAlive: true,
          roles: ['R_SUPER']
        }
      },
      {
        path: 'user-center',
        name: 'UserCenter',
        component: RoutesAlias.UserCenter,
        meta: {
          title: '用户中心',
          isHide: true,
          keepAlive: true,
          isHideTab: true
        }
      },
      {
        path: 'menu',
        name: 'Menus',
        component: RoutesAlias.Menu,
        meta: {
          title: '菜单管理',
          keepAlive: true,
          roles: ['R_SUPER'],
          authList: [
            {
              title: '新增',
              authMark: 'add'
            },
            {
              title: '编辑',
              authMark: 'edit'
            },
            {
              title: '删除',
              authMark: 'delete'
            }
          ]
        }
      }
    ]
  },
  {
    path: '/user',
    name: 'UserManagement',
    component: RoutesAlias.Layout,
    meta: {
      title: '用户管理',
      icon: '&#xe6b8;',
      roles: ['R_SUPER', 'R_ADMIN']
    },
    children: [
      {
        path: 'list',
        name: 'UserList',
        component: RoutesAlias.User,
        meta: {
          title: '用户列表',
          keepAlive: true,
          authList: [
            {
              title: '新增',
              authMark: 'add'
            },
            {
              title: '编辑',
              authMark: 'edit'
            },
            {
              title: '删除',
              authMark: 'delete'
            },
            {
              title: '导出',
              authMark: 'export'
            }
          ]
        }
      },
      {
        path: 'groups',
        name: 'UserGroups',
        component: '/user/groups',
        meta: {
          title: '用户组管理',
          keepAlive: true,
          authList: [
            {
              title: '新增',
              authMark: 'add'
      },
      {
              title: '编辑',
              authMark: 'edit'
            },
            {
              title: '删除',
              authMark: 'delete'
            }
          ]
        }
      },
      {
        path: 'permissions',
        name: 'UserPermissions',
        component: '/user/permissions',
        meta: {
          title: '用户权限',
          keepAlive: true,
          roles: ['R_SUPER'],
          authList: [
            {
              title: '分配权限',
              authMark: 'assign'
  },
  {
              title: '撤销权限',
              authMark: 'revoke'
            }
          ]
        }
      },
      {
        path: 'logs',
        name: 'UserLogs',
        component: '/user/logs',
        meta: {
          title: '用户日志',
          keepAlive: true,
          authList: [
            {
              title: '查看',
              authMark: 'view'
    },
            {
              title: '导出',
              authMark: 'export'
            }
          ]
        }
      },
      {
        path: 'settings',
        name: 'UserSettings',
        component: '/user/settings',
        meta: {
          title: '用户设置',
          keepAlive: true,
          roles: ['R_SUPER'],
          authList: [
            {
              title: '修改',
              authMark: 'edit'
      }
    ]
        }
      }
    ]
  }
]
