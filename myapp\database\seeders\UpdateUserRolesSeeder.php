<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Region;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * 创建测试商品数据
     *
     * @return void
     */
    public function run()
    {
        // 首先确保有地区数据
        $regions = [
            ['name' => '北京', 'code' => 'BJ', 'is_active' => true],
            ['name' => '上海', 'code' => 'SH', 'is_active' => true],
            ['name' => '广州', 'code' => 'GZ', 'is_active' => true],
            ['name' => '深圳', 'code' => 'SZ', 'is_active' => true],
        ];

        foreach ($regions as $regionData) {
            Region::firstOrCreate(
                ['code' => $regionData['code']],
                $regionData
            );
        }

        // 获取地区ID
        $beijingId = Region::where('code', 'BJ')->first()->id;
        $shanghaiId = Region::where('code', 'SH')->first()->id;
        $guangzhouId = Region::where('code', 'GZ')->first()->id;
        $shenzhenId = Region::where('code', 'SZ')->first()->id;

        // 创建测试商品数据
        $products = [
            [
                'name' => 'iPhone 15 Pro Max 256GB',
                'description' => 'Apple iPhone 15 Pro Max，搭载A17 Pro芯片，钛金属设计，专业级摄像系统',
                'price' => 9999.00,
                'original_price' => 10999.00,
                'brand' => 'Apple',
                'category' => '手机数码',
                'sku' => 'IP15PM256',
                'region_id' => $beijingId,
                'is_active' => true,
                'stock_status' => 'in_stock',
                'image_url' => 'https://via.placeholder.com/400x400/007bff/ffffff?text=iPhone+15+Pro',
                'product_url' => 'https://www.apple.com.cn',
            ],
            [
                'name' => '华为Mate 60 Pro 512GB',
                'description' => '华为Mate 60 Pro，麒麟9000S芯片，超感知摄像，卫星通话功能',
                'price' => 6999.00,
                'original_price' => 7499.00,
                'brand' => '华为',
                'category' => '手机数码',
                'sku' => 'HWM60P512',
                'region_id' => $shanghaiId,
                'is_active' => true,
                'stock_status' => 'low_stock',
                'image_url' => 'https://via.placeholder.com/400x400/ff6b35/ffffff?text=Mate+60+Pro',
                'product_url' => 'https://consumer.huawei.com',
            ],
            [
                'name' => '小米14 Ultra 16GB+1TB',
                'description' => '小米14 Ultra，骁龙8 Gen3，徕卡光学镜头，专业摄影旗舰',
                'price' => 6499.00,
                'original_price' => 6999.00,
                'brand' => '小米',
                'category' => '手机数码',
                'sku' => 'MI14U1TB',
                'region_id' => $guangzhouId,
                'is_active' => true,
                'stock_status' => 'in_stock',
                'image_url' => 'https://via.placeholder.com/400x400/ff9500/ffffff?text=Mi+14+Ultra',
                'product_url' => 'https://www.mi.com',
            ],
            [
                'name' => 'MacBook Pro 14英寸 M3 Max',
                'description' => 'MacBook Pro 14英寸，M3 Max芯片，36GB统一内存，1TB SSD存储',
                'price' => 25999.00,
                'original_price' => 27999.00,
                'brand' => 'Apple',
                'category' => '电脑办公',
                'sku' => 'MBP14M3MAX',
                'region_id' => $shenzhenId,
                'is_active' => true,
                'stock_status' => 'in_stock',
                'image_url' => 'https://via.placeholder.com/400x400/5856d6/ffffff?text=MacBook+Pro',
                'product_url' => 'https://www.apple.com.cn',
            ],
            [
                'name' => '联想ThinkPad X1 Carbon',
                'description' => '联想ThinkPad X1 Carbon，第11代英特尔酷睿处理器，轻薄商务本',
                'price' => 12999.00,
                'original_price' => 14999.00,
                'brand' => '联想',
                'category' => '电脑办公',
                'sku' => 'TPX1C11',
                'region_id' => $beijingId,
                'is_active' => true,
                'stock_status' => 'in_stock',
                'image_url' => 'https://via.placeholder.com/400x400/e50000/ffffff?text=ThinkPad',
                'product_url' => 'https://www.lenovo.com.cn',
            ],
            [
                'name' => 'Nike Air Jordan 1 Retro High OG',
                'description' => 'Nike Air Jordan 1 复刻高帮篮球鞋，经典配色，街头潮流必备',
                'price' => 1299.00,
                'original_price' => 1499.00,
                'brand' => 'Nike',
                'category' => '服装鞋帽',
                'sku' => 'AJ1RETRO',
                'region_id' => $shanghaiId,
                'is_active' => true,
                'stock_status' => 'low_stock',
                'image_url' => 'https://via.placeholder.com/400x400/000000/ffffff?text=Air+Jordan+1',
                'product_url' => 'https://www.nike.com',
            ],
            [
                'name' => 'Adidas Yeezy Boost 350 V2',
                'description' => 'Adidas Yeezy Boost 350 V2，椰子鞋经典款，舒适透气运动鞋',
                'price' => 1899.00,
                'original_price' => 2199.00,
                'brand' => 'Adidas',
                'category' => '服装鞋帽',
                'sku' => 'YZY350V2',
                'region_id' => $guangzhouId,
                'is_active' => true,
                'stock_status' => 'out_of_stock',
                'image_url' => 'https://via.placeholder.com/400x400/808080/ffffff?text=Yeezy+350',
                'product_url' => 'https://www.adidas.com.cn',
            ],
            [
                'name' => '海尔BCD-456WDPG冰箱',
                'description' => '海尔456升双开门冰箱，变频节能，干湿分储，智能控温',
                'price' => 3299.00,
                'original_price' => 3699.00,
                'brand' => '海尔',
                'category' => '家用电器',
                'sku' => 'HLBCD456',
                'region_id' => $shenzhenId,
                'is_active' => true,
                'stock_status' => 'in_stock',
                'image_url' => 'https://via.placeholder.com/400x400/1e90ff/ffffff?text=海尔冰箱',
                'product_url' => 'https://www.haier.com',
            ],
            [
                'name' => '美的KJ700G-H32空气净化器',
                'description' => '美的空气净化器，CADR值700，H13级HEPA滤网，除甲醛除雾霾',
                'price' => 2199.00,
                'original_price' => 2599.00,
                'brand' => '美的',
                'category' => '家用电器',
                'sku' => 'MDKJ700',
                'region_id' => $beijingId,
                'is_active' => true,
                'stock_status' => 'in_stock',
                'image_url' => 'https://via.placeholder.com/400x400/32cd32/ffffff?text=美的净化器',
                'product_url' => 'https://www.midea.cn',
            ],
            [
                'name' => '茅台飞天53度500ml',
                'description' => '贵州茅台酒飞天53度，国酒典范，收藏送礼佳品',
                'price' => 2680.00,
                'original_price' => 2880.00,
                'brand' => '茅台',
                'category' => '食品饮料',
                'sku' => 'MTFT53',
                'region_id' => $shanghaiId,
                'is_active' => true,
                'stock_status' => 'low_stock',
                'image_url' => 'https://via.placeholder.com/400x400/dc143c/ffffff?text=茅台酒',
                'product_url' => 'https://www.moutai.com.cn',
            ],
        ];

        foreach ($products as $productData) {
            Product::firstOrCreate(
                ['sku' => $productData['sku']],
                $productData
            );
        }

        $this->command->info('测试商品数据创建完成！');
    }
}
