<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\BannerController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\UploadController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\ProductController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// 认证相关路由
Route::prefix('auth')->group(function () {
    Route::post('/login', [AuthController::class, 'login']); // 登录
    Route::post('/logout', [AuthController::class, 'logout'])->middleware('auth:sanctum'); // 登出
    Route::post('/refresh', [AuthController::class, 'refresh'])->middleware('auth:sanctum'); // 刷新token
});

// 用户相关路由
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/user', function (Request $request) {
    return $request->user();
    });
    Route::get('/user/info', [AuthController::class, 'getUserInfo']); // 获取用户信息
});

// 文件上传路由（需要认证）
Route::middleware('auth:sanctum')->prefix('upload')->group(function () {
    Route::post('/image', [UploadController::class, 'uploadImage']); // 上传图片
    Route::delete('/image', [UploadController::class, 'deleteImage']); // 删除图片
});

// 轮播图相关API路由
Route::prefix('banners')->group(function () {
    Route::get('/', [BannerController::class, 'index']); // 获取轮播图列表
    Route::get('/stats', [BannerController::class, 'stats']); // 获取轮播图统计
    Route::get('/{id}', [BannerController::class, 'show']); // 获取单个轮播图详情
});

// 管理员轮播图管理路由（需要认证）
Route::middleware('auth:sanctum')->prefix('admin/banners')->group(function () {
    Route::post('/', [BannerController::class, 'store']); // 创建轮播图
    Route::put('/{id}', [BannerController::class, 'update']); // 更新轮播图
    Route::delete('/{id}', [BannerController::class, 'destroy']); // 删除轮播图
    Route::put('/{id}/toggle', [BannerController::class, 'toggle']); // 切换轮播图状态
});

// 商品相关API路由（公开访问）
Route::prefix('products')->group(function () {
    Route::get('/test', [ProductController::class, 'test']); // 测试接口
    Route::get('/stats', [ProductController::class, 'stats']); // 获取商品统计
    Route::get('/options', [ProductController::class, 'options']); // 获取商品选项数据
    Route::get('/{id}', [ProductController::class, 'show']); // 获取单个商品详情
});

// 管理员商品管理路由（需要认证和管理员权限）
Route::middleware('auth:sanctum')->prefix('admin/products')->group(function () {
    Route::get('/', [ProductController::class, 'index']); // 获取商品列表
    Route::post('/', [ProductController::class, 'store']); // 创建商品
    Route::put('/{id}', [ProductController::class, 'update']); // 更新商品
    Route::delete('/{id}', [ProductController::class, 'destroy']); // 删除商品
    Route::put('/{id}/toggle', [ProductController::class, 'toggle']); // 切换商品状态（上架/下架）
    Route::post('/batch', [ProductController::class, 'batch']); // 批量操作商品
    Route::get('/stats', [ProductController::class, 'stats']); // 获取商品统计
    Route::get('/options', [ProductController::class, 'options']); // 获取商品选项数据
});

// 用户管理路由（需要认证和管理员权限）
Route::middleware('auth:sanctum')->prefix('user')->group(function () {
    // 用户列表管理
    Route::get('/list', [UserController::class, 'index']); // 获取用户列表
    Route::post('/list', [UserController::class, 'store']); // 创建新用户
    Route::put('/list/{id}', [UserController::class, 'update']); // 更新用户
    Route::delete('/list/{id}', [UserController::class, 'destroy']); // 删除用户
    Route::put('/list/{id}/toggle', [UserController::class, 'toggleStatus']); // 切换用户状态
    
    // 用户组管理
    Route::get('/groups', [UserController::class, 'getGroups']); // 获取用户组列表
    
    // 用户权限管理
    Route::get('/permissions', [UserController::class, 'getPermissions']); // 获取用户权限列表
    
    // 用户日志
    Route::get('/logs', [UserController::class, 'getLogs']); // 获取用户日志
    
    // 系统设置
    Route::get('/settings', [UserController::class, 'getSettings']); // 获取系统设置
    Route::post('/settings', [UserController::class, 'updateSettings']); // 更新系统设置
});
