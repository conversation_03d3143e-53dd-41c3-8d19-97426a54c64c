<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        // 设置MySQL默认字符串长度，解决索引长度问题
        // Schema::defaultStringLength(191);
        
        // 避免在启动时连接数据库
        try {
            Schema::defaultStringLength(191);
        } catch (\Exception $e) {
            // 忽略数据库连接错误，允许应用启动
        }
    }
}
