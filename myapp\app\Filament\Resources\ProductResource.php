<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductResource\Pages;
use App\Filament\Resources\ProductResource\RelationManagers;
use App\Models\Product;
use App\Models\User;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'heroicon-o-shopping-bag';

    protected static ?string $navigationLabel = '商品管理';

    protected static ?string $modelLabel = '商品';

    protected static ?string $pluralModelLabel = '商品';

    protected static ?string $navigationGroup = '商品监控';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('基本信息')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('商品名称')
                            ->required()
                            ->maxLength(255),
                        
                        Forms\Components\Textarea::make('description')
                            ->label('商品描述')
                            ->maxLength(1000)
                            ->rows(3),
                        
                        Forms\Components\TextInput::make('product_url')
                            ->label('商品链接')
                            ->url()
                            ->maxLength(500),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('价格信息')
                    ->schema([
                        Forms\Components\TextInput::make('price')
                            ->label('当前价格')
                            ->numeric()
                            ->step(0.01)
                            ->prefix('¥'),
                        
                        Forms\Components\TextInput::make('original_price')
                            ->label('原价')
                            ->numeric()
                            ->step(0.01)
                            ->prefix('¥'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('库存信息')
                    ->schema([
                        Forms\Components\Select::make('stock_status')
                            ->label('库存状态')
                            ->options([
                                'in_stock' => '有库存',
                                'out_of_stock' => '缺货',
                                'limited_stock' => '库存紧张',
                                'unknown' => '未知',
                            ])
                            ->default('unknown'),
                        
                        Forms\Components\TextInput::make('stock_quantity')
                            ->label('库存数量')
                            ->numeric()
                            ->min(0),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('监控设置')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->label('启用监控')
                            ->default(true),
                    ])
                    ->columns(1),

                Forms\Components\Section::make('其他信息')
                    ->schema([
                        Forms\Components\DateTimePicker::make('last_updated_at')
                            ->label('最后更新时间'),
                        
                        Forms\Components\TextInput::make('image_url')
                            ->label('商品图片URL')
                            ->url()
                            ->maxLength(500),
                        
                        Forms\Components\TextInput::make('category')
                            ->label('商品分类')
                            ->maxLength(255),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('地区信息')
                    ->schema([
                        Forms\Components\Select::make('region_id')
                            ->label('所属地区')
                            ->relationship('region', 'name')
                            ->required()
                            ->searchable(),
                        
                        Forms\Components\TextInput::make('sku')
                            ->label('商品SKU')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),
                        
                        Forms\Components\TextInput::make('brand')
                            ->label('品牌')
                            ->maxLength(255),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('商品名称')
                    ->searchable()
                    ->sortable()
                    ->limit(30),
                
                Tables\Columns\TextColumn::make('sku')
                    ->label('商品SKU')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('price')
                    ->label('当前价格')
                    ->money('CNY')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('original_price')
                    ->label('原价')
                    ->money('CNY')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('brand')
                    ->label('品牌')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\BadgeColumn::make('stock_status')
                    ->label('库存状态')
                    ->colors([
                        'success' => 'in_stock',
                        'danger' => 'out_of_stock',
                        'secondary' => 'discontinued',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'in_stock' => '有库存',
                        'out_of_stock' => '缺货',
                        'discontinued' => '已停产',
                        default => $state,
                    }),
                
                Tables\Columns\IconColumn::make('is_active')
                    ->label('监控状态')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('添加时间')
                    ->dateTime('Y-m-d H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('监控状态')
                    ->placeholder('全部')
                    ->trueLabel('已启用')
                    ->falseLabel('已禁用'),
                
                Tables\Filters\SelectFilter::make('stock_status')
                    ->label('库存状态')
                    ->options([
                        'in_stock' => '有库存',
                        'out_of_stock' => '缺货',
                        'limited_stock' => '库存紧张',
                        'unknown' => '未知',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()->label('查看'),
                Tables\Actions\EditAction::make()->label('编辑'),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make()->label('批量删除'),
            ])
            ->defaultSort('created_at', 'desc');
    }
    
    public static function getRelations(): array
    {
        return [
            //
        ];
    }
    
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            'view' => Pages\ViewProduct::route('/{record}'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),
        ];
    }    
}
