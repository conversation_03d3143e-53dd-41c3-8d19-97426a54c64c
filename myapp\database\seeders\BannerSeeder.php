<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class BannerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $now = Carbon::now();
        
        $banners = [
            [
                'title' => '欢迎使用Art Design Pro',
                'image' => 'https://picsum.photos/1200/400?random=1',
                'link' => 'https://www.lingchen.kim/art-design-pro',
                'description' => 'Art Design Pro - 一个美观实用的Vue3管理后台模板',
                'is_active' => true,
                'sort_order' => 1,
                'target' => '_blank',
                'alt_text' => 'Art Design Pro Banner',
                'start_time' => null,
                'end_time' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'title' => '功能丰富的管理系统',
                'image' => 'https://picsum.photos/1200/400?random=2',
                'link' => '#',
                'description' => '包含用户管理、权限控制、数据统计等完整功能',
                'is_active' => true,
                'sort_order' => 2,
                'target' => '_self',
                'alt_text' => '功能介绍',
                'start_time' => null,
                'end_time' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'title' => '响应式设计',
                'image' => 'https://picsum.photos/1200/400?random=3',
                'link' => '#',
                'description' => '完美适配各种设备尺寸，提供最佳用户体验',
                'is_active' => true,
                'sort_order' => 3,
                'target' => '_self',
                'alt_text' => '响应式设计',
                'start_time' => null,
                'end_time' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'title' => '即将推出新功能',
                'image' => 'https://picsum.photos/1200/400?random=4',
                'link' => '#',
                'description' => '更多精彩功能正在开发中，敬请期待',
                'is_active' => false,
                'sort_order' => 4,
                'target' => '_self',
                'alt_text' => '新功能预告',
                'start_time' => $now->addDays(7),
                'end_time' => $now->addDays(30),
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ];

        DB::table('banners')->insert($banners);
    }
} 