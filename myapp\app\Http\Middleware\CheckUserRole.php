<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class CheckUserRole
{
    /**
     * 处理传入的请求，检查用户是否为前端用户
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // 检查用户是否已登录
        if (!Auth::check()) {
            Log::info('CheckUserRole: 用户未登录，跳转登录页面');
            return redirect()->route('login');
        }

        $user = Auth::user();
        
        // 检查用户角色是否为前端用户
        if ($user->role !== 'R_USER') {
            Log::info('CheckUserRole: 用户角色不符合前端用户要求，当前角色：' . $user->role);
            
            // 如果是管理员用户，重定向到后台管理系统
            $adminRoles = ['R_SUPER', 'R_ADMIN', 'R_EDITOR'];
            if (in_array($user->role, $adminRoles)) {
                return redirect('/admin')->with('info', '管理员用户请使用后台管理系统');
            }
            
            // 其他情况返回403错误
            abort(403, '您没有权限访问此页面');
        }

        Log::info('CheckUserRole: 用户角色验证通过，用户角色：' . $user->role);
        return $next($request);
    }
} 