<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Models\User;
use App\Models\Region;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Hash;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationLabel = '用户管理';

    protected static ?string $modelLabel = '用户';

    protected static ?string $pluralModelLabel = '用户';

    protected static ?string $navigationGroup = '用户管理';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('基本信息')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('用户名')
                            ->required()
                            ->maxLength(255),
                        
                        Forms\Components\TextInput::make('email')
                            ->label('邮箱')
                            ->email()
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),
                        
                        Forms\Components\TextInput::make('phone')
                            ->label('手机号')
                            ->tel()
                            ->maxLength(20),
                        
                        Forms\Components\Select::make('region_id')
                            ->label('所在地区')
                            ->relationship('region', 'name')
                            ->searchable(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('账户设置')
                    ->schema([
                        Forms\Components\TextInput::make('password')
                            ->label('密码')
                            ->password()
                            ->dehydrateStateUsing(fn ($state) => Hash::make($state))
                            ->dehydrated(fn ($state) => filled($state))
                            ->required(fn (string $context): bool => $context === 'create')
                            ->maxLength(255),
                        
                        Forms\Components\Select::make('status')
                            ->label('账户状态')
                            ->options([
                                'active' => '正常',
                                'inactive' => '禁用',
                                'pending' => '待激活',
                            ])
                            ->default('active')
                            ->required(),
                        
                        Forms\Components\Select::make('role')
                            ->label('用户角色')
                            ->options([
                                'R_SUPER' => '超级管理员',
                                'R_ADMIN' => '管理员',
                                'R_EDITOR' => '编辑者',
                                'R_USER' => '普通用户',
                            ])
                            ->default('R_USER')
                            ->required()
                            ->helperText('普通用户：前端订阅用户；管理员及以上：后台管理用户'),
                        
                        Forms\Components\Toggle::make('email_verified_at')
                            ->label('邮箱已验证')
                            ->dehydrateStateUsing(fn ($state) => $state ? now() : null)
                            ->mutateDehydratedStateUsing(fn ($state) => !is_null($state))
                            ->default(false),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('通知设置')
                    ->schema([
                        Forms\Components\Toggle::make('email_notifications')
                            ->label('邮件通知')
                            ->default(true),
                        
                        Forms\Components\Toggle::make('wechat_notifications')
                            ->label('微信通知')
                            ->default(false),
                        
                        Forms\Components\TextInput::make('wechat_id')
                            ->label('微信ID')
                            ->maxLength(255),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('其他信息')
                    ->schema([
                        Forms\Components\DateTimePicker::make('last_login_at')
                            ->label('最后登录时间'),
                        
                        Forms\Components\TextInput::make('timezone')
                            ->label('时区')
                            ->default('Asia/Shanghai')
                            ->maxLength(255),
                        
                        Forms\Components\TextInput::make('avatar')
                            ->label('头像URL')
                            ->url()
                            ->maxLength(255),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('用户名')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('email')
                    ->label('邮箱')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('phone')
                    ->label('手机号')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('region.name')
                    ->label('地区')
                    ->sortable(),
                
                Tables\Columns\BadgeColumn::make('role')
                    ->label('角色')
                    ->colors([
                        'danger' => 'R_SUPER',
                        'warning' => 'R_ADMIN',
                        'info' => 'R_EDITOR',
                        'success' => 'R_USER',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'R_SUPER' => '超级管理员',
                        'R_ADMIN' => '管理员',
                        'R_EDITOR' => '编辑者',
                        'R_USER' => '普通用户',
                        default => $state,
                    }),
                
                Tables\Columns\BadgeColumn::make('status')
                    ->label('状态')
                    ->colors([
                        'success' => 'active',
                        'danger' => 'inactive',
                        'warning' => 'pending',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'active' => '正常',
                        'inactive' => '禁用',
                        'pending' => '待激活',
                        default => $state,
                    }),
                
                Tables\Columns\IconColumn::make('email_verified_at')
                    ->label('邮箱验证')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger')
                    ->getStateUsing(fn ($record) => !is_null($record->email_verified_at)),
                
                Tables\Columns\TextColumn::make('subscriptions_count')
                    ->label('订阅数')
                    ->counts('subscriptions')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('followed_products_count')
                    ->label('关注商品')
                    ->counts('followedProducts')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('last_login_at')
                    ->label('最后登录')
                    ->dateTime('Y-m-d H:i')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('注册时间')
                    ->dateTime('Y-m-d H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('role')
                    ->label('用户角色')
                    ->options([
                        'R_SUPER' => '超级管理员',
                        'R_ADMIN' => '管理员',
                        'R_EDITOR' => '编辑者',
                        'R_USER' => '普通用户',
                    ])
                    ->placeholder('全部角色'),
                
                Tables\Filters\SelectFilter::make('status')
                    ->label('账户状态')
                    ->options([
                        'active' => '正常',
                        'inactive' => '禁用',
                        'pending' => '待激活',
                    ]),
                
                Tables\Filters\TernaryFilter::make('email_verified_at')
                    ->label('邮箱验证')
                    ->placeholder('全部')
                    ->trueLabel('已验证')
                    ->falseLabel('未验证')
                    ->queries(
                        true: fn ($query) => $query->whereNotNull('email_verified_at'),
                        false: fn ($query) => $query->whereNull('email_verified_at'),
                    ),
                
                Tables\Filters\SelectFilter::make('region_id')
                    ->label('地区')
                    ->relationship('region', 'name')
                    ->placeholder('全部'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()->label('查看'),
                Tables\Actions\EditAction::make()->label('编辑'),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make()->label('批量删除'),
            ])
            ->defaultSort('created_at', 'desc');
    }
    
    public static function getRelations(): array
    {
        return [
            //
        ];
    }
    
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'view' => Pages\ViewUser::route('/{record}'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }    
} 